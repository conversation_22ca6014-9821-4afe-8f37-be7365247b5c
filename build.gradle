buildscript {
    ext {
        requiredHttps = false
        requiredPmd = false
        cmpayVersion = '3.2.x'
        hutoolVersion = '5.8.30'
        versionMngUrl = "http://gitlab.devops.cmft/publicProject/lemon/snippets/34/raw"
    }
    apply from:resources.text.fromInsecureUri(versionMngUrl)
}

plugins {
    id 'com.cmpay.platform' version "${cmpayVersion}" apply false
    id 'io.freefair.maven-optional' version '6.0.0-m2' apply false
    id 'io.freefair.lombok' version '6.0.0-m2' apply false
    id 'org.sonarqube' version '2.7' apply false
}

allprojects {
    repositories {
        maven {
            url 'http://nexus.devops.cmft/repository/maven-public/'
            allowInsecureProtocol = true
        }
    }
}

subprojects {
    apply plugin: 'java-library'
    apply plugin: 'idea'
    apply plugin: 'com.cmpay.platform'
    apply plugin: 'io.freefair.lombok'
    apply plugin: 'io.freefair.maven-optional'
    apply plugin: 'jacoco'

    version = '1.0.4-SNAPSHOT'
    group = 'com.cmpay.high-availability'

    compileJava {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
        options.encoding = 'UTF-8'
    }

    dependencies {
        testImplementation('com.cmpay:lemon-framework-starter-test')
    }

    java {
        withSourcesJar()
    }

    jacoco {
        toolVersion = '0.8.10'
    }

    jacocoTestReport {
        reports {
            html.required=true
            xml.required=true
        }
    }

    test {
        useJUnitPlatform()
        finalizedBy jacocoTestReport
    }

}

configure(subprojects.findAll {it.name.matches('\\S*(-interface)\\S*')}) {
    apply plugin: 'maven-publish'
    publishing {
        publications {
            mavenJava(MavenPublication) {
                versionMapping {
                    usage('java-runtime') {
                        fromResolutionResult()
                    }
                }
                from components.java
            }
        }
        repositories {
//            maven {
//                allowInsecureProtocol = true
//                // 需在本地.gradle根目录下配置gradle.application，并将appMavenUserSnap、appMavenUserRelease、appMavenPasswordSnap、appMavenPasswordRelease配置进去
//                // 具体账号密码请找老同事获取
//                def releasesRepoUrl = 'http://nexus.devops.cmft/repository/cmpay-interface-releases/'
//                def snapshotsRepoUrl = 'http://nexus.devops.cmft/repository/cmpay-interface-snapshots/'
//                def nexusUserName = version.endsWith('SNAPSHOT') ? appMavenUserSnap : appMavenUserRelease
//                def nexusPassword = version.endsWith('SNAPSHOT') ? appMavenPasswordSnap : appMavenPasswordRelease
//                url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
//                    credentials {
//                        username nexusUserName
//                        password nexusPassword
//                    }
//            }
        }
    }
}

configure(subprojects.findAll {it.name.matches('\\S*(-rest)\\S*' )}) {
    dependencies {
        optional('io.swagger:swagger-annotations')
    }
}

description '高可用管控平台'
