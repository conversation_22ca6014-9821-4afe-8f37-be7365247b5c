def getApps() {
    return [
            'hacp-admin'   : [
                    appDir         : 'app/high-availability-control-platform-app',
                    releaseUserHost: 'ruut@***********',
                    releaseDir     : 'hacp-admin/',
                    userName       : 'hacp-admin',
                    'master'  : [hostName: '***********'],
            ]
    ]
}

def getChoicesApp() {
    def choiceSet = getApps().keySet().collect { key, value -> key }
    choiceSet.add('publishInterface')
    return choiceSet
}


pipeline {
    agent any
    options {
        timestamps()
    }

    parameters {
        choice(
                description: '请选择需要发布的应用!',
                name: 'deployApp',
                choices: getChoicesApp()
        )
        choice(
                description: '请选择需要发布的目标环境!',
                name: 'deployAppEnv',
                choices: ['master','release/3.1.x']
        )
        choice(
                description: '是否发布镜像!',
                name: 'requiredPushImage',
                choices: ['no', 'yes']
        )
        choice(
                description: '请选择你将要上传的镜像仓库!',
                name: 'repository',
                choices: ['*************', '**********', 'repos.cloud.test', 'repos.cloud.com']
        )
        string(
                description: '请输入应用所属项目!',
                name: 'group',
                defaultValue: 'hpaddle',
        )

        string(
                description: '请输入镜像仓库账号 !',
                name: 'repositoryUsername',
                defaultValue: 'admin',
        )

        password(
                description: '请输入镜像仓库密码 !',
                name: 'repositoryPassword',
                defaultValue: 'Harbor12345',
        )

        string(
                description: '请输入基础镜像版本 !',
                name: 'hpaddleBaseVersion',
                defaultValue: '1.0.3',
        )

        string(
                description: '请输入镜像版本 !',
                name: 'imageVersion',
                defaultValue: '2.2.11',
        )
    }

    stages {
        stage('Interface publish') {
            when {
                anyOf {
                    expression { params.deployApp == 'publishInterface' }
                    changeset pattern: "^(interface|constant|common).*", comparator: "REGEXP";
                    changeset pattern: ".*(interface|constant|common).*", comparator: "REGEXP"
                }
            }
            steps {
                script {
                    sh "chmod +x ./gradlew"
                    sh './gradlew clean assemble --refresh-dependencies publish'
                }
            }
        }

        stage('Deploy for project') {
            when {
                expression { return params.deployAppEnv != 'release/pre' }
                expression { return !getApps().isEmpty() && getApps().keySet().any { appName -> appName == params.deployApp } }
            }
            options {
                timeout(time: 10, unit: 'MINUTES')
            }
            steps {
                script {
                    getApps().each { appName, app ->
                        if (appName == params.deployApp) {
                            def appDir = app['appDir'], userName = app['userName'], gradleApp = appDir.replaceAll("/", ":")
                            sh "chmod +x ./gradlew"
                            sh "./gradlew clean $gradleApp:bootJar --refresh-dependencies"
                            app[params.deployAppEnv]['hostName'].split("\\|").each { hostName ->
                                sh "ssh $userName@$hostName 'rm -rf ~/lib.last; mv ~/lib ~/lib.last; :'"
                                sh "rsync -av --exclude='*-sources.jar' ./$appDir/build/libs/*.jar $userName@$hostName:~/lib/"
                                sh "ssh $userName@$hostName 'source .bash_profile; sh bin/start.sh'"
                                println "Success deploy $params.deployAppEnv to $userName@$hostName."
                            }

                        }
                    }
                }
            }
        }

        stage('PRD Release') {
            when {
                anyOf { branch 'release/pre'; branch 'release/3.0.x' }
                expression { return !getApps().isEmpty() && getApps().keySet().any { appName -> appName == params.deployApp } }
            }
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                script {
                    sh "./gradlew clean bootJar --refresh-dependencies"
                    getApps().each { appName, app ->
                        def appDir = app['appDir'], gradleApp = appDir.replaceAll("/", ":")
                        def releaseUserHost = app['releaseUserHost'], releaseDir = app['releaseDir']
                        if (releaseUserHost) {
                            sh "rsync -av --exclude='*-sources.jar' ./$appDir/build/libs/*.jar $releaseUserHost:~/$releaseDir/"
                            println "Success deploy $appName to $releaseUserHost"
                        }
                    }
                }
            }
        }

        // 构建Docker镜像
        stage('DockerPublish') {
            when {
                expression { return "yes" == params.requiredPushImage }
            }
            options {
                timeout(time: 5, unit: 'MINUTES')
            }
            steps {
                script {
                    def image = "${params.deployApp}:${params.imageVersion}"
                    def imageTag = "${params.repository}/${params.group}/${image}"
                    echo "repository ${params.repository}"
                    sh "docker login -p ${params.repositoryPassword} -u ${params.repositoryUsername} ${params.repository}"
                    getApps().each { appName, app ->
                        if (appName == params.deployApp && appName != 'publishUI') {
                            def userName = app['userName']
                            def appDir = app['appDir']
                            sh "docker build --target app --build-arg ARG_USER=${userName}  --build-arg ARG_HPADDLEBASE_VERSION=${params.hpaddleBaseVersion}  --build-arg ARG_VERSION=${params.imageVersion} --build-arg ARG_APPLICATION=${params.deployApp} --build-arg ARG_REPOSITORY=${params.repository} --build-arg ARG_APPLICATION_DIR=${appDir} --no-cache -t $image ."
                        }
                    }
                    if ('publishUI' == params.deployApp) {
                        image = "gw-adm-ui:${params.imageVersion}"
                        imageTag = "${params.repository}/${params.group}/${image}"
                        sh "docker build --target frontend --build-arg ARG_APPLICATION=ui-frontend --build-arg ARG_REPOSITORY=${params.repository} --no-cache -t $image ."
                    }
                    sh "docker tag $image $imageTag"
                    sh "docker push $imageTag"
                }
            }
        }
    }
}
