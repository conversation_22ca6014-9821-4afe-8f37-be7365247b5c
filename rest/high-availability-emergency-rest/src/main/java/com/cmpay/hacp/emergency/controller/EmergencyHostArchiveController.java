package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.emergency.bo.EmergencyHostArchiveBO;
import com.cmpay.hacp.dto.emergency.EmergencyHostArchiveReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyHostArchiveRspDTO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.emergency.service.EmergencyHostArchiveService;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应急机器控制器
 *
 * <AUTHOR>
 * @create 2024/08/21 14:02:05
 * @since 1.0.0
 */

@RestController
@Api(tags = "应急调度主机管理")
@RequestMapping("/v1/emergency-host-archive")
@RequiredArgsConstructor
@Slf4j
public class EmergencyHostArchiveController {

    /**
     * 机器历史服务
     */
    private final EmergencyHostArchiveService emergencyHostArchiveService;


    /**
     * 查询列表
     *
     * @return 查询列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    public DefaultRspDTO<List<EmergencyHostArchiveRspDTO>> getList(@RequestBody EmergencyHostArchiveReqDTO reqDTO) {
        List<EmergencyHostArchiveBO> list = emergencyHostArchiveService.findByBusinessKeyAndTaskId(reqDTO.getBusinessKey(),reqDTO.getActivityId(),
                TenantUtils.getWorkspaceIdNotNull(),reqDTO.getTaskId());
        return DefaultRspDTO.newSuccessInstance(BeanConvertUtil.convertList(list,EmergencyHostArchiveRspDTO.class));
    }

 }
