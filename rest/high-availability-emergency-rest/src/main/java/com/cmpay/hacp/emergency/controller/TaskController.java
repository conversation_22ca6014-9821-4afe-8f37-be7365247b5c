package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.dto.emergency.HacpEmergencyTaskReqDto;
import com.cmpay.hacp.dto.emergency.HacpEmergencyTaskRspDto;
import com.cmpay.hacp.enums.KeyValue;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.emergency.service.HacpTaskService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/emergency/task")
@Api(tags = "任务管理")
public class TaskController {

    @Autowired
    private HacpTaskService hacpTaskService;

    @ApiOperation(value = "新增任务", notes = "新增任务")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/add")
    @LogRecord(title = "新增任务", action = "新增")
    @PreAuthorize("hasPermission('TaskController','emergency:task:add')")
    public DefaultRspDTO<NoBody> addTask(@Validated @RequestBody HacpEmergencyTaskReqDto taskReqDto) {
        HacpEmergencyTaskBO taskBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskBO(), taskReqDto);
        TenantSecurityUtils.copyTenantSecurity(taskBO);
        hacpTaskService.addTask(taskBO, TenantUtils.getWorkspaceIdNotNull());
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询任务列表", notes = "新增任务")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-task-list")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TaskController','emergency:task:query')")
    public DefaultRspDTO<PageInfo<HacpEmergencyTaskRspDto>> queryTaskList(@Validated @RequestBody HacpEmergencyTaskReqDto taskReqDto) {
        HacpEmergencyTaskBO taskBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskBO(), taskReqDto);
        taskBO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        PageInfo<HacpEmergencyTaskBO> boPageInfo = hacpTaskService.queryTaskList(taskBO, taskReqDto.getPageNum(), taskReqDto.getPageSize());
        List<HacpEmergencyTaskRspDto> collect = boPageInfo.getList().stream().map(
                bo -> BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskRspDto(), bo)).collect(Collectors.toList());
        PageInfo<HacpEmergencyTaskRspDto> pageInfo = new PageInfo<>(collect);
        pageInfo.setTotal(boPageInfo.getTotal());
        pageInfo.setHasNextPage(boPageInfo.isHasNextPage());
        pageInfo.setPages(boPageInfo.getPages());
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "查询任务详情", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get")
    @LogRecord(title = "查询任务详情", action = "查询")
    @PreAuthorize("hasPermission('TaskController','emergency:task:query')")
    public DefaultRspDTO<HacpEmergencyTaskRspDto> getTask(@Validated @RequestBody HacpEmergencyTaskReqDto taskReqDto) {
        HacpEmergencyTaskBO taskInfo = hacpTaskService.getTaskInfo(taskReqDto.getId(), false,true);
        AbstractTaskStrategyFactory instance = AbstractTaskStrategyFactory.newInstance(taskInfo.getTaskType());
        taskInfo.setTaskParamJson(instance.toTaskParam(taskInfo.getTaskParam()));
        return DefaultRspDTO.newSuccessInstance(BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskRspDto(), taskInfo));
    }

    @ApiOperation(value = "修改任务", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/update")
    @LogRecord(title = "修改任务", action = "修改")
    @PreAuthorize("hasPermission('TaskController','emergency:task:update')")
    public DefaultRspDTO<NoBody> updateTask(@Validated @RequestBody HacpEmergencyTaskReqDto taskReqDto) {
        HacpEmergencyTaskBO taskBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskBO(), taskReqDto);
        TenantSecurityUtils.copyTenantSecurity(taskBO);
        hacpTaskService.updateTask(taskBO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除任务", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/delete")
    @LogRecord(title = "删除任务", action = "删除")
    @PreAuthorize("hasPermission('TaskController','emergency:task:delete')")
    public DefaultRspDTO<NoBody> deleteTask(@Validated @RequestBody HacpEmergencyTaskReqDto taskReqDto) {
        HacpEmergencyTaskBO taskBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskBO(), taskReqDto);
        TenantSecurityUtils.copyTenantSecurity(taskBO);
        hacpTaskService.deleteTask(taskBO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "获取任务界面的下拉选项", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @GetMapping("/drop-down")
    @LogNoneRecord
    public DefaultRspDTO<Map<String,List<KeyValue>>> getDropDown(){
        return DefaultRspDTO.newSuccessInstance(hacpTaskService.getDropDown());
    }
}
