package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.emergency.bo.EmergencyProcessBO;
import com.cmpay.hacp.emergency.bo.process.EmergencyProcessQueryDTO;
import com.cmpay.hacp.emergency.bo.process.HistoricProcessInstanceExtDto;
import com.cmpay.hacp.emergency.bo.process.TaskExecuteLogPage;
import com.cmpay.hacp.dto.emergency.ProcessTaskCompleteReqDto;
import com.cmpay.hacp.dto.emergency.ProcessTaskLogDto;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.camunda.ProcessInstanceService;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.rest.dto.runtime.ProcessInstanceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 11:38
 * @version 1.0
 */
@RestController
@RequestMapping("/v1/emergency/process")
@Api(tags = "流程管理")
public class ProcessController {
    @Autowired
    private ProcessInstanceService processInstanceService;
    @Autowired
    private EmergencyProcessService emergencyProcessService;
    @ApiOperation(value = "", notes = "查询流程实例列表")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-process-list")
    @LogNoneRecord
    @PreAuthorize("hasPermission('ProcessController','emergency:process:query')")
    public DefaultRspDTO<PageInfo<EmergencyProcessBO>> queryProcessList(
            @Validated @RequestBody EmergencyProcessQueryDTO reqDto) {
        EmergencyProcessBO query = BeanConvertUtil.convert(reqDto, EmergencyProcessBO.class);
        TenantSecurityUtils.copyWorkspace(query);
        if(JudgeUtils.isNotNull(reqDto.getStartedAfter())&&JudgeUtils.isNotNull(reqDto.getStartedAfter())){
            query.setLogPeriods(new Long[]{Long.valueOf(reqDto.getStartedAfter()), Long.valueOf(reqDto.getStartedAfter())});
        }
        PageInfo<EmergencyProcessBO> page = emergencyProcessService.getPage(reqDto.getPageNum(), reqDto.getPageSize(), query);
        return DefaultRspDTO.newSuccessInstance(page);
    }

    @ApiOperation(value = "", notes = "查询流程实例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get")
    @LogNoneRecord
    @PreAuthorize("hasPermission('ProcessController','emergency:process:query')")
    public DefaultRspDTO<HistoricProcessInstanceExtDto> getProcessInfo(@Validated @RequestBody ProcessInstanceDto processInstanceDto) {
        HistoricProcessInstanceExtDto processInstanceExtDto = getHistoricProcessInstanceExtDto(processInstanceDto);

        return DefaultRspDTO.newSuccessInstance(processInstanceExtDto);
    }

    @ApiOperation(value = "", notes = "查询审批流程实例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get-approval")
    @LogRecord(title = "查询审批流程实例", action = "查询")
    @PreAuthorize("hasPermission('ProcessController','emergency:process:dispose')")
    public DefaultRspDTO<HistoricProcessInstanceExtDto> getApprovalProcessInfo(@Validated @RequestBody ProcessInstanceDto processInstanceDto) {
        HistoricProcessInstanceExtDto processInstanceExtDto = getHistoricProcessInstanceExtDto(processInstanceDto);
        if(!processInstanceExtDto.getState().equals(HistoricProcessInstance.STATE_ACTIVE)){
            BusinessException.throwBusinessException(MsgEnum.THE_PROCESS_IS_AT_A_STAGE_THAT_DOES_NOT_MEET);
        }
        return DefaultRspDTO.newSuccessInstance(processInstanceExtDto);
    }

    @ApiOperation(value = "", notes = "查询重试流程实例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get-retry")
    @LogRecord(title = "查询重试流程实例", action = "查询")
    @PreAuthorize("hasPermission('ProcessController','emergency:process:dispose')")
    public DefaultRspDTO<HistoricProcessInstanceExtDto> getRetryProcessInfo(@Validated @RequestBody ProcessInstanceDto processInstanceDto) {
        HistoricProcessInstanceExtDto processInstanceExtDto = getHistoricProcessInstanceExtDto(processInstanceDto);
        if(!processInstanceExtDto.getState().equals(HistoricProcessInstance.STATE_INTERNALLY_TERMINATED)){
            BusinessException.throwBusinessException(MsgEnum.THE_PROCESS_IS_AT_A_STAGE_THAT_DOES_NOT_MEET);
        }
        return DefaultRspDTO.newSuccessInstance(processInstanceExtDto);
    }

    private HistoricProcessInstanceExtDto getHistoricProcessInstanceExtDto(ProcessInstanceDto processInstanceDto) {
        HistoricProcessInstanceExtDto processInstanceExtDto = processInstanceService.getProcessInstanceById(processInstanceDto.getBusinessKey());
        processInstanceService.setRuntimeVariable(processInstanceExtDto);
        processInstanceService.computeNodeStatus(processInstanceExtDto);

        processInstanceExtDto.setAssignee(processInstanceService.isAssigneeTask(processInstanceExtDto.getBusinessKey(), SecurityUtils.getLoginUserId()));
        return processInstanceExtDto;
    }


    @ApiOperation(value = "", notes = "完成任务")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/complete-task")
    @LogRecord(title = "完成任务", action = "修改")
    @PreAuthorize("hasPermission('ProcessController','emergency:process:dispose')")
    public DefaultRspDTO<String> completeTask(@Validated @RequestBody ProcessTaskCompleteReqDto reqDto) {
        String restartProcessInstanceId = processInstanceService.completeTask(reqDto.getBusinessKey(), reqDto.getResult()
                , reqDto.getComment(), reqDto.getVariables(),true);
        return DefaultRspDTO.newSuccessInstance(restartProcessInstanceId);
    }

    @ApiOperation(value = "", notes = "重试")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/retry-task")
    @LogRecord(title = "重试", action = "修改")
    @PreAuthorize("hasPermission('ProcessController','emergency:process:dispose')")
    public DefaultRspDTO<String> retryTask(@Validated @RequestBody ProcessTaskLogDto reqDto) {
        String restartProcessInstanceId = processInstanceService.retryTask(reqDto.getBusinessKey(),reqDto.getActivityId(),
                reqDto.getSkip());
        return DefaultRspDTO.newSuccessInstance(restartProcessInstanceId);
    }

    @ApiOperation(value = "", notes = "删除流程实例")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除流程实例", action = "删除")
    @PostMapping("/delete-process-instance")
    @PreAuthorize("hasPermission('ProcessController','emergency:process:delete')")
    public DefaultRspDTO<NoBody> deleteProcessInstance(@Validated @RequestBody ProcessInstanceDto reqDto) {
        EmergencyProcessBO bo = new EmergencyProcessBO();
        TenantSecurityUtils.copyWorkspace(bo);
        bo.setBusinessKey(reqDto.getBusinessKey());
        emergencyProcessService.delete(bo);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "", notes = "查询任务执行日志")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get-task-execute-log")
    //@PreAuthorize("hasPermission('ProcessController','emergency:process:dispose')")
    public DefaultRspDTO<TaskExecuteLogPage> getTaskExecuteLog(@Validated @RequestBody ProcessTaskLogDto taskDto) {
        return DefaultRspDTO.newSuccessInstance(processInstanceService.getTaskLog(taskDto.getBusinessKey()
                , taskDto.getActivityId(), taskDto.getIndex()));
    }
}
