
package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.emergency.bo.EmergencyTagBO;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.dto.emergency.EmergencyTagAddReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyTagUpdateReqDTO;
import com.cmpay.hacp.enums.EntityTypeEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.emergency.service.EmergencyTagService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急机器控制器
 *
 * <AUTHOR>
 * @create 2024/08/21 14:02:05
 * @since 1.0.0
 */

@RestController
@Api(tags = "应急调度标签管理")
@RequestMapping("/v1/emergency-host-tag")
@RequiredArgsConstructor
@Slf4j
public class EmergencyHostTagController {

    /**
     * 标签服务
     */
    private final EmergencyTagService emergencyTagService;


    /**
     * 新增
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增主机标签", action = "新增")
    @PreAuthorize("hasPermission('EmergencyTagController','emergency:tag:add')")
    public DefaultRspDTO<EmergencyTagBO> add(@Validated @RequestBody EmergencyTagAddReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        EmergencyTagBO convert = BeanConvertUtil.convert(reqDTO, EmergencyTagBO.class);
        convert.setEntityType(EntityTypeEnum.HOST);
        EmergencyTagBO result = emergencyTagService.add(convert);
        return DefaultRspDTO.newSuccessInstance(result);
    }


    /**
     * 修改
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改主机标签", action = "修改")
    @PreAuthorize("hasPermission('EmergencyTagController','emergency:tag:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody EmergencyTagUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        EmergencyTagBO convert = BeanConvertUtil.convert(reqDTO, EmergencyTagBO.class);
        convert.setEntityType(EntityTypeEnum.HOST);
        emergencyTagService.update(convert);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除主机标签", action = "删除")
    @PreAuthorize("hasPermission('EmergencyTagController','emergency:tag:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody EmergencyTagUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        EmergencyTagBO convert = BeanConvertUtil.convert(reqDTO, EmergencyTagBO.class);
        convert.setEntityType(EntityTypeEnum.HOST);
        emergencyTagService.delete(convert);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询列表
     *
     * @return 查询列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    public DefaultRspDTO<List<EmergencyTagBO>> getList() {
        EmergencyTagBO emergencyHostTagBO = new EmergencyTagBO();
        TenantSecurityUtils.copyWorkspace(emergencyHostTagBO);
        emergencyHostTagBO.setEntityType(EntityTypeEnum.HOST);
        List<EmergencyTagBO> list = emergencyTagService.getList(emergencyHostTagBO);
        return DefaultRspDTO.newSuccessInstance(list);
    }

 }
