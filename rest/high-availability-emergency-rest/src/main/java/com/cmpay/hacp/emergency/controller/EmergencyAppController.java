
package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.emergency.bo.EmergencyAppBO;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.dto.emergency.EmergencyAppAddReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyAppUpdateReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyHostUpdateReqDTO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.emergency.service.EmergencyAppService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急机器控制器
 *
 * <AUTHOR>
 * @create 2024/08/21 14:02:05
 * @since 1.0.0
 */

@RestController
@Api(tags = "应急调度应用管理")
@RequestMapping("/v1/emergency-app")
@RequiredArgsConstructor
@Slf4j
public class EmergencyAppController {

    /**
     * 应用
     */
    private final EmergencyAppService emergencyAppService;


    /**
     * 新增
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增应用名", action = "新增")
    @PreAuthorize("hasPermission('EmergencyAppController','emergency:app:add')")
    public DefaultRspDTO<EmergencyAppBO> add(@Validated @RequestBody EmergencyAppAddReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        EmergencyAppBO result = emergencyAppService.add(BeanConvertUtil.convert(reqDTO, EmergencyAppBO.class));
        return DefaultRspDTO.newSuccessInstance(result);
    }


    /**
     * 修改
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改应用名", action = "修改")
    @PreAuthorize("hasPermission('EmergencyAppController','emergency:app:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody EmergencyHostUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        emergencyAppService.update(BeanConvertUtil.convert(reqDTO, EmergencyAppBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除应用名", action = "删除")
    @PreAuthorize("hasPermission('EmergencyAppController','emergency:app:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody EmergencyAppUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        emergencyAppService.delete(BeanConvertUtil.convert(reqDTO, EmergencyAppBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @ApiResponse(code = 200, message = "详情")
    @LogRecord(title = "查询应用名详情", action = "查询")
    public DefaultRspDTO<EmergencyAppBO> getInfo(EmergencyAppUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        EmergencyAppBO result = emergencyAppService.getDetailInfo(BeanConvertUtil.convert(reqDTO, EmergencyAppBO.class));
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 查询列表
     *
     * @return 查询列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    public DefaultRspDTO<List<EmergencyAppBO>> getList() {
        EmergencyAppBO emergencyHostTagBO = new EmergencyAppBO();
        TenantSecurityUtils.copyWorkspace(emergencyHostTagBO);
        List<EmergencyAppBO> list = emergencyAppService.getList(emergencyHostTagBO);
        return DefaultRspDTO.newSuccessInstance(list);
    }

 }
