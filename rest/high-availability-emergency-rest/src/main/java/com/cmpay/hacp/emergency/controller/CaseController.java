package com.cmpay.hacp.emergency.controller;

import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.emergency.bo.HacpEmergencyCaseBO;
import com.cmpay.hacp.emergency.bo.HacpProcessDetailBO;
import com.cmpay.hacp.dto.emergency.*;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.emergency.service.HacpCaseService;
import com.cmpay.hacp.emergency.service.camunda.ProcessDefinitionService;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDiagramDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@RestController
@RequestMapping("/v1/emergency/case")
@Api(tags = "案例管理")
@Slf4j
public class CaseController {

    @Autowired
    private HacpCaseService caseService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @ApiOperation(value = "新增案例", notes = "新增案例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/add")
    @LogRecord(title = "新增案例",action = "新增")
    @PreAuthorize("hasPermission('CaseController','emergency:case:add')")
    public DefaultRspDTO<NoBody> addCase(@Validated @RequestParam("caseName") String caseName, @RequestParam("caseDescribe") String caseDescribe,
            @RequestParam(value = "tagIds[]",required = false)List<Integer> tagIds, MultipartFile bpmnFile) {
        HacpEmergencyCaseBO caseBO = new HacpEmergencyCaseBO();
        caseBO.setCaseName(caseName);
        caseBO.setCaseDescribe(caseDescribe);
        caseBO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        caseBO.setTagIds(tagIds);
        caseService.addCase(caseBO, bpmnFile);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询案例列表", notes = "查询案例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/query-case-list")
    @LogNoneRecord
    @PreAuthorize("hasPermission('CaseController','emergency:case:query')")
    public DefaultRspDTO<PageInfo<HacpEmergencyCaseRspDto>> queryCaseList(@Validated @RequestBody HacpEmergencyCaseReqDto taskReqDto) {
        HacpEmergencyCaseBO caseBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyCaseBO(), taskReqDto);
        caseBO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        PageInfo<HacpEmergencyCaseBO> casePageInfo = caseService.queryCaseList(caseBO, taskReqDto.getPageNum(), taskReqDto.getPageSize());
        List<HacpEmergencyCaseRspDto> collect = casePageInfo.getList().stream().map(
                bo -> BeanUtils.copyPropertiesReturnDest(new HacpEmergencyCaseRspDto(), bo)).collect(Collectors.toList());
        PageInfo<HacpEmergencyCaseRspDto> pageInfo = new PageInfo<>(collect);
        pageInfo.setTotal(casePageInfo.getTotal());
        pageInfo.setHasNextPage(casePageInfo.isHasNextPage());
        pageInfo.setPages(casePageInfo.getPages());
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "查询案例详情", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get")
    @LogRecord(title = "查询案例详情",action = "查询")
    @PreAuthorize("hasPermission('CaseController','emergency:case:query')")
    public DefaultRspDTO<HacpEmergencyCaseRspDto> getCase(@Validated @RequestBody HacpEmergencyCaseReqDto caseReqDto) {
        HacpEmergencyCaseBO caseInfo = caseService.getCaseInfo(caseReqDto.getId(), false);
        if (JudgeUtils.isNotBlank(caseInfo.getCaseDeployId())) {
            ProcessDefinitionDiagramDto processDefinitionDiagramDto
                    = processDefinitionService.getDefinitionXmlByDeployId(caseInfo.getCaseDeployId());
            //查询xml
            caseInfo.setProcessDefinitionXml(processDefinitionDiagramDto.getBpmn20Xml());
        }
        return DefaultRspDTO.newSuccessInstance(BeanUtils.copyPropertiesReturnDest(new HacpEmergencyCaseRspDto(), caseInfo));
    }

    @ApiOperation(value = "查询案例详情", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get-bpmn-case")
    @LogNoneRecord
    @PreAuthorize("hasPermission('CaseController','emergency:case:query')")
    public DefaultRspDTO<HacpEmergencyCaseRspDto> getBpmnCase(@Validated @RequestBody HacpEmergencyCaseReqDto caseReqDto) {
        HacpEmergencyCaseBO caseInfo = caseService.getCaseInfo(caseReqDto.getId(), false);
        HacpEmergencyCaseRspDto emergencyCaseRspDto = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyCaseRspDto(), caseInfo);
        if (JudgeUtils.isNotBlank(caseInfo.getCaseDeployId())) {
            ProcessDefinitionDiagramDto processDefinitionDiagramDto
                    = processDefinitionService.getDefinitionXmlByDeployId(caseInfo.getCaseDeployId());
            //查询xml
            emergencyCaseRspDto.setProcessDefinitionXml(processDefinitionDiagramDto.getBpmn20Xml());
            HacpProcessDetailBO caseProcessDetail = caseService.getCaseProcessDetail(emergencyCaseRspDto.getProcessDefinitionXml());
            emergencyCaseRspDto.setVariableList(convertTaskVariables(caseProcessDetail.getVariableList()));
            HacpProcessDetailDto hacpProcessDetailDto = BeanUtils.copyPropertiesReturnDest(new HacpProcessDetailDto(), caseProcessDetail);
            hacpProcessDetailDto.setConfirmTaskNum(caseProcessDetail.getConfirmTaskNum());
            hacpProcessDetailDto.setTaskIds(caseProcessDetail.getTaskIds());
            emergencyCaseRspDto.setProcessDetail(hacpProcessDetailDto);
        }
        return DefaultRspDTO.newSuccessInstance(emergencyCaseRspDto);
    }

    @ApiOperation(value = "查询案例参数变量", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/get-case-variables")
    @LogRecord(title = "查询案例参数变量",action = "查询")
    public DefaultRspDTO<List<HacpCaseVariableRspDto>> getCaseVariables(@Validated @RequestBody HacpEmergencyCaseReqDto caseReqDto) {
        List<HacpCaseVariableBO> caseVariables = caseService.getDynamicCaseVariables(caseReqDto.getId(),true);
        return DefaultRspDTO.newSuccessInstance(convertTaskVariables(caseVariables));
    }

    @ApiOperation(value = "修改案例", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/update")
    @LogRecord(title = "修改案例",action = "查询")
    @PreAuthorize("hasPermission('CaseController','emergency:case:update')")
    public DefaultRspDTO<NoBody> updateCase(@Validated @RequestParam("caseName") String caseName, @RequestParam("caseDescribe") String caseDescribe,
            @RequestParam(value = "tagIds[]",required = false) List<Integer> tagIds, MultipartFile bpmnFile, @Validated @RequestParam("id") Long id) {
        HacpEmergencyCaseBO caseBO = new HacpEmergencyCaseBO();
        caseBO.setCaseName(caseName);
        caseBO.setCaseDescribe(caseDescribe);
        caseBO.setId(id);
        caseBO.setTagIds(tagIds);
        caseBO.setWorkspaceId(TenantUtils.getWorkspaceIdNotNull());
        caseService.updateCase(caseBO, bpmnFile);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除案例", notes = "")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/delete")
    @LogRecord(title = "删除案例",action = "查询")
    @PreAuthorize("hasPermission('CaseController','emergency:case:delete')")
    public DefaultRspDTO<NoBody> deleteTask(@Validated @RequestBody HacpEmergencyCaseReqDto caseReqDto) {
        caseService.deleteCase(caseReqDto.getId());
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "", notes = "启动流程案例")
    @ApiResponse(code = 200, message = "返回结果")
    @PostMapping("/start-case-process")
    @LogRecord(title = "启动流程案例", action = "新增")
    @PreAuthorize("hasPermission('CaseController','emergency:case:start')")
    public DefaultRspDTO<StartRspDTO> startProcessInstance(@Validated @RequestBody HacpEmergencyCaseReqDto caseReqDto) {
        // 将变量转换成map
        Map<String,HacpCaseVariableBO> variables = Optional.ofNullable(caseReqDto.getTaskVariables())
                .orElse(new ArrayList<>())
                .stream()
                .map(x ->
                {
                    HacpCaseVariableBO hacpCaseVariableBO = new HacpCaseVariableBO();
                    hacpCaseVariableBO.setActivityId(x.getActivityId());
                    hacpCaseVariableBO.setTaskId(x.getTaskId());
                    hacpCaseVariableBO.setTaskName(x.getTaskName());
                    hacpCaseVariableBO.setTaskType(x.getTaskType());
                    hacpCaseVariableBO.setTaskParam(x.getTaskParamJson().toString());
                    return hacpCaseVariableBO;
                })
                .collect(Collectors.toMap(HacpCaseVariableBO::getActivityId, Function.identity()));
        String processInstance = caseService.startProcessInstance(caseReqDto.getId(), TenantUtils.getWorkspaceIdNotNull(), variables, caseReqDto.getUuid());
        return DefaultRspDTO.newSuccessInstance(new StartRspDTO(processInstance));
    }

    @ApiOperation(value = "", notes = "查询是否存在有启动的流程")
    @ApiResponse(code = 200, message = "返回结果")
    @GetMapping ("/exist-runtime-case/{id}")
    @LogRecord(title = "启动流程案例", action = "新增")
    @PreAuthorize("hasPermission('CaseController','emergency:case:start')")
    public DefaultRspDTO<Boolean> existRuntimeCase(@PathVariable("id") Long id){
        Boolean exited = caseService.existRuntimeCase(id,TenantUtils.getWorkspaceIdNotNull());
        return DefaultRspDTO.newSuccessInstance(exited);
    }

    private List<HacpCaseVariableRspDto> convertTaskVariables(List<HacpCaseVariableBO> variables) {
        return variables.stream().map(
                bo -> {
                    HacpCaseVariableRspDto hacpCaseVariableRspDto = BeanUtils.copyPropertiesReturnDest(new HacpCaseVariableRspDto(), bo);
                    AbstractTaskStrategyFactory instance = AbstractTaskStrategyFactory.newInstance(hacpCaseVariableRspDto.getTaskType());
                    hacpCaseVariableRspDto.setTaskParamJson(instance.toTaskParam(bo.getTaskParam()));
                    return hacpCaseVariableRspDto;

                }).collect(Collectors.toList());
    }
}
