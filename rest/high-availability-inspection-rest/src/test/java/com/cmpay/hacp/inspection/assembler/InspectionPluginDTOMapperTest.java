package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.ParamType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfigScript;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;
import com.cmpay.hacp.inspection.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 巡检插件对象转换器单元测试
 */
public class InspectionPluginDTOMapperTest {

    private InspectionPluginDTOMapper mapper;
    private InspectionPluginReqDTO reqDTO;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(InspectionPluginDTOMapper.class);

        // 创建测试数据
        reqDTO = new InspectionPluginReqDTO();
        reqDTO.setPluginId("1001");
        reqDTO.setName("CPU使用率检查");
        reqDTO.setType(PluginType.SHELL_SCRIPT);
        reqDTO.setStatus(CommonStatusEnum.ENABLE);
        reqDTO.setDescription("检查服务器CPU使用率是否超过阈值");
        PluginConfigShellScriptDTO config = new PluginConfigShellScriptDTO();
        config.setScriptContent("#!/bin/bash\necho \"CPU使用率检查\"");
        reqDTO.setPluginConfig(config);
        reqDTO.setTagIds(Arrays.asList(1L, 2L, 3L));

        // 设置输出字段定义
        List<PluginOutputFieldDTO> results = new ArrayList<>();
        PluginOutputFieldDTO result = new PluginOutputFieldDTO();
        result.setFieldName("cpu.usage");
        result.setExampleValue("85.5");
        result.setFieldType(PluginOutputFieldType.NUMERIC);
        result.setDescription("CPU使用率");
        results.add(result);
        config.setResults(results);

        // 设置参数设置
        List<PluginScriptParameterDTO> parameters = new ArrayList<>();
        PluginScriptParameterDTO parameter = new PluginScriptParameterDTO();
        parameter.setParamName("threshold");
        parameter.setParamType(ParamType.TEXT);
        parameter.setRegexPattern("^[0-9]+$");
        parameter.setParamValue("90");
        parameter.setParamDesc("告警阈值百分比");
        parameter.setIsEncrypted(false);
        parameters.add(parameter);
        config.setParameters(parameters);
    }

    @Test
    @DisplayName("测试请求DTO转领域对象")
    void testToInspectionPlugin() {
        // 执行转换
        InspectionPlugin result = mapper.toInspectionPlugin(reqDTO);

        PluginConfigScript pluginConfig = (PluginConfigScript) result.getPluginConfig();

        // 验证基本字段映射
        assertNotNull(result);
        assertEquals(reqDTO.getPluginId(), result.getPluginId());
        assertEquals(reqDTO.getName(), result.getName());
        assertEquals(reqDTO.getDescription(), result.getDescription());
        assertEquals(((PluginConfigScriptDTO)reqDTO.getPluginConfig()).getScriptContent(), pluginConfig.getScriptContent());

        // 验证枚举类型转换
        assertEquals(PluginType.SHELL_SCRIPT, result.getType());
        assertEquals(CommonStatusEnum.ENABLE, result.getStatus());

        // 验证集合映射 - 标签ID
        assertNotNull(result.getTagIds());
        assertEquals(reqDTO.getTagIds().size(), result.getTagIds().size());
        assertTrue(result.getTagIds().containsAll(reqDTO.getTagIds()));

        // 验证输出字段定义映射
        assertNotNull(pluginConfig.getResults());
        assertEquals(1, pluginConfig.getResults().size());
        PluginOutputField outputResult = pluginConfig.getResults().get(0);
        assertEquals("cpu.usage", outputResult.getFieldName());
        assertEquals("85.5", outputResult.getExampleValue());
        assertEquals(PluginOutputFieldType.NUMERIC, outputResult.getFieldType());
        assertEquals("CPU使用率", outputResult.getDescription());

        // 验证参数设置映射
        assertNotNull(pluginConfig.getParameters());
        assertEquals(1, pluginConfig.getParameters().size());
        PluginScriptParameter paramResult = pluginConfig.getParameters().get(0);
        assertEquals("threshold", paramResult.getParamName());
        assertEquals("^[0-9]+$", paramResult.getRegexPattern());
        assertEquals("90", paramResult.getParamValue());
        assertEquals("告警阈值百分比", paramResult.getParamDesc());
        assertEquals(false, paramResult.getIsEncrypted());
    }
}
