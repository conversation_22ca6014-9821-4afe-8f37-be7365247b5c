package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 按日巡检报告详细内容响应DTO
 */
@Data
@Schema(description = "按日巡检报告详细内容响应DTO")
public class DailyInspectionReportDetailRspDTO {
    @Schema(description = "报告信息")
    private DailyInspectionReportRspDTO dailyInspectionReport;

    @Schema(description = "巡检分类统计（用于饼图展示）")
    private List<CategoryStatisticsDTO> categoryStatistics;

    @Schema(description = "近7日通过率趋势（用于柱状图展示）")
    private List<DailyPassRateTrendDTO> passRateTrends;

    @Schema(description = "异常详情列表（异常项标签页）")
    private ExceptionDetailsDTO exceptionDetails;

    @Schema(description = "趋势分析数据（趋势分析标签页）")
    private TrendAnalysisDTO trendAnalysis;

    /**
     * 巡检分类统计DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "巡检分类统计")
    public static class CategoryStatisticsDTO {
        @Schema(description = "分类名称", example = "数据库")
        private String categoryName;

        @Schema(description = "该分类的检查数量", example = "25")
        private Integer checkCount;

        @Schema(description = "占比（百分比）", example = "16.7")
        private Double percentage;
    }

    /**
     * 近7日通过率趋势DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "近7日通过率趋势")
    public static class DailyPassRateTrendDTO {
        @Schema(description = "日期", example = "2025-03-24")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate date;

        @Schema(description = "通过率", example = "92.5")
        private Double passRate;

        @Schema(description = "通过率级别", example = "良好")
        private String level;
    }

    /**
     * 异常详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "异常详情")
    public static class ExceptionDetailsDTO {
        @Schema(description = "异常总数", example = "8")
        private Integer totalCount;

        @Schema(description = "错误数", example = "2")
        private Integer errorCount;

        @Schema(description = "警告数", example = "3")
        private Integer warningCount;

        @Schema(description = "异常详情列表")
        private List<ExceptionItemDTO> exceptions;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "异常项")
        public static class ExceptionItemDTO {
            /**
             * 执行ID
             */
            private Long ruleExecutionId;

            /**
             * 异常类型（错误、警告、信息）
             */
            @Schema(description = "异常类型", example = "性能异常")
            private ReportStatusEnum status;

            /**
             * 异常标题
             */
            private String title;

            /**
             * 异常描述
             */
            @Schema(description = "异常描述", example = "CPU使用率持续超过90%")
            private String description;

            /**
             * 资源类型（如：页面、数据库、接口等）
             */
            private ResourceType resourceType;

            /**
             * 资源名称
             */
            private String resourceName;

            /**
             * 建议操作
             */
            @Schema(description = "建议措施", example = "建议优化应用程序或增加服务器资源")
            private String suggest;
            /**
             * 执行结果详情
             */
            private String details;
            /**
             * 条件描述
             */
            private String conditionMsg;

            /**
             * 发生时间
             */
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
            private LocalDateTime occurTime;
        }
    }

    /**
     * 趋势分析DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "趋势分析")
    public static class TrendAnalysisDTO {
        @Schema(description = "通过率趋势数据")
        private List<TrendPointDTO> passRateTrend;

        @Schema(description = "响应时间趋势数据")
        private List<TrendPointDTO> responseTimeTrend;

        @Schema(description = "异常数量趋势数据")
        private ExceptionTrendDTO exceptionTrend;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "趋势数据点")
        public static class TrendPointDTO {
            @Schema(description = "日期", example = "2025-03-24")
            @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
            private LocalDate date;

            @Schema(description = "日期显示", example = "03-24")
            private String dateDisplay;

            @Schema(description = "数值（通过率或响应时间）", example = "92.5")
            private Double value;

            @Schema(description = "单位（%或ms）", example = "%")
            private String unit;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Schema(description = "异常趋势")
        public static class ExceptionTrendDTO {
            @Schema(description = "错误趋势")
            private List<TrendPointDTO> errorTrend;

            @Schema(description = "警告趋势")
            private List<TrendPointDTO> warningTrend;
        }
    }
}
