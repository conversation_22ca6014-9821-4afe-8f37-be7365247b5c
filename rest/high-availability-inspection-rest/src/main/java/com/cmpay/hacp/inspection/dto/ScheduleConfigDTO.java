package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.DiscriminatorMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 调度配置基类DTO
 */
@Data
@Schema(
        description = "调度配置基类DTO",
        discriminatorProperty = "type",
        discriminatorMapping = {
                @DiscriminatorMapping(value = "1", schema = CronScheduleConfigDTO.class),
                @DiscriminatorMapping(value = "2", schema = IntervalScheduleConfigDTO.class),
                @DiscriminatorMapping(value = "3", schema = FixedTimeScheduleConfigDTO.class)
        }
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = CronScheduleConfigDTO.class, name = "1"),
        @JsonSubTypes.Type(value = IntervalScheduleConfigDTO.class, name = "2"),
        @JsonSubTypes.Type(value = FixedTimeScheduleConfigDTO.class, name = "3")
})
public abstract class ScheduleConfigDTO {

    @Schema(description = "是否启用定时执行", example = "true")
    private boolean enabled;

    @Schema(description = "调度类型：1-CRON表达式,2-固定间隔,3-固定时间", example = "1")
    private ScheduleType type;
}
