package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.report.model.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.report.model.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 按次巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportDTOMapper {

    /**
     * 查询请求DTO转领域对象
     *
     * @param reqDTO 查询请求DTO
     * @return 领域对象
     */
    @Mapping(target = "taskExecutionId", ignore = true)
    @Mapping(target = "executionDuration", ignore = true)
    @Mapping(target = "executionTimeStr", ignore = true)
    @Mapping(target = "resultStats", ignore = true)
    @Mapping(target = "generateTime", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdByName", ignore = true)
    PerInspectionReport toPerInspectionReport(PerInspectionReportQueryReqDTO reqDTO);

    /**
     * 领域对象列表转响应DTO列表
     *
     * @param reportList 领域对象列表
     * @return 响应DTO列表
     */
    List<PerInspectionReportRspDTO> toPerInspectionReportRspDTOList(List<PerInspectionReport> reportList);

    /**
     * 领域对象列表转响应DTO
     *
     * @param report 领域对象
     * @return 响应DTO列表
     */
    PerInspectionReportRspDTO toPerInspectionReportRspDTO(PerInspectionReport report);

    List<PerInspectionReportDetailRspDTO.RuleCheckDetailDTO> toRuleCheckDetailDTO(List<PerInspectionReportDetail.RuleCheckDetail> ruleCheckDetails);

    List<PerInspectionReportDetailRspDTO.ExceptionDetailDTO> toExceptionDetailDTO(List<PerInspectionReportDetail.ExceptionDetail> exceptionDetails);

    /**
     * 详细内容领域对象转响应DTO
     *
     * @param detail 详细内容领域对象
     * @return 详细内容响应DTO
     */
    PerInspectionReportDetailRspDTO toPerInspectionReportDetailRspDTO(PerInspectionReportDetail detail);

    default Map<String,List<PerInspectionReportDetailRspDTO.RuleCheckDetailDTO>> toRuleCheckDetailDTOMap(Map<String,List<PerInspectionReportDetail.RuleCheckDetail>> ruleCheckDetailMap){
        if ( ruleCheckDetailMap == null ) {
            return null;
        }
        Map<String,List<PerInspectionReportDetailRspDTO.RuleCheckDetailDTO>> result = new HashMap<>(ruleCheckDetailMap.size());
        ruleCheckDetailMap.forEach((k,v)->{
            result.put(k,toRuleCheckDetailDTO(v));
        });
        return result;
    }

    default Map<String,List<PerInspectionReportDetailRspDTO.ExceptionDetailDTO>> toExceptionDetailDTOMap(Map<String,List<PerInspectionReportDetail.ExceptionDetail>> exceptionDetailMap){
        if ( exceptionDetailMap == null ) {
            return null;
        }
        Map<String,List<PerInspectionReportDetailRspDTO.ExceptionDetailDTO>> result = new HashMap<>(exceptionDetailMap.size());
        exceptionDetailMap.forEach((k,v)->{
            result.put(k,toExceptionDetailDTO(v));
        });
        return result;
    }
}
