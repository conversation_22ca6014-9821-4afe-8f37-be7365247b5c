package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 巡检任务请求DTO
 */
@Data
@Schema(description = "巡检任务请求DTO")
public class InspectionTaskReqDTO {

    @Schema(description = "任务ID，新增时不需要传入", example = "TASK-000001")
    private String taskId;

    @Schema(description = "任务名称", required = true, example = "每日系统巡检")
    @NotBlank(message = "HAI30006")
    @Size(max = 128, message = "HAI30007")
    private String name;

    @Schema(description = "任务描述", example = "每日对系统进行全面巡检，检查系统健康状态")
    @Size(max = 500, message = "HAI30008")
    private String description;

    @Schema(description = "任务状态：0-禁用，1-启用", example = "1")
    @NotNull(message = "HAI30012")
    private CommonStatusEnum status;

    @Schema(description = "任务关联的规则执行配置列表", required = true)
    @NotEmpty(message = "HAI20003")
    @Valid
    private List<TaskRuleExecutionDTO> taskRuleExecutions;

    @Schema(description = "调度配置", required = true)
    @NotNull(message = "HAI50406")
    @Valid
    private ScheduleConfigDTO scheduleConfig;

    @Schema(description = "告警通知")
    private AlarmNotificationDTO alarmNotification;

}
