package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * K8s检查项模板DTO（精简版）
 */
@Data
@Schema(description = "K8s检查项模板")
public class K8sCheckItemTemplateDTO {
    
    @Schema(description = "检查项名称", example = "终止宽限期")
    private String name;
    
    @Schema(description = "分类", example = "resource", allowableValues = {"resource", "health", "security"})
    private String category;
    
    @Schema(description = "描述信息", example = "容器优雅关闭等待时间（秒）")
    private String description;
    
    @Schema(description = "JSONPath取值路径", example = "$.spec.template.spec.terminationGracePeriodSeconds")
    private String jsonPath;
}