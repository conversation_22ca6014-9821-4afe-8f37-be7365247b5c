package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 按次巡检报告查询请求DTO
 */
@Data
@Schema(description = "按次巡检报告查询请求DTO")
public class PerInspectionReportQueryReqDTO {

    @Schema(description = "报告ID", example = "RPT-202503240003")
    private String reportId;

    @Schema(description = "任务ID", example = "TASK-000001")
    private String taskId;

    @Schema(description = "任务名称", example = "网络连通性检测")
    private String taskName;

    @Schema(description = "触发方式：0-定时触发，1-手动触发", example = "0")
    private TriggerMode triggerMode;

    @Schema(description = "执行状态：0-待执行，1-执行中，2-已完成，3-执行失败，4-已取消，5-告警，6-超时，7-已停止", example = "2")
    private ExecutionStatus executionStatus;

    @Schema(description = "开始时间（查询范围起始）", example = "2025-03-24T10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间（查询范围结束）", example = "2025-03-24T18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "分页参数", required = true)
    @NotNull(message = "分页参数不能为空")
    private PageDTO<?> page = new PageDTO<>();
}
