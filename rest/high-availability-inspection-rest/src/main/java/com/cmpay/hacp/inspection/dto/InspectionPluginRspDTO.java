package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.DeployEnvEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginSourceEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 巡检插件响应DTO
 */
@Data
@Schema(description = "巡检插件响应DTO")
public class InspectionPluginRspDTO {
    @Schema(description = "插件ID", example = "1001")
    private String pluginId;

    @Schema(description = "插件名称", example = "CPU使用率检查")
    private String name;

    @Schema(description = "插件类型：1-SHELL脚本, 2-PYTHON脚本, 3-页面检查, 4-自动化测试", example = "1")
    private PluginType type;

    @Schema(description = "插件来源：0-内置，1-自定义", example = "0")
    private PluginSourceEnum source;

    @Schema(description = "插件状态：0-禁用，1-启用", example = "1")
    private CommonStatusEnum status;

    @Schema(description = "插件描述", example = "检查服务器CPU使用率是否超过阈值")
    private String description;

    @Schema(description = "标签列表")
    private List<TagDTO> tags;

    private List<DeployEnvEnum> deployEnvs;

    private PluginConfigDTO pluginConfig;

    private AuditInfoDTO auditInfo;
}
