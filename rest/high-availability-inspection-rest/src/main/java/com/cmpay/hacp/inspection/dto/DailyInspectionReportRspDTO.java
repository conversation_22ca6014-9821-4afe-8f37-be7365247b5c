package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.GenerateState;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 按日巡检报告响应DTO
 */
@Data
@Schema(description = "按日巡检报告响应DTO")
public class DailyInspectionReportRspDTO {

    @Schema(description = "报告日期", example = "2025-03-24")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate reportDate;

    @Schema(description = "日报告编号", example = "DR20250324")
    private String reportId;

    @Schema(description = "任务数", example = "10")
    private Integer taskCount;

    @Schema(description = "规则数", example = "15")
    private Integer ruleCount;

    @Schema(description = "执行数（总检查数）", example = "150")
    private Integer executionCount;

    @Schema(description = "告警数", example = "5")
    private Integer warningCount;

    @Schema(description = "错误数", example = "3")
    private Integer failedCount;

    @Schema(description = "通过率（百分比）", example = "92.2")
    private Double passRate;

    @Schema(description = "通过率变化（相对昨日，百分点）", example = "1.5")
    private Double passRateChange;

    @Schema(description = "平均响应时间（毫秒）", example = "2500")
    private Long averageResponseTime;

    /**
     * 响应时间变化（相对昨日，毫秒）
     * 正数表示增加，负数表示减少
     */
    private Long responseTimeChange;

    @Schema(description = "报告状态", example = "COMPLETED")
    private GenerateState reportStatus;

    /**
     * 异常总数
     */
    private Integer totalExceptionCount;

    @Schema(description = "审计信息")
    private AuditInfoDTO auditInfo;
}
