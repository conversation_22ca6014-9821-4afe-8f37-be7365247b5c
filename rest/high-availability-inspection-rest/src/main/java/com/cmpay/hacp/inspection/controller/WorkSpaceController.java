package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.tenant.api.WorkspaceApi;
import com.cmpay.hacp.tenant.service.WorkspaceBaseService;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 初始化项目接口
 */
@RestController
public class WorkSpaceController implements WorkspaceApi {

    @Autowired(required = false)
    private List<WorkspaceBaseService> workspaceBaseServices;

    @Override
    public DefaultRspDTO<NoBody> initialization(String workspaceId) {
        Optional.ofNullable(workspaceBaseServices).ifPresent(services ->
                services.forEach(workspaceBaseService -> workspaceBaseService.initialization(workspaceId))
        );
        return DefaultRspDTO.newSuccessInstance();
    }

}
