package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 固定时间调度配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("FIXED_TIME")
@Schema(description = "固定时间调度配置DTO")
public class FixedTimeScheduleConfigDTO extends ScheduleConfigDTO {

    @Schema(description = "执行时间", required = true, example = "2023-12-31 HH:mm:ss")
    @NotNull(message = "HAI50100")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime executionDateTime;


    public FixedTimeScheduleConfigDTO() {
        setType(ScheduleType.FIXED_TIME);
    }
}
