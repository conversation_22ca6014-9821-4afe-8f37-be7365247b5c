package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.plugin.model.K8sCheckItemTemplate;
import com.cmpay.hacp.inspection.dto.K8sCheckItemTemplateDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * K8s检查项模板DTO映射器
 */
@Mapper(componentModel = "spring")
public interface K8sCheckItemTemplateDTOMapper {
    
    /**
     * 转换为DTO
     */
    K8sCheckItemTemplateDTO toK8sCheckItemTemplateDTO(K8sCheckItemTemplate template);
    
    /**
     * 转换为DTO列表
     */
    List<K8sCheckItemTemplateDTO> toK8sCheckItemTemplateDTOList(List<K8sCheckItemTemplate> templates);
    
    /**
     * 转换为领域对象
     */
    K8sCheckItemTemplate toK8sCheckItemTemplate(K8sCheckItemTemplateDTO dto);
}