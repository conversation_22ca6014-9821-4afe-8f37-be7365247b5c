package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.plugin.model.*;
import com.cmpay.hacp.inspection.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ObjectFactory;
import org.mapstruct.SubclassMapping;

@Mapper(componentModel = "spring")
public interface PluginConfigDTOMapper {
    @SubclassMapping(source = PluginConfigK8sDTO.class, target = PluginConfigK8s.class)
    @SubclassMapping(source = PluginConfigShellScriptDTO.class, target = PluginConfigShellScript.class)
    @SubclassMapping(source = PluginConfigPythonScriptDTO.class, target = PluginConfigPythonScript.class)
    @SubclassMapping(source = PluginConfigScriptDTO.class, target = PluginConfigScript.class)
    PluginConfig toPluginConfig(PluginConfigDTO dto);

    @ObjectFactory
    default PluginConfig createPluginConfig(PluginConfigDTO dto) {
        throw new IllegalArgumentException("Unknown DTO type: " + dto.getClass().getName());
    }

    @SubclassMapping(source = PluginConfigK8s.class, target = PluginConfigK8sDTO.class)
    @SubclassMapping(source = PluginConfigShellScript.class, target = PluginConfigShellScriptDTO.class)
    @SubclassMapping(source = PluginConfigPythonScript.class, target = PluginConfigPythonScriptDTO.class)
    @SubclassMapping(source = PluginConfigScript.class, target = PluginConfigScriptDTO.class)
    PluginConfigDTO toPluginConfigDTO(PluginConfig config);

    @ObjectFactory
    default PluginConfigDTO createPluginConfigDTO(PluginConfig scheduleConfig) {
        throw new IllegalArgumentException("Unknown ScheduleConfig type: " + scheduleConfig.getClass().getName());
    }

    PluginOutputFieldDTO toPluginOutputFieldDTO(PluginOutputField outputField);

    @Mapping(target = "id", ignore = true)
    PluginOutputField toPluginOutputField(PluginOutputFieldDTO outputFieldDTO);
}
