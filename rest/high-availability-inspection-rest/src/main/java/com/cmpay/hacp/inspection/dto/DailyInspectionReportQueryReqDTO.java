package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.GenerateState;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 按日巡检报告查询请求DTO
 */
@Data
@Schema(description = "按日巡检报告查询请求DTO")
public class DailyInspectionReportQueryReqDTO {

    @Schema(description = "报告ID", example = "DR20250324")
    private String reportId;

    @Schema(description = "报告日期", example = "2025-03-24")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate reportDate;

    @Schema(description = "最低通过率（查询通过率大于等于此值的报告）", example = "80.0")
    private Double passRate;

    @Schema(description = "报告状态", example = "COMPLETED")
    private GenerateState reportStatus;

    @Schema(description = "分页参数", required = true)
    private PageDTO<?> page;
}
