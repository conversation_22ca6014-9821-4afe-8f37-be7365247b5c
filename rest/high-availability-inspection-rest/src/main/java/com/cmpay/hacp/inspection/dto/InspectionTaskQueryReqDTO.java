package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "巡检任务查询请求DTO")
public class InspectionTaskQueryReqDTO {


    @Schema(description = "任务名称", example = "CPU 使用率阈值检查")
    private String name;

    @Schema(description = "执行状态：0-待执行，1-执行中，2-已完成，3-执行失败，4-已取消，5-告警，6-超时，7-已停止", example = "1")
    private ExecutionStatus executionStatus;

    @Schema(description = "任务状态：0-禁用，1-启用", example = "1")
    private CommonStatusEnum status;

    @Schema(description = "开始时间", example = "2025-06-05")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-06-06")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private PageDTO<?> page = new PageDTO<>();

}
