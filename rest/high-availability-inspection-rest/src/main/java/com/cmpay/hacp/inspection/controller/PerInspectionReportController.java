package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.assembler.PerInspectionReportDTOMapper;
import com.cmpay.hacp.inspection.domain.report.model.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.report.model.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 按次巡检报告控制器
 */
@Tag(name = "按次巡检报告管理")
@RestController
@RequestMapping(VersionApi.VERSION_V1 +"/inspection/per-report")
@RequiredArgsConstructor
@Slf4j
public class PerInspectionReportController {

    private final PerInspectionReportService perInspectionReportService;
    private final PerInspectionReportDTOMapper perInspectionReportDTOMapper;

    /**
     * 分页查询按次巡检报告列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询按次巡检报告列表", description = "根据条件分页查询按次巡检报告列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:query')")
    public DefaultRspDTO<PageDTO<PerInspectionReportRspDTO>> getReportPage(
            @Validated @RequestBody PerInspectionReportQueryReqDTO reqDTO) {

        // 转换查询条件
        PerInspectionReport queryCondition = perInspectionReportDTOMapper.toPerInspectionReport(reqDTO);

        // 执行分页查询
        IPage<PerInspectionReport> page = perInspectionReportService.getReportPage(
                reqDTO.getPage(), queryCondition);

        // 转换结果
        List<PerInspectionReportRspDTO> rspDTOList = perInspectionReportDTOMapper
                .toPerInspectionReportRspDTOList(page.getRecords());

        // 构建分页响应
        PageDTO<PerInspectionReportRspDTO> result = new PageDTO<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 获取按次巡检报告详情
     *
     * @param reportId 报告ID
     * @return 报告详情
     */
    @GetMapping("/detail/{reportId}")
    @Operation(summary = "获取按次巡检报告详情", description = "获取指定按次巡检报告的基本信息")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:query')")
    public DefaultRspDTO<PerInspectionReportDetailRspDTO> getReportDetail(
            @Parameter(name = "reportId", description = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {

        // 获取报告详细内容
        PerInspectionReportDetail detail = perInspectionReportService.getReportDetailContent(reportId);
        // 转换为响应DTO
        PerInspectionReportDetailRspDTO rspDTO = perInspectionReportDTOMapper.toPerInspectionReportDetailRspDTO(detail);

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    @PostMapping("/export/{reportId}")
    @Operation(summary = "导出按次巡检报告", description = "导出指定按次巡检报告")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:export')")
    public ResponseEntity<byte[]> exportReport(
            @Parameter(name = "reportId", description = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {
        // TODO: 实现导出逻辑
        return new ResponseEntity<byte[]>(new byte[0], null, 200);
    }
}
