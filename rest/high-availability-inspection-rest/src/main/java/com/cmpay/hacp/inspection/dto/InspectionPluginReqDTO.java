package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.DeployEnvEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginSourceEnum;
import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 巡检插件请求DTO
 */
@Data
@Schema(description = "巡检插件请求DTO")
public class InspectionPluginReqDTO {

    @Schema(description = "插件ID，新增时不需要传入", example = "1001")
    private String pluginId;

    @Schema(description = "插件名称", required = true, example = "CPU使用率检查")
    @NotBlank(message = "HAI10008")
    @Size(max = 128, message = "HAI10009")
    private String name;

    @Schema(description = "插件类型：1-SHELL脚本, 2-PYTHON脚本, 3-页面检查, 4-自动化测试", required = true, example = "1")
    @NotNull(message = "HAI10010")
    private PluginType type;

    @Schema(description = "来源，0:内置，1自定义", required = true, example = "1")
    private PluginSourceEnum source;

    @Schema(description = "插件状态：0-禁用，1-启用", example = "1")
    private CommonStatusEnum status;

    @Schema(description = "插件描述", example = "检查服务器CPU使用率是否超过阈值")
    @Size(max = 500, message = "HAI10011")
    private String description;

    @Schema(description = "标签ID列表", example = "[1, 2, 3]")
    private List<Long> tagIds;

    @Schema(description = "支持环境", example = "[1, 2, 3]")
    private List<DeployEnvEnum> deployEnvs;

    @Schema(description = "加密key标识")
    private String key;

    @NotNull(message = "HAI10019")
    @Valid
    private PluginConfigDTO pluginConfig;
}
