-- CREATE TABLE `表名` (
--     -- 主键
--                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
--
--     -- 多租户支持（按需）：如需支持多租户，在表中添加 `workspace_id` 字段，并为其建立索引。
--     -- 若不需要多租户支持，则不添加该字段。
--                         `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',

--
--     -- 业务字段（根据实际需求添加）
--                         `name` VARCHAR(100) NOT NULL COMMENT '名称',
--                         `description` varchar(500) DEFAULT NULL COMMENT '描述',
--
--     -- 审计字段
--                         `created_by` varchar(64) NOT NULL COMMENT '创建人ID',
--                         `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
--                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--                         `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人ID',
--                         `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
--                         `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--
--     -- 索引
--                         PRIMARY KEY (`id`),
--                         INDEX `idx_workspace_id` (`workspace_id`),
--     -- 根据业务需求添加其他索引
--
--     -- 表备注
-- ) ENGINE=InnoDB CHARSET=utf8 COMMENT='表描述';

/*
 Navicat Premium Data Transfer

 Source Server         : hacp_sit_database
 Source Server Type    : MySQL
 Source Server Version : 80028
 Source Host           : *************:6446
 Source Schema         : hacp_sit_database

 Target Server Type    : MySQL
 Target Server Version : 80028
 File Encoding         : 65001

 Date: 21/08/2025 15:08:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for inspection_daily_inspection_report
-- ----------------------------
DROP TABLE IF EXISTS `inspection_daily_inspection_report`;

-- ----------------------------
-- Table structure for inspection_daily_inspection_report_detail
-- ----------------------------
DROP TABLE IF EXISTS `inspection_daily_inspection_report_detail`;

-- ----------------------------
-- Table structure for inspection_daily_report
-- ----------------------------
DROP TABLE IF EXISTS `inspection_daily_report`;
CREATE TABLE `inspection_daily_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
  `report_date` date NOT NULL COMMENT '报告日期',
  `report_id` varchar(64) NOT NULL COMMENT '日报告编号（如：DAILY-20250124）',
  `task_count` int NOT NULL DEFAULT '0' COMMENT '汇总的任务总数',
  `rule_count` int NOT NULL DEFAULT '0' COMMENT '汇总的规则总数',
  `execution_count` int NOT NULL DEFAULT '0' COMMENT '汇总的执行数量',
  `warning_count` int NOT NULL DEFAULT '0' COMMENT '告警检查数',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败检查数',
  `pass_rate` decimal(10,2) NOT NULL DEFAULT '0' COMMENT '整体通过率（百分比）',
  `pass_rate_change` decimal(10,2) NOT NULL DEFAULT '0' COMMENT '通过率变化（相对昨日，百分点）',
  `average_response_time` int NOT NULL DEFAULT '0' COMMENT '平均响应时间（毫秒）',
  `response_time_change` int NOT NULL DEFAULT '0' COMMENT '响应时间变化（相对昨日，毫秒）',
  `total_exception_count` int NOT NULL COMMENT '异常总数',
  `report_status` tinyint(1) NOT NULL COMMENT '报告生成状态',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_daily_report_date` (`workspace_id`,`report_date`) COMMENT '日期唯一索引',
  UNIQUE KEY `uk_workspace_report_id` (`workspace_id`,`report_id`) COMMENT '报告ID唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 COMMENT='按日巡检汇总报告表';

-- ----------------------------
-- Table structure for inspection_daily_report_detail
-- ----------------------------
DROP TABLE IF EXISTS `inspection_daily_report_detail`;
CREATE TABLE `inspection_daily_report_detail`  (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
   `report_id` bigint NOT NULL COMMENT '关联报告主表ID',
   `category_statistics` json NULL COMMENT '巡检分类统计数据（饼图用）',
   `pass_rate_trends` json NULL COMMENT '近7日通过率趋势数据（柱状图用）',
   `exception_details` json NULL COMMENT '异常详情数据（异常项标签页用）',
   `trend_analysis` json NULL COMMENT '趋势分析数据（趋势分析标签页用）',
   `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
   `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `uk_workspace_report_id`(`workspace_id` ASC, `report_id` ASC) USING BTREE COMMENT '报告ID唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '按日巡检报告详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_plugin
-- ----------------------------
DROP TABLE IF EXISTS `inspection_plugin`;
CREATE TABLE `inspection_plugin`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `plugin_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件ID',
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件名称',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '插件类型(0-SHELL、1-PYTHON)',
  `source` tinyint(1) NULL DEFAULT NULL COMMENT '来源',
  `deploy_envs` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支持环境',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '插件状态(0禁用，1启用)',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '插件描述',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_plugin`(`workspace_id` ASC, `plugin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '巡检插件基本信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_plugin_output_filed
-- ----------------------------
DROP TABLE IF EXISTS `inspection_plugin_output_filed`;
DROP TABLE IF EXISTS `inspection_plugin_output_field`;
CREATE TABLE `inspection_plugin_output_field`  (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
   `plugin_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件ID',
   `field_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字段名称(如cpu.usage)',
   `field_path` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '取值路径',
   `example_value` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '示例值',
   `field_type` tinyint(1) NOT NULL COMMENT '字段类型(0:数值型, 1:字符串型, 2:布尔型)',
   `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
   `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `uk_workspace_plugin_field`(`workspace_id` ASC, `plugin_id` ASC, `field_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '结构化数据字段定义' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_plugin_script
-- ----------------------------
DROP TABLE IF EXISTS `inspection_plugin_script`;
CREATE TABLE `inspection_plugin_script`  (
	 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
	 `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
	 `plugin_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件ID',
	 `script_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '脚本内容',
	 `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
	 `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
	 `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
	 `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
	 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	 PRIMARY KEY (`id`) USING BTREE,
	 INDEX `idx_workspace_plugin`(`workspace_id` ASC, `plugin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '插件脚本内容表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_plugin_script_parameter
-- ----------------------------
DROP TABLE IF EXISTS `inspection_plugin_script_parameter`;
CREATE TABLE `inspection_plugin_script_parameter`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `plugin_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件ID',
  `param_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '参数名称',
  `param_type` tinyint(1) NULL DEFAULT 0 COMMENT '参数类型(文本、数字、邮箱、URL、自定义正则等)',
  `regex_pattern` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数限制(正则表达式)',
  `param_value` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '参数值',
  `param_desc` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '参数描述',
  `is_encrypted` tinyint(1) NULL DEFAULT 0 COMMENT '是否加密(0不加密，1加密)',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_plugin_param`(`workspace_id` ASC, `plugin_id` ASC, `param_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '插件参数定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_plugin_tag
-- ----------------------------
DROP TABLE IF EXISTS `inspection_plugin_tag`;
CREATE TABLE `inspection_plugin_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `plugin_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '插件ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_plugin_tag`(`workspace_id` ASC, `plugin_id` ASC, `tag_id` ASC) USING BTREE,
  INDEX `idx_workspace_id`(`workspace_id` ASC) USING BTREE,
  INDEX `idx_tag_id`(`tag_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '插件标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_report
-- ----------------------------
DROP TABLE IF EXISTS `inspection_report`;
CREATE TABLE `inspection_report`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `report_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '报告编号（唯一标识，如：RPT-202503240003）',
  `task_execution_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关联的任务执行记录ID',
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID（用于关联任务信息）',
  `task_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称（如：网络连通性检测）',
  `trigger_mode` tinyint NOT NULL COMMENT '触发方式 0:定时触发 1:手动触发',
  `execution_status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '执行状态（成功、告警、失败、执行中）',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `execution_duration` bigint NULL DEFAULT NULL COMMENT '执行耗时（毫秒）',
  `execution_time_str` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行耗时（格式化字符串，如：42秒）',
  `result_stats` json NULL COMMENT '执行结果统计（JSON格式：{\"passed\": 3, \"warning\": 1, \"failed\": 2}）',
  `generate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '报告生成时间',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_report_id`(`workspace_id` ASC, `report_id` ASC) USING BTREE COMMENT '报告ID唯一索引',
  INDEX `idx_task_execution`(`workspace_id` ASC, `task_execution_id` ASC) USING BTREE COMMENT '任务执行记录索引',
  INDEX `idx_task_id`(`workspace_id` ASC, `task_id` ASC) USING BTREE COMMENT '任务ID索引',
  INDEX `idx_execution_status`(`workspace_id` ASC, `execution_status` ASC) USING BTREE COMMENT '执行状态索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '按次巡检汇总报告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_report_detail
-- ----------------------------
DROP TABLE IF EXISTS `inspection_report_detail`;
CREATE TABLE `inspection_report_detail`  (
	 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
	 `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
	 `report_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '报告编号（唯一标识，如：RPT-202503240003）',
	 `execution_summary` json NULL COMMENT '执行概况（JSON格式，包含开始时间、结束时间、执行耗时、规则总数等）',
	 `execution_distribution` json NULL COMMENT '执行结果分布（饼图数据，JSON格式，包含总检查数、通过率、各状态数量等）',
	 `time_distribution` json NULL COMMENT '执行时间分布（柱状图数据，JSON格式，包含时间区间数据）',
	 `rule_check_details` json NULL COMMENT '规则检查详情列表（JSON格式，包含规则ID、规则名称、资源信息、检查状态等）',
	 `exception_details` json NULL COMMENT '异常详情列表（问题汇总，JSON格式，包含规则信息、异常描述、建议措施等）',
	 `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
	 `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
	 PRIMARY KEY (`id`) USING BTREE,
	 UNIQUE INDEX `uk_report_id`(`workspace_id` ASC, `report_id` ASC) USING BTREE COMMENT '报告ID唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '按次巡检报告详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule`;
CREATE TABLE `inspection_rule`  (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则ID 唯一标识',
	`workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
	`rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件ID',
	`name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
	`description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则描述',
	`level` tinyint(1) NOT NULL COMMENT '告警等级(低、中、高、严重)',
	`status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '插件状态(0启用，1禁用)',
	`type` tinyint(1) NOT NULL COMMENT '规则类型(指标、日志、可用性)',
	`deploy_env` tinyint(1) NOT NULL COMMENT '部署环境',
	`created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	`created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
	`updated_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
	`updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `uk_workspace_rule`(`workspace_id` ASC, `rule_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则基本信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_condition
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_condition`;
CREATE TABLE `inspection_rule_condition`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则执行条件ID',
  `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
  `condition_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '条件组id',
  `plugin_output_filed_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '监控字段（插件输出字段Name）',
  `comparison_operator` tinyint(1) NOT NULL COMMENT '判断条件(大小等于)',
  `comparison_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数值',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则监控字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_condition_group
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_condition_group`;
CREATE TABLE `inspection_rule_condition_group`  (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
	`workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
	`rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则id',
	`plugin_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件id',
	`condition_group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '条件组id',
	`created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	`created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_condition_group_info
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_condition_group_info`;
CREATE TABLE `inspection_rule_condition_group_info`  (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则执行条件ID',
	`workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
	`condition_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '条件组id',
	`condition_logic` tinyint NOT NULL COMMENT '条件运算',
	`suggest` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '治理建议',
	`created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	`created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则监控字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_execution
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_execution`;
CREATE TABLE `inspection_rule_execution`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'workspace ID',
  `task_execution_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务执行ID',
  `rule_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
  `resource_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源类型',
  `resource_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源名称',
  `plugin_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件ID',
  `execution_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行状态(0:失败 1:成功)',
  `match_status` tinyint(1) NOT NULL COMMENT '匹配状态(0:失败 1:成功)',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `report_status` tinyint DEFAULT NULL COMMENT '报告状态',
  `execution_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '执行结果',
  `suggest` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '治理建议',
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '执行结果详情',
  `condition_msg` varchar(4096) DEFAULT NULL COMMENT '条件描述',
  `plugin_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '插件名称',
  `execution_duration` bigint NULL DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人ID',
  `created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_execution_id`(`task_execution_id` ASC) USING BTREE,
  INDEX `idx_rule_resource`(`rule_name` ASC, `resource_type` ASC) USING BTREE,
  INDEX `idx_execution_time`(`execution_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '巡检规则执行记录表，记录巡检任务中每个规则在各个资源上的具体执行状态' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_plugin_mapping
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_plugin_mapping`;
CREATE TABLE `inspection_rule_plugin_mapping`  (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
   `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
   `rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联规则标识',
   `plugin_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联插件标识',
   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
   `created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `uk_workspace_rule_plugin`(`workspace_id` ASC, `rule_id` ASC, `plugin_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则与插件多对多关联关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_plugin_param
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_plugin_param`;
CREATE TABLE `inspection_rule_plugin_param`  (
	 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则执行条件ID',
	 `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
	 `rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则ID',
	 `plugin_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件ID',
	 `plugin_param_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件参数名称',
	 `plugin_param_value` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '插件参数值',
	 `is_encrypted` tinyint(1) NULL DEFAULT 0 COMMENT '是否加密(0不加密，1加密)',
	 `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	 `created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
	 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	 `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
	 `updated_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
	 `updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	 PRIMARY KEY (`id`) USING BTREE,
	 UNIQUE INDEX `uk_workspace_rule_plugin_param_id`(`workspace_id` ASC, `rule_id` ASC, `plugin_id` ASC, `plugin_param_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则插件参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_tag
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_tag`;
CREATE TABLE `inspection_rule_tag`  (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
	`workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
	`rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规则ID',
	`tag_id` bigint NOT NULL COMMENT '标签ID',
	`created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	`created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `uk_workspace_rule_tag`(`workspace_id` ASC, `rule_id` ASC, `tag_id` ASC) USING BTREE,
	INDEX `idx_workspace_id`(`workspace_id` ASC) USING BTREE,
	INDEX `idx_tag_id`(`tag_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '规则标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_rule_target
-- ----------------------------
DROP TABLE IF EXISTS `inspection_rule_target`;
CREATE TABLE `inspection_rule_target`  (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '范围id',
   `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
   `rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则ID',
   `deploy_env` tinyint(1) NOT NULL COMMENT '部署环境',
   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
   `created_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `uk_workspace_rule_id`(`workspace_id` ASC, `rule_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则目标范围表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_tag
-- ----------------------------
DROP TABLE IF EXISTS `inspection_tag`;
CREATE TABLE `inspection_tag`  (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
   `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签名称',
   `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签描述',
   `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
   `updated_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   UNIQUE INDEX `uk_workspace_tag_name`(`workspace_id` ASC, `name` ASC) USING BTREE,
   INDEX `idx_workspace_id`(`workspace_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '标签定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task`;
CREATE TABLE `inspection_task`  (
	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
	`workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
	`task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID',
	`name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称',
	`status` tinyint(1) NULL DEFAULT 0 COMMENT '任务状态(0禁用，1启用)',
	`description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '插件描述',
	`created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
	`created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
	`updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
	`updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
	`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `uk_workspace_task`(`workspace_id` ASC, `task_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '巡检任务定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task_alarm
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task_alarm`;
CREATE TABLE `inspection_task_alarm`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID',
  `alert_conditions` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通知条件',
  `email` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知邮箱',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_task_alarm_task_id`(`workspace_id` ASC, `task_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务告警表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task_execution
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task_execution`;
CREATE TABLE `inspection_task_execution`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `execution_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '执行ID',
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID，外键，关联InspectionTask表的id',
  `task_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务名称，冗余存储，便于查询显示',
  `trigger_mode` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '触发方式 0:定时触发 1:手动触发',
  `execution_status` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '执行状态 0:待执行 1:执行中 2:已完成 3:失败 4:已停止',
  `execution_result` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行结果 pass:通过 fail:失败',
  `scheduled_time` datetime NULL DEFAULT NULL COMMENT '计划执行时间，定时触发时的计划时间',
  `execution_time` datetime NULL DEFAULT NULL COMMENT '实际执行时间，任务开始执行的时间',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间，实际开始执行的时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间，执行完成的时间',
  `duration` int NULL DEFAULT 0 COMMENT '耗时(秒)，执行总耗时',
  `rule_total` int NULL DEFAULT 0 COMMENT '关联规则总数，本次执行涉及的规则总数',
  `rule_success_count` int NULL DEFAULT 0 COMMENT '执行成功规则数',
  `rule_fail_count` int NULL DEFAULT 0 COMMENT '执行失败规则数',
  `coverage_rate` int NULL DEFAULT 0 COMMENT '资源覆盖率(%)',
  `success_rate` int NULL DEFAULT 0 COMMENT '规则通过率(%)，成功规则数/总规则数',
  `result_summary` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行结果概要',
  `status_summary` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态概要',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_execution_task_id`(`workspace_id` ASC, `execution_id` ASC, `task_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '巡检任务执行状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task_rule
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task_rule`;
CREATE TABLE `inspection_task_rule`  (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
 `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
 `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID',
 `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规则ID',
 `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
 `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
 `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uk_workspace_task_rule`(`workspace_id` ASC, `task_id` ASC, `rule_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务规则关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task_rule_resource
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task_rule_resource`;
CREATE TABLE `inspection_task_rule_resource`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
  `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID',
  `rule_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规则ID',
  `resource_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资源类型',
  `resource` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资源JSON',
  `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workspace_task_rule_resource`(`workspace_id` ASC, `task_id` ASC, `rule_id` ASC, `resource` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '规则资源关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inspection_task_schedule
-- ----------------------------
DROP TABLE IF EXISTS `inspection_task_schedule`;
CREATE TABLE `inspection_task_schedule`  (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
 `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'workspace ID',
 `task_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关联的巡检任务ID',
 `schedule_type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '调度类型(CRON/INTERVAL/FIXED_TIME)',
 `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用(0-禁用, 1-启用)',
 `cron_expression` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'CRON表达式',
 `interval_value` int NULL DEFAULT NULL COMMENT '间隔值',
 `interval_unit` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '间隔单位(MINUTE/HOUR/DAY/WEEK/MONTH)',
 `execution_date_time` datetime NULL DEFAULT NULL COMMENT '执行时间',
 `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
 `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
 `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
 `updated_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
 `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
 PRIMARY KEY (`id`) USING BTREE,
 UNIQUE INDEX `uk_workspace_task_schedule`(`workspace_id` ASC, `task_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '巡检任务调度配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for workspace_initialize_config
-- ----------------------------
DROP TABLE IF EXISTS `workspace_initialize_config`;
CREATE TABLE `workspace_initialize_config` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
   `type` tinyint(1) NOT NULL COMMENT '初始化类型',
   `config` text NOT NULL COMMENT '初始化内容',
   `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
   `created_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
   `created_by_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
SET FOREIGN_KEY_CHECKS = 1;
