-- 系统会自动创建，账号需要索引权限
-- ----------------------------
-- Table structure for ACT_GE_PROPERTY
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_PROPERTY`;
CREATE TABLE `ACT_GE_PROPERTY`  (
`NAME_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`VALUE_` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`REV_` int(11) NULL DEFAULT NULL,
PRIMARY KEY (`NAME_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_GE_SCHEMA_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_SCHEMA_LOG`;
CREATE TABLE `ACT_GE_SCHEMA_LOG`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `TIMESTAMP_` datetime NULL DEFAULT NULL,
  `VERSION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_ACTINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_ACTINST`;
CREATE TABLE `ACT_HI_ACTINST`  (
 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `PARENT_ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `CALL_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `CALL_CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `ACT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `ACT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `START_TIME_` datetime NOT NULL,
 `END_TIME_` datetime NULL DEFAULT NULL,
 `DURATION_` bigint(20) NULL DEFAULT NULL,
 `ACT_INST_STATE_` int(11) NULL DEFAULT NULL,
 `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
 PRIMARY KEY (`ID_`) USING BTREE,
 INDEX `ACT_IDX_HI_ACTINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_START_END`(`START_TIME_` ASC, `END_TIME_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_END`(`END_TIME_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_PROCINST`(`PROC_INST_ID_` ASC, `ACT_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_COMP`(`EXECUTION_ID_` ASC, `ACT_ID_` ASC, `END_TIME_` ASC, `ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_STATS`(`PROC_DEF_ID_` ASC, `PROC_INST_ID_` ASC, `ACT_ID_` ASC, `END_TIME_` ASC, `ACT_INST_STATE_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_AI_PDEFID_END_TIME`(`PROC_DEF_ID_` ASC, `END_TIME_` ASC) USING BTREE,
 INDEX `ACT_IDX_HI_ACT_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_ATTACHMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_ATTACHMENT`;
CREATE TABLE `ACT_HI_ATTACHMENT`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `URL_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CONTENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CREATE_TIME_` datetime NULL DEFAULT NULL,
  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_ATTACHMENT_CONTENT`(`CONTENT_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_HI_ATTACHMENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_HI_ATTACHMENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_HI_ATTACHMENT_TASK`(`TASK_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_HI_ATTACHMENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_BATCH
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_BATCH`;
CREATE TABLE `ACT_HI_BATCH`  (
								 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `TOTAL_JOBS_` int(11) NULL DEFAULT NULL,
								 `JOBS_PER_SEED_` int(11) NULL DEFAULT NULL,
								 `INVOCATIONS_PER_JOB_` int(11) NULL DEFAULT NULL,
								 `SEED_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `MONITOR_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `BATCH_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `START_TIME_` datetime NOT NULL,
								 `END_TIME_` datetime NULL DEFAULT NULL,
								 `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								 PRIMARY KEY (`ID_`) USING BTREE,
								 INDEX `ACT_HI_BAT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_CASEACTINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_CASEACTINST`;
CREATE TABLE `ACT_HI_CASEACTINST`  (
   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
   `PARENT_ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
   `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
   `CASE_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CALL_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CALL_CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CASE_ACT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CASE_ACT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   `CREATE_TIME_` datetime NOT NULL,
   `END_TIME_` datetime NULL DEFAULT NULL,
   `DURATION_` bigint(20) NULL DEFAULT NULL,
   `STATE_` int(11) NULL DEFAULT NULL,
   `REQUIRED_` tinyint(1) NULL DEFAULT NULL,
   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
   PRIMARY KEY (`ID_`) USING BTREE,
   INDEX `ACT_IDX_HI_CAS_A_I_CREATE`(`CREATE_TIME_` ASC) USING BTREE,
   INDEX `ACT_IDX_HI_CAS_A_I_END`(`END_TIME_` ASC) USING BTREE,
   INDEX `ACT_IDX_HI_CAS_A_I_COMP`(`CASE_ACT_ID_` ASC, `END_TIME_` ASC, `ID_` ASC) USING BTREE,
   INDEX `ACT_IDX_HI_CAS_A_I_CASEINST`(`CASE_INST_ID_` ASC, `CASE_ACT_ID_` ASC) USING BTREE,
   INDEX `ACT_IDX_HI_CAS_A_I_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_CASEINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_CASEINST`;
CREATE TABLE `ACT_HI_CASEINST`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`CREATE_TIME_` datetime NOT NULL,
`CLOSE_TIME_` datetime NULL DEFAULT NULL,
`DURATION_` bigint(20) NULL DEFAULT NULL,
`STATE_` int(11) NULL DEFAULT NULL,
`CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`SUPER_CASE_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`SUPER_PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
UNIQUE INDEX `CASE_INST_ID_`(`CASE_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_CAS_I_CLOSE`(`CLOSE_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_CAS_I_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_CAS_I_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_COMMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_COMMENT`;
CREATE TABLE `ACT_HI_COMMENT`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TIME_` datetime NOT NULL,
								   `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ACTION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `MESSAGE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `FULL_MSG_` longblob NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_HI_COMMENT_TASK`(`TASK_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_COMMENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_COMMENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_COMMENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DECINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DECINST`;
CREATE TABLE `ACT_HI_DECINST`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `DEC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `DEC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `DEC_DEF_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `EVAL_TIME_` datetime NOT NULL,
								   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								   `COLLECT_VALUE_` double NULL DEFAULT NULL,
								   `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ROOT_DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DEC_REQ_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DEC_REQ_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_ID`(`DEC_DEF_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_KEY`(`DEC_DEF_KEY_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_PI`(`PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_CI`(`CASE_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_ACT`(`ACT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_ACT_INST`(`ACT_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_TIME`(`EVAL_TIME_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_ROOT_ID`(`ROOT_DEC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_REQ_ID`(`DEC_REQ_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_REQ_KEY`(`DEC_REQ_KEY_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DEC_IN
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DEC_IN`;
CREATE TABLE `ACT_HI_DEC_IN`  (
								  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `CLAUSE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CLAUSE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `DOUBLE_` double NULL DEFAULT NULL,
								  `LONG_` bigint(20) NULL DEFAULT NULL,
								  `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CREATE_TIME_` datetime NULL DEFAULT NULL,
								  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								  PRIMARY KEY (`ID_`) USING BTREE,
								  INDEX `ACT_IDX_HI_DEC_IN_INST`(`DEC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DEC_IN_CLAUSE`(`DEC_INST_ID_` ASC, `CLAUSE_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DEC_IN_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DEC_IN_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DEC_OUT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DEC_OUT`;
CREATE TABLE `ACT_HI_DEC_OUT`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `CLAUSE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CLAUSE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `RULE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `RULE_ORDER_` int(11) NULL DEFAULT NULL,
								   `VAR_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DOUBLE_` double NULL DEFAULT NULL,
								   `LONG_` bigint(20) NULL DEFAULT NULL,
								   `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CREATE_TIME_` datetime NULL DEFAULT NULL,
								   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_OUT_INST`(`DEC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_OUT_RULE`(`RULE_ORDER_` ASC, `CLAUSE_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_OUT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_DEC_OUT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DETAIL
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DETAIL`;
CREATE TABLE `ACT_HI_DETAIL`  (
								  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `VAR_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `VAR_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `REV_` int(11) NULL DEFAULT NULL,
								  `TIME_` datetime NOT NULL,
								  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `DOUBLE_` double NULL DEFAULT NULL,
								  `LONG_` bigint(20) NULL DEFAULT NULL,
								  `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
								  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `OPERATION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								  `INITIAL_` tinyint(1) NULL DEFAULT NULL,
								  PRIMARY KEY (`ID_`) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_PROC_INST`(`PROC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_ACT_INST`(`ACT_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_CASE_EXEC`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_TIME`(`TIME_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_NAME`(`NAME_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_TASK_ID`(`TASK_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_BYTEAR`(`BYTEARRAY_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_TASK_BYTEAR`(`BYTEARRAY_ID_` ASC, `TASK_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_DETAIL_VAR_INST_ID`(`VAR_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_EXT_TASK_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_EXT_TASK_LOG`;
CREATE TABLE `ACT_HI_EXT_TASK_LOG`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`TIMESTAMP_` timestamp NOT NULL,
	`EXT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`RETRIES_` int(11) NULL DEFAULT NULL,
	`TOPIC_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`WORKER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
	`ERROR_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ERROR_DETAILS_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`STATE_` int(11) NULL DEFAULT NULL,
	`REV_` int(11) NULL DEFAULT NULL,
	`REMOVAL_TIME_` datetime NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_EXTTASKLOG_ERRORDET`(`ERROR_DETAILS_ID_` ASC) USING BTREE,
	INDEX `ACT_HI_EXT_TASK_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_IDENTITYLINK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_IDENTITYLINK`;
CREATE TABLE `ACT_HI_IDENTITYLINK`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`TIMESTAMP_` timestamp NOT NULL,
	`TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`OPERATION_TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ASSIGNER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`REMOVAL_TIME_` datetime NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_USER`(`USER_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_GROUP`(`GROUP_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LINK_TASK`(`TASK_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LINK_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
	INDEX `ACT_IDX_HI_IDENT_LNK_TIMESTAMP`(`TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_INCIDENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_INCIDENT`;
CREATE TABLE `ACT_HI_INCIDENT`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CREATE_TIME_` timestamp NOT NULL,
`END_TIME_` timestamp NULL DEFAULT NULL,
`INCIDENT_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`INCIDENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`FAILED_ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ROOT_CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`HISTORY_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`INCIDENT_STATE_` int(11) NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`REMOVAL_TIME_` datetime NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_CREATE_TIME`(`CREATE_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_INCIDENT_END_TIME`(`END_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_JOB_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_JOB_LOG`;
CREATE TABLE `ACT_HI_JOB_LOG`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `TIMESTAMP_` datetime NOT NULL,
								   `JOB_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `JOB_DUEDATE_` datetime NULL DEFAULT NULL,
								   `JOB_RETRIES_` int(11) NULL DEFAULT NULL,
								   `JOB_PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
								   `JOB_EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `JOB_EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `JOB_STATE_` int(11) NULL DEFAULT NULL,
								   `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `JOB_DEF_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `JOB_DEF_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `FAILED_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROCESS_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROCESS_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `HOSTNAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_PROCINST`(`PROCESS_INSTANCE_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_PROCDEF`(`PROCESS_DEF_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_JOB_DEF_ID`(`JOB_DEF_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_PROC_DEF_KEY`(`PROCESS_DEF_KEY_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_EX_STACK`(`JOB_EXCEPTION_STACK_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_JOB_LOG_JOB_CONF`(`JOB_DEF_CONFIGURATION_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_OP_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_OP_LOG`;
CREATE TABLE `ACT_HI_OP_LOG`  (
								  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `JOB_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `BATCH_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TIMESTAMP_` timestamp NOT NULL,
								  `OPERATION_TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `OPERATION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ENTITY_TYPE_` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `PROPERTY_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ORG_VALUE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `NEW_VALUE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								  `CATEGORY_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `EXTERNAL_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  `ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  PRIMARY KEY (`ID_`) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_TASK`(`TASK_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_TIMESTAMP`(`TIMESTAMP_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_USER_ID`(`USER_ID_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_OP_TYPE`(`OPERATION_TYPE_` ASC) USING BTREE,
								  INDEX `ACT_IDX_HI_OP_LOG_ENTITY_TYPE`(`ENTITY_TYPE_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_PROCINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_PROCINST`;
CREATE TABLE `ACT_HI_PROCINST`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`START_TIME_` datetime NOT NULL,
`END_TIME_` datetime NULL DEFAULT NULL,
`REMOVAL_TIME_` datetime NULL DEFAULT NULL,
`DURATION_` bigint(20) NULL DEFAULT NULL,
`START_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`START_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`END_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`SUPER_PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`SUPER_CASE_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`STATE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
UNIQUE INDEX `PROC_INST_ID_`(`PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_END`(`END_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_I_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_PROC_TIME`(`START_TIME_` ASC, `END_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PI_PDEFID_END_TIME`(`PROC_DEF_ID_` ASC, `END_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_PRO_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_TASKINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_TASKINST`;
CREATE TABLE `ACT_HI_TASKINST`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`START_TIME_` datetime NOT NULL,
`END_TIME_` datetime NULL DEFAULT NULL,
`DURATION_` bigint(20) NULL DEFAULT NULL,
`DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PRIORITY_` int(11) NULL DEFAULT NULL,
`DUE_DATE_` datetime NULL DEFAULT NULL,
`FOLLOW_UP_DATE_` datetime NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`REMOVAL_TIME_` datetime NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
INDEX `ACT_IDX_HI_TASKINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASK_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASK_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASKINST_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASKINSTID_PROCINST`(`ID_` ASC, `PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASK_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASK_INST_START`(`START_TIME_` ASC) USING BTREE,
INDEX `ACT_IDX_HI_TASK_INST_END`(`END_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_VARINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_VARINST`;
CREATE TABLE `ACT_HI_VARINST`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `CREATE_TIME_` datetime NULL DEFAULT NULL,
								   `REV_` int(11) NULL DEFAULT NULL,
								   `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DOUBLE_` double NULL DEFAULT NULL,
								   `LONG_` bigint(20) NULL DEFAULT NULL,
								   `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `STATE_` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_HI_VARINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_PROCVAR_PROC_INST`(`PROC_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_PROCVAR_NAME_TYPE`(`NAME_` ASC, `VAR_TYPE_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_CASEVAR_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VAR_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VAR_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VARINST_BYTEAR`(`BYTEARRAY_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VARINST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VAR_PI_NAME_TYPE`(`PROC_INST_ID_` ASC, `NAME_` ASC, `VAR_TYPE_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VARINST_NAME`(`NAME_` ASC) USING BTREE,
								   INDEX `ACT_IDX_HI_VARINST_ACT_INST_ID`(`ACT_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_GROUP
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_GROUP`;
CREATE TABLE `ACT_ID_GROUP`  (
								 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								 `REV_` int(11) NULL DEFAULT NULL,
								 `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								 PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_INFO
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_INFO`;
CREATE TABLE `ACT_ID_INFO`  (
								`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								`REV_` int(11) NULL DEFAULT NULL,
								`USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`VALUE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`PASSWORD_` longblob NULL,
								`PARENT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_ID_TENANT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_TENANT`;
CREATE TABLE `ACT_ID_TENANT`  (
								  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								  `REV_` int(11) NULL DEFAULT NULL,
								  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_USER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_USER`;
CREATE TABLE `ACT_ID_USER`  (
								`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								`REV_` int(11) NULL DEFAULT NULL,
								`FIRST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`LAST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`EMAIL_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`PWD_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`SALT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								`LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
								`ATTEMPTS_` int(11) NULL DEFAULT NULL,
								`PICTURE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RE_CAMFORMDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_CAMFORMDEF`;
CREATE TABLE `ACT_RE_CAMFORMDEF`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `VERSION_` int(11) NOT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RE_DECISION_REQ_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DECISION_REQ_DEF`;
CREATE TABLE `ACT_RE_DECISION_REQ_DEF`  (
		`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
		`REV_` int(11) NULL DEFAULT NULL,
		`CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
		`VERSION_` int(11) NOT NULL,
		`DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		PRIMARY KEY (`ID_`) USING BTREE,
		INDEX `ACT_IDX_DEC_REQ_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RE_DEPLOYMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DEPLOYMENT`;
CREATE TABLE `ACT_RE_DEPLOYMENT`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DEPLOY_TIME_` datetime NULL DEFAULT NULL,
  `SOURCE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_DEPLOYMENT_NAME`(`NAME_` ASC) USING BTREE,
  INDEX `ACT_IDX_DEPLOYMENT_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_AUTHORIZATION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_AUTHORIZATION`;
CREATE TABLE `ACT_RU_AUTHORIZATION`  (
	 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	 `REV_` int(11) NOT NULL,
	 `TYPE_` int(11) NOT NULL,
	 `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 `RESOURCE_TYPE_` int(11) NOT NULL,
	 `RESOURCE_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 `PERMS_` int(11) NULL DEFAULT NULL,
	 `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
	 `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 PRIMARY KEY (`ID_`) USING BTREE,
	 UNIQUE INDEX `ACT_UNIQ_AUTH_USER`(`USER_ID_` ASC, `TYPE_` ASC, `RESOURCE_TYPE_` ASC, `RESOURCE_ID_` ASC) USING BTREE,
	 UNIQUE INDEX `ACT_UNIQ_AUTH_GROUP`(`GROUP_ID_` ASC, `TYPE_` ASC, `RESOURCE_TYPE_` ASC, `RESOURCE_ID_` ASC) USING BTREE,
	 INDEX `ACT_IDX_AUTH_GROUP_ID`(`GROUP_ID_` ASC) USING BTREE,
	 INDEX `ACT_IDX_AUTH_RESOURCE_ID`(`RESOURCE_ID_` ASC) USING BTREE,
	 INDEX `ACT_IDX_AUTH_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
	 INDEX `ACT_IDX_AUTH_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RE_CASE_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_CASE_DEF`;
CREATE TABLE `ACT_RE_CASE_DEF`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`REV_` int(11) NULL DEFAULT NULL,
`CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`VERSION_` int(11) NOT NULL,
`DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`HISTORY_TTL_` int(11) NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
INDEX `ACT_IDX_CASE_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_CASE_EXECUTION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_CASE_EXECUTION`;
CREATE TABLE `ACT_RU_CASE_EXECUTION`  (
	  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	  `REV_` int(11) NULL DEFAULT NULL,
	  `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `SUPER_CASE_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `SUPER_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `PARENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  `PREV_STATE_` int(11) NULL DEFAULT NULL,
	  `CURRENT_STATE_` int(11) NULL DEFAULT NULL,
	  `REQUIRED_` tinyint(1) NULL DEFAULT NULL,
	  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	  PRIMARY KEY (`ID_`) USING BTREE,
	  INDEX `ACT_IDX_CASE_EXEC_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
	  INDEX `ACT_IDX_CASE_EXE_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
	  INDEX `ACT_FK_CASE_EXE_PARENT`(`PARENT_ID_` ASC) USING BTREE,
	  INDEX `ACT_FK_CASE_EXE_CASE_DEF`(`CASE_DEF_ID_` ASC) USING BTREE,
	  INDEX `ACT_IDX_CASE_EXEC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_CASE_DEF` FOREIGN KEY (`CASE_DEF_ID_`) REFERENCES `ACT_RE_CASE_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_PARENT` FOREIGN KEY (`PARENT_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_CASE_SENTRY_PART
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_CASE_SENTRY_PART`;
CREATE TABLE `ACT_RU_CASE_SENTRY_PART`  (
		`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
		`REV_` int(11) NULL DEFAULT NULL,
		`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`CASE_EXEC_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`SENTRY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`SOURCE_CASE_EXEC_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`STANDARD_EVENT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`SOURCE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`VARIABLE_EVENT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`VARIABLE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		`SATISFIED_` tinyint(1) NULL DEFAULT NULL,
		`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
		PRIMARY KEY (`ID_`) USING BTREE,
		INDEX `ACT_FK_CASE_SENTRY_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
		INDEX `ACT_FK_CASE_SENTRY_CASE_EXEC`(`CASE_EXEC_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_CASE_SENTRY_PART` ADD CONSTRAINT `ACT_FK_CASE_SENTRY_CASE_EXEC` FOREIGN KEY (`CASE_EXEC_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_CASE_SENTRY_PART` ADD CONSTRAINT `ACT_FK_CASE_SENTRY_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;


	-- ----------------------------
-- Table structure for ACT_RE_PROCDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_PROCDEF`;
CREATE TABLE `ACT_RE_PROCDEF`  (
								   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `REV_` int(11) NULL DEFAULT NULL,
								   `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
								   `VERSION_` int(11) NOT NULL,
								   `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `HAS_START_FORM_KEY_` tinyint(4) NULL DEFAULT NULL,
								   `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
								   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `VERSION_TAG_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
								   `HISTORY_TTL_` int(11) NULL DEFAULT NULL,
								   `STARTABLE_` tinyint(1) NOT NULL DEFAULT 1,
								   PRIMARY KEY (`ID_`) USING BTREE,
								   INDEX `ACT_IDX_PROCDEF_DEPLOYMENT_ID`(`DEPLOYMENT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_PROCDEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
								   INDEX `ACT_IDX_PROCDEF_VER_TAG`(`VERSION_TAG_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_EXECUTION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EXECUTION`;
CREATE TABLE `ACT_RU_EXECUTION`  (
 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `REV_` int(11) NULL DEFAULT NULL,
 `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PARENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `SUPER_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `SUPER_CASE_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `IS_ACTIVE_` tinyint(4) NULL DEFAULT NULL,
 `IS_CONCURRENT_` tinyint(4) NULL DEFAULT NULL,
 `IS_SCOPE_` tinyint(4) NULL DEFAULT NULL,
 `IS_EVENT_SCOPE_` tinyint(4) NULL DEFAULT NULL,
 `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
 `CACHED_ENT_STATE_` int(11) NULL DEFAULT NULL,
 `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 PRIMARY KEY (`ID_`) USING BTREE,
 INDEX `ACT_IDX_EXEC_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_EXEC_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
 INDEX `ACT_IDX_EXEC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
 INDEX `ACT_FK_EXE_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
 INDEX `ACT_FK_EXE_PARENT`(`PARENT_ID_` ASC) USING BTREE,
 INDEX `ACT_FK_EXE_SUPER`(`SUPER_EXEC_` ASC) USING BTREE,
 INDEX `ACT_FK_EXE_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PARENT` FOREIGN KEY (`PARENT_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_SUPER` FOREIGN KEY (`SUPER_EXEC_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_TASK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_TASK`;
CREATE TABLE `ACT_RU_TASK`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`REV_` int(11) NULL DEFAULT NULL,
	`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`DELEGATION_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PRIORITY_` int(11) NULL DEFAULT NULL,
	`CREATE_TIME_` datetime NULL DEFAULT NULL,
	`DUE_DATE_` datetime NULL DEFAULT NULL,
	`FOLLOW_UP_DATE_` datetime NULL DEFAULT NULL,
	`SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_IDX_TASK_CREATE`(`CREATE_TIME_` ASC) USING BTREE,
	INDEX `ACT_IDX_TASK_ASSIGNEE`(`ASSIGNEE_` ASC) USING BTREE,
	INDEX `ACT_IDX_TASK_OWNER`(`OWNER_` ASC) USING BTREE,
	INDEX `ACT_IDX_TASK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TASK_EXE`(`EXECUTION_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TASK_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TASK_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TASK_CASE_EXE`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TASK_CASE_DEF`(`CASE_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_CASE_DEF` FOREIGN KEY (`CASE_DEF_ID_`) REFERENCES `ACT_RE_CASE_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_CASE_EXE` FOREIGN KEY (`CASE_EXECUTION_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_FILTER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_FILTER`;
CREATE TABLE `ACT_RU_FILTER`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NOT NULL,
  `RESOURCE_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `QUERY_` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROPERTIES_` longtext CHARACTER SET utf8 COLLATE utf8_bin NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_JOBDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_JOBDEF`;
CREATE TABLE `ACT_RU_JOBDEF`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `JOB_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `JOB_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
  `JOB_PRIORITY_` bigint(20) NULL DEFAULT NULL,
  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_JOBDEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_JOBDEF_PROC_DEF_ID`(`PROC_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_METER_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_METER_LOG`;
CREATE TABLE `ACT_RU_METER_LOG`  (
 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `NAME_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `REPORTER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `VALUE_` bigint(20) NULL DEFAULT NULL,
 `TIMESTAMP_` datetime NULL DEFAULT NULL,
 `MILLISECONDS_` bigint(20) NULL DEFAULT 0,
 PRIMARY KEY (`ID_`) USING BTREE,
 INDEX `ACT_IDX_METER_LOG_MS`(`MILLISECONDS_` ASC) USING BTREE,
 INDEX `ACT_IDX_METER_LOG_NAME_MS`(`NAME_` ASC, `MILLISECONDS_` ASC) USING BTREE,
 INDEX `ACT_IDX_METER_LOG_REPORT`(`NAME_` ASC, `REPORTER_` ASC, `MILLISECONDS_` ASC) USING BTREE,
 INDEX `ACT_IDX_METER_LOG_TIME`(`TIMESTAMP_` ASC) USING BTREE,
 INDEX `ACT_IDX_METER_LOG`(`NAME_` ASC, `TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_TASK_METER_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_TASK_METER_LOG`;
CREATE TABLE `ACT_RU_TASK_METER_LOG`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ASSIGNEE_HASH_` bigint(20) NULL DEFAULT NULL,
  `TIMESTAMP_` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_TASK_METER_LOG_TIME`(`TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_MEMBERSHIP
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_MEMBERSHIP`;
CREATE TABLE `ACT_ID_MEMBERSHIP`  (
  `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `GROUP_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  PRIMARY KEY (`USER_ID_`, `GROUP_ID_`) USING BTREE,
  INDEX `ACT_FK_MEMB_GROUP`(`GROUP_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_ID_MEMBERSHIP` ADD CONSTRAINT `ACT_FK_MEMB_GROUP` FOREIGN KEY (`GROUP_ID_`) REFERENCES `ACT_ID_GROUP` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_MEMBERSHIP` ADD CONSTRAINT `ACT_FK_MEMB_USER` FOREIGN KEY (`USER_ID_`) REFERENCES `ACT_ID_USER` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_ID_TENANT_MEMBER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_TENANT_MEMBER`;
CREATE TABLE `ACT_ID_TENANT_MEMBER`  (
	 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	 `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 `GROUP_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	 PRIMARY KEY (`ID_`) USING BTREE,
	 UNIQUE INDEX `ACT_UNIQ_TENANT_MEMB_USER`(`TENANT_ID_` ASC, `USER_ID_` ASC) USING BTREE,
	 UNIQUE INDEX `ACT_UNIQ_TENANT_MEMB_GROUP`(`TENANT_ID_` ASC, `GROUP_ID_` ASC) USING BTREE,
	 INDEX `ACT_FK_TENANT_MEMB_USER`(`USER_ID_` ASC) USING BTREE,
	 INDEX `ACT_FK_TENANT_MEMB_GROUP`(`GROUP_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB` FOREIGN KEY (`TENANT_ID_`) REFERENCES `ACT_ID_TENANT` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB_GROUP` FOREIGN KEY (`GROUP_ID_`) REFERENCES `ACT_ID_GROUP` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB_USER` FOREIGN KEY (`USER_ID_`) REFERENCES `ACT_ID_USER` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_GE_BYTEARRAY
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_BYTEARRAY`;
CREATE TABLE `ACT_GE_BYTEARRAY`  (
 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `REV_` int(11) NULL DEFAULT NULL,
 `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `BYTES_` longblob NULL,
 `GENERATED_` tinyint(4) NULL DEFAULT NULL,
 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `TYPE_` int(11) NULL DEFAULT NULL,
 `CREATE_TIME_` datetime NULL DEFAULT NULL,
 `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
 PRIMARY KEY (`ID_`) USING BTREE,
 INDEX `ACT_FK_BYTEARR_DEPL`(`DEPLOYMENT_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_BYTEARRAY_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_BYTEARRAY_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
 INDEX `ACT_IDX_BYTEARRAY_NAME`(`NAME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_GE_BYTEARRAY` ADD CONSTRAINT `ACT_FK_BYTEARR_DEPL` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `ACT_RE_DEPLOYMENT` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RE_DECISION_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DECISION_DEF`;
CREATE TABLE `ACT_RE_DECISION_DEF`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`REV_` int(11) NULL DEFAULT NULL,
	`CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`VERSION_` int(11) NOT NULL,
	`DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`DEC_REQ_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`DEC_REQ_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`HISTORY_TTL_` int(11) NULL DEFAULT NULL,
	`VERSION_TAG_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_IDX_DEC_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_DEC_DEF_REQ_ID`(`DEC_REQ_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RE_DECISION_DEF` ADD CONSTRAINT `ACT_FK_DEC_REQ` FOREIGN KEY (`DEC_REQ_ID_`) REFERENCES `ACT_RE_DECISION_REQ_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_BATCH
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_BATCH`;
CREATE TABLE `ACT_RU_BATCH`  (
 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
 `REV_` int(11) NOT NULL,
 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `TOTAL_JOBS_` int(11) NULL DEFAULT NULL,
 `JOBS_CREATED_` int(11) NULL DEFAULT NULL,
 `JOBS_PER_SEED_` int(11) NULL DEFAULT NULL,
 `INVOCATIONS_PER_JOB_` int(11) NULL DEFAULT NULL,
 `SEED_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `BATCH_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `MONITOR_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
 `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 `CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
 PRIMARY KEY (`ID_`) USING BTREE,
 INDEX `ACT_IDX_BATCH_SEED_JOB_DEF`(`SEED_JOB_DEF_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_BATCH_MONITOR_JOB_DEF`(`MONITOR_JOB_DEF_ID_` ASC) USING BTREE,
 INDEX `ACT_IDX_BATCH_JOB_DEF`(`BATCH_JOB_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_JOB_DEF` FOREIGN KEY (`BATCH_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_MONITOR_JOB_DEF` FOREIGN KEY (`MONITOR_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_SEED_JOB_DEF` FOREIGN KEY (`SEED_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_EVENT_SUBSCR
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EVENT_SUBSCR`;
CREATE TABLE `ACT_RU_EVENT_SUBSCR`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`REV_` int(11) NULL DEFAULT NULL,
	`EVENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`EVENT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`CREATED_` datetime NOT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_IDX_EVENT_SUBSCR_CONFIG_`(`CONFIGURATION_` ASC) USING BTREE,
	INDEX `ACT_IDX_EVENT_SUBSCR_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_EVENT_EXEC`(`EXECUTION_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_EVENT_SUBSCR_EVT_NAME`(`EVENT_NAME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EVENT_SUBSCR` ADD CONSTRAINT `ACT_FK_EVENT_EXEC` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_EXT_TASK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EXT_TASK`;
CREATE TABLE `ACT_RU_EXT_TASK`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`REV_` int(11) NOT NULL,
`WORKER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TOPIC_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`RETRIES_` int(11) NULL DEFAULT NULL,
`ERROR_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ERROR_DETAILS_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
`SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
`LAST_FAILURE_LOG_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
INDEX `ACT_IDX_EXT_TASK_TOPIC`(`TOPIC_NAME_` ASC) USING BTREE,
INDEX `ACT_IDX_EXT_TASK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_EXT_TASK_PRIORITY`(`PRIORITY_` ASC) USING BTREE,
INDEX `ACT_IDX_EXT_TASK_ERR_DETAILS`(`ERROR_DETAILS_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_EXT_TASK_EXEC`(`EXECUTION_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EXT_TASK` ADD CONSTRAINT `ACT_FK_EXT_TASK_ERROR_DETAILS` FOREIGN KEY (`ERROR_DETAILS_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXT_TASK` ADD CONSTRAINT `ACT_FK_EXT_TASK_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_IDENTITYLINK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_IDENTITYLINK`;
CREATE TABLE `ACT_RU_IDENTITYLINK`  (
	`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`REV_` int(11) NULL DEFAULT NULL,
	`GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
	PRIMARY KEY (`ID_`) USING BTREE,
	INDEX `ACT_IDX_IDENT_LNK_USER`(`USER_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_IDENT_LNK_GROUP`(`GROUP_ID_` ASC) USING BTREE,
	INDEX `ACT_IDX_ATHRZ_PROCEDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
	INDEX `ACT_FK_TSKASS_TASK`(`TASK_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_IDENTITYLINK` ADD CONSTRAINT `ACT_FK_ATHRZ_PROCEDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_IDENTITYLINK` ADD CONSTRAINT `ACT_FK_TSKASS_TASK` FOREIGN KEY (`TASK_ID_`) REFERENCES `ACT_RU_TASK` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_INCIDENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_INCIDENT`;
CREATE TABLE `ACT_RU_INCIDENT`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`REV_` int(11) NOT NULL,
`INCIDENT_TIMESTAMP_` datetime NOT NULL,
`INCIDENT_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`INCIDENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`FAILED_ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ROOT_CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
INDEX `ACT_IDX_INC_CONFIGURATION`(`CONFIGURATION_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_JOB_DEF`(`JOB_DEF_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_CAUSEINCID`(`CAUSE_INCIDENT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_EXID`(`EXECUTION_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_PROCDEFID`(`PROC_DEF_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_PROCINSTID`(`PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_INC_ROOTCAUSEINCID`(`ROOT_CAUSE_INCIDENT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_CAUSE` FOREIGN KEY (`CAUSE_INCIDENT_ID_`) REFERENCES `ACT_RU_INCIDENT` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_JOB_DEF` FOREIGN KEY (`JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_RCAUSE` FOREIGN KEY (`ROOT_CAUSE_INCIDENT_ID_`) REFERENCES `ACT_RU_INCIDENT` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;

	-- ----------------------------
-- Table structure for ACT_RU_VARIABLE
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_VARIABLE`;
CREATE TABLE `ACT_RU_VARIABLE`  (
`ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`REV_` int(11) NULL DEFAULT NULL,
`TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`BATCH_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`DOUBLE_` double NULL DEFAULT NULL,
`LONG_` bigint(20) NULL DEFAULT NULL,
`TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
`VAR_SCOPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
`SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
`IS_CONCURRENT_LOCAL_` tinyint(4) NULL DEFAULT NULL,
`TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
PRIMARY KEY (`ID_`) USING BTREE,
UNIQUE INDEX `ACT_UNIQ_VARIABLE`(`VAR_SCOPE_` ASC, `NAME_` ASC) USING BTREE,
INDEX `ACT_IDX_VARIABLE_TASK_ID`(`TASK_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_VARIABLE_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_VARIABLE_TASK_NAME_TYPE`(`TASK_ID_` ASC, `NAME_` ASC, `TYPE_` ASC) USING BTREE,
INDEX `ACT_FK_VAR_EXE`(`EXECUTION_ID_` ASC) USING BTREE,
INDEX `ACT_FK_VAR_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
INDEX `ACT_FK_VAR_BYTEARRAY`(`BYTEARRAY_ID_` ASC) USING BTREE,
INDEX `ACT_IDX_BATCH_ID`(`BATCH_ID_` ASC) USING BTREE,
INDEX `ACT_FK_VAR_CASE_EXE`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
INDEX `ACT_FK_VAR_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_BATCH` FOREIGN KEY (`BATCH_ID_`) REFERENCES `ACT_RU_BATCH` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_BYTEARRAY` FOREIGN KEY (`BYTEARRAY_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_CASE_EXE` FOREIGN KEY (`CASE_EXECUTION_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

	-- ----------------------------
-- Table structure for ACT_RU_JOB
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_JOB`;
CREATE TABLE `ACT_RU_JOB`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
  `LOCK_OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RETRIES_` int(11) NULL DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `FAILED_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DUEDATE_` datetime NULL DEFAULT NULL,
  `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `REPEAT_OFFSET_` bigint(20) NULL DEFAULT 0,
  `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NOT NULL DEFAULT 1,
  `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
  `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CREATE_TIME_` datetime NULL DEFAULT NULL,
  `LAST_FAILURE_LOG_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_JOB_EXECUTION_ID`(`EXECUTION_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_JOB_HANDLER`(`HANDLER_TYPE_`(100) ASC, `HANDLER_CFG_`(155) ASC) USING BTREE,
  INDEX `ACT_IDX_JOB_PROCINST`(`PROCESS_INSTANCE_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_JOB_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_JOB_JOB_DEF_ID`(`JOB_DEF_ID_` ASC) USING BTREE,
  INDEX `ACT_FK_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_` ASC) USING BTREE,
  INDEX `ACT_IDX_JOB_HANDLER_TYPE`(`HANDLER_TYPE_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_JOB` ADD CONSTRAINT `ACT_FK_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE `act_hi_varinst` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;
ALTER TABLE `act_ru_variable` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;
ALTER TABLE `act_hi_detail` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;

DROP TABLE IF EXISTS `message_content`;
CREATE TABLE `message_content`
(
	`message_content_id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
	`extra_id` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拓展id',
	`tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
	`workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目id',
	`message_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型',
	`message_title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
	`message_content` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
	`scope` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '范围',
	`message_child_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子类型',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	`operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	`status` tinyint(1) NULL DEFAULT 0 COMMENT '删除标记，1删除',
	`extra_params` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展参数',
	PRIMARY KEY (`message_content_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `message_content_user`;
CREATE TABLE `message_content_user`
(
	`message_id`         bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键',
	`message_content_id` int                                                          NOT NULL COMMENT '消息id',
	`user_id`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型',
	`status`             tinyint(1)                                                   NOT NULL DEFAULT 1 COMMENT '消息状态，1未读，0已读',
	`update_time`        datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`message_id`) USING BTREE,
	INDEX `user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = Dynamic;


SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for import_history_file_info
-- ----------------------------
DROP TABLE IF EXISTS `import_history_file_info`;
CREATE TABLE `import_history_file_info`
(
	`id`                   int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '主键',
	`file_name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '文件名称',
	`file_blob`            mediumblob                                                   NULL COMMENT '文件内容',
	`excel_count`          int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息总计',
	`excel_success`        int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息成功数',
	`excel_fail`           int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息失败数',
	`excel_success_remark` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci     NULL     DEFAULT NULL COMMENT '成功序列（1,2,3）',
	`excel_fail_remark`    varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci     NULL     DEFAULT NULL COMMENT '失败序列（4,5,6）',
	`batch_number`         varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '批次号',
	`create_time`          datetime                                                     NOT NULL COMMENT '创建时间',
	`workspace_id`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
	`operator_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
	`operator_name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app`
(
	`app_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用系统编码',
	`app_name`       varchar(64) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用系统中文名称',
	`dept_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '部门id',
	`app_owner_id`   varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用负责人',
	`app_key`        varchar(256) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '应用系统秘钥',
	`salt`           varchar(100) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '应用密钥加盐',
	`pub_key`        text CHARACTER SET utf8 COLLATE utf8_bin                      NULL COMMENT '应用公钥',
	`pri_key`        text CHARACTER SET utf8 COLLATE utf8_bin                      NULL COMMENT '应用私钥',
	`status`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT 'DISABLE:禁用  ENABLE:启用',
	`remark`         varchar(100) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '备注',
	`create_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp(6)                                                  NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`    timestamp(6)                                                  NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
	`token_key`      varchar(128) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT '' COMMENT '访问token申请的key',
	`user_source`    varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NULL DEFAULT NULL COMMENT 'SELF_REGISTER 用户自己注册 ADMIN_REGISTER 系统注册',
	`sm_public_key`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SM公钥',
	`sm_private_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SM私钥',
	PRIMARY KEY (`app_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '应用系统注册表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`
(
	`dept_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
	`parent_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级部门id，一级部门为0',
	`dept_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '部门名称',
	`order_num`      int(11)                                                NOT NULL COMMENT '排序',
	`status`         varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'DISABLE:禁用  ENABLE:启用',
	`create_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp(6)                                           NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`    timestamp(6)                                           NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
	PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '部门管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`
(
	`dict_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '编号',
	`value`       varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据值',
	`label`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '标签名',
	`type`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '类型',
	`description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '描述',
	`sort`        decimal(10, 0)                                                 NOT NULL COMMENT '排序（升序）',
	`parent_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NULL     DEFAULT '0' COMMENT '父级编号',
	`create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '创建者',
	`create_time` datetime                                                       NOT NULL COMMENT '创建时间',
	`update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '更新者',
	`update_time` datetime                                                       NOT NULL COMMENT '更新时间',
	`remarks`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '备注信息',
	`status`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`dict_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dynamic_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_dynamic_log`;
CREATE TABLE `sys_dynamic_log`
(
	`id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT 'ID',
	`request_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '日志号',
	`operator`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者',
	`operation_action` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作动作',
	`execution_target` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行对象',
	`operator_ip`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作者IP地址',
	`operator_time`    datetime                                                      NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '执行时间',
	`operator_status`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作状态',
	`data_size`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '数据量级',
	`data_size_type`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '数据量级单位(line-行数、byte-大小)',
	`application_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称/id',
	`data_it`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据所属对象',
	`data_path`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流转路径',
	`params`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '请求参数',
	`interface_record` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接口记录',
	`end_time`         datetime                                                      NULL DEFAULT NULL COMMENT '结束时间',
	`duration`         bigint(20)                                                    NULL DEFAULT NULL COMMENT '耗时(毫秒)',
	`type`             char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NULL DEFAULT '1' COMMENT '日志类型',
	`exception`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '异常信息',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '动态日志/调用第三方日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`
(
	`id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '编号',
	`type`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NULL DEFAULT '1' COMMENT '日志类型',
	`title`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作动作',
	`user_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者ID',
	`user_name`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作者',
	`msg_cd`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT 'URM00000' COMMENT '消息码',
	`mobile`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者手机号码',
	`create_by`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者ID',
	`create_date`        datetime                                                      NULL DEFAULT NULL COMMENT '执行时间',
	`remote_addr`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作IP地址',
	`user_agent`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户终端',
	`request_uri`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行对象',
	`application_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称',
	`data_it`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据所属对象',
	`method`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作方式',
	`params`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '请求参数',
	`exception`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '异常信息',
	`request_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '日志号',
	`rsp_data_size`      bigint(20)                                                    NULL DEFAULT NULL COMMENT '响应报文大小',
	`rsp_data_size_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT 'byte' COMMENT '响应报文大小类型(line-行数、byte-大小)',
	`duration`           bigint(20)                                                    NULL DEFAULT NULL COMMENT '耗时(毫秒-millisecond）',
	`end_time`           datetime                                                      NULL DEFAULT NULL COMMENT '结束时间',
	`tenant_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '租户ID',
	`workspace_id`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '项目ID',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `sys_log_pk_app_name` (`application_name` ASC) USING BTREE,
	INDEX `sys_log_pk_mobile` (`mobile` ASC) USING BTREE,
	INDEX `sys_log_pk_create_by` (`create_by` ASC) USING BTREE,
	INDEX `sys_log_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_login_history_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_history_log`;
CREATE TABLE `sys_login_history_log`
(
	`id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键id',
	`user_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户id',
	`user_name`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '用户名',
	`name`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '姓名',
	`mobile`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '手机号码',
	`login_ip`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '登录IP',
	`login_terminal` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录终端设备信息 user_agent',
	`login_from`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录来源',
	`login_date`     date                                                          NOT NULL COMMENT '登录日期',
	`login_time`     datetime                                                      NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '登录时间',
	`request_id`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '日志流水号',
	`is_use`         smallint(6)                                                   NOT NULL DEFAULT 1 COMMENT '是否可用(0-否,1-是)',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `sys_login_history_log_pk_user_name` (`user_name` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_mobile` (`mobile` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_name` (`name` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '登录记录详情历史表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_login_latest_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_latest_info`;
CREATE TABLE `sys_login_latest_info`
(
	`id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
	`pid`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联主键',
	`user_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
	`latest_date` date                                                         NOT NULL COMMENT '最近一次登录日期',
	`latest_time` datetime                                                     NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近一次登录时间',
	`first_date`  date                                                         NOT NULL COMMENT '第一次登录日期',
	`first_time`  datetime                                                     NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '第一登录时间',
	`is_use`      smallint(6)                                                  NOT NULL DEFAULT 0 COMMENT '是否可用(0-否,1-是)',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `sys_login_latest_info_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '最近一次登录信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`
(
	`menu_id`             bigint(20)                                       NOT NULL AUTO_INCREMENT COMMENT '菜单id',
	`parent_id`           bigint(20)                                       NOT NULL COMMENT '父菜单id，一级菜单为0',
	`name`                varchar(50) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL COMMENT '菜单名称',
	`app_id`              varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '应用英文简称',
	`url`                 varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '菜单url',
	`perms`               varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
	`type`                varchar(4) CHARACTER SET utf8 COLLATE utf8_bin   NULL     DEFAULT NULL COMMENT '类型   D：目录   M：菜单   B：按钮',
	`icon`                varchar(50) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '菜单图标',
	`order_num`           bigint(20)                                       NULL     DEFAULT 0 COMMENT '排序',
	`create_user_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`         timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`         timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
	`meta`                varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT 'meta数据',
	`component`           varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '组件名',
	`redirect`            varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '路由重定向跳转地址',
	`en_name`             varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '对应的英文名',
	`parent_name`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '父菜单名称',
	`hide_title`          tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏标题',
	`hidden`              tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏',
	`hide_children`       tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏子菜单',
	`keepalive`           tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '是否缓存',
	`hide_page_title_bar` tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏标题栏标题',
	PRIMARY KEY (`menu_id`) USING BTREE,
	INDEX `idx_appid` (`app_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 189177
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '菜单管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
	`role_id`        bigint(20)                                       NOT NULL AUTO_INCREMENT COMMENT '角色编号',
	`role_name`      varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '角色名称',
	`remark`         varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '备注',
	`status`         varchar(16) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT 'ENABLE' COMMENT 'DISABLE:禁用  ENABLE:启用',
	`dept_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '部门id',
	`create_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp                                        NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`modify_time`    timestamp                                        NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	`owner_app_id`   varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '归属应用',
	PRIMARY KEY (`role_id`) USING BTREE,
	UNIQUE INDEX `un_rolename_appid` (`role_name` ASC, `owner_app_id` ASC) USING BTREE,
	INDEX `idx_appid` (`owner_app_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1108
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '角色'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`
(
	`id`      varchar(150) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`role_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '角色id',
	`menu_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '菜单id',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '用户与角色对应关系'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
	`user_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT '' COMMENT '用户编号',
	`user_name`       varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '用户名',
	`scim_user_id`    varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT '' COMMENT '新4A用户编号',
	`scim_user_name`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '新4A用户名',
	`full_name`       varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '姓名',
	`password`        varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '密码',
	`salt`            varchar(20) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '盐',
	`dept_id`         varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '部门编号',
	`duty_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '岗位编号',
	`email`           varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '邮箱',
	`mobile`          varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '手机号',
	`weixin`          varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '微信号',
	`status`          varchar(16) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT 'ENABLE' COMMENT 'DISABLE:禁用  ENABLE:启用',
	`create_user_id`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`     timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`     timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
	`last_login_time` timestamp(6)                                     NULL     DEFAULT NULL COMMENT '用户上次登陆时间',
	`has_role`        varchar(20) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '是否拥有角色 Y:是 N:否',
	`cst_user_id`     varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT '' COMMENT '用户中心id',
	`app_id`          varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '应用id',
	`pwd_modify_time` timestamp(6)                                     NULL     DEFAULT NULL COMMENT '密码上次修改时间',
	PRIMARY KEY (`user_id`) USING BTREE,
	UNIQUE INDEX `un_username_appid` (`user_name` ASC, `app_id` ASC) USING BTREE,
	INDEX `idx_appid` (`app_id` ASC) USING BTREE,
	INDEX `uk_mobile_appid` (`mobile` ASC) USING BTREE,
	INDEX `scim_user_id_pk` (`scim_user_id` ASC) USING BTREE,
	INDEX `scim_user_name_index` (`scim_user_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '系统用户'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`
(
	`id`      varchar(150) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL DEFAULT NULL COMMENT '用户id',
	`role_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '角色id',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '用户与角色对应关系'
  ROW_FORMAT = Dynamic;



SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for cmft_dispatch_config
-- ----------------------------
DROP TABLE IF EXISTS `cmft_dispatch_config`;
CREATE TABLE `cmft_dispatch_config`  (
	 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
	 `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
	 `external_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公钥',
	 `external_private_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '私钥',
	 `cmft_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '双活公钥',
	 `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '在双活的项目id',
	 `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	 `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	 `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
	 `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
	 `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
	 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `emergency_task_script`;
CREATE TABLE `emergency_task_script`  (
	  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
	  `script_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本名称',
	  `script_describe` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本描述',
	  `script_content` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本内容',
	  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
	  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
	  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
	  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;
-- ----------------------------
-- Table structure for emergency_app
-- ----------------------------
DROP TABLE IF EXISTS `emergency_app`;
CREATE TABLE `emergency_app`  (
								  `host_app_id` int(11) NOT NULL AUTO_INCREMENT,
								  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
								  `host_app` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
								  `host_app_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
								  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
								  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
								  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
								  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
								  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
								  PRIMARY KEY (`host_app_id`) USING BTREE,
								  UNIQUE INDEX `un_host_app`(`host_app` ASC ,`workspace_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_container
-- ----------------------------
DROP TABLE IF EXISTS `emergency_container`;
CREATE TABLE `emergency_container`  (
	`container_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端id',
	`client_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密钥',
	`grant_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '授权类型',
	`username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
	`password` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
	`root_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '端点根url',
	`workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目空间ID',
	`cloud_workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '云平台项目空间ID',
	`cloud_clusters` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '云平台项目集群名称',
	`operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	`operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	`create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`container_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_entity_tag
-- ----------------------------
DROP TABLE IF EXISTS `emergency_entity_tag`;
CREATE TABLE `emergency_entity_tag`  (
	 `entity_id` int(11) NOT NULL COMMENT '实体ID',
	 `tag_id` int(11) NOT NULL COMMENT 'tag ID',
	 `entity_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '实体类型',
	 `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目空间ID',
	 `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
	 `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	 `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	 PRIMARY KEY (`entity_id`, `tag_id`, `entity_type`,`workspace_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '实体标签关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_host
-- ----------------------------
DROP TABLE IF EXISTS `emergency_host`;
CREATE TABLE `emergency_host`  (
								   `host_id` int(11) NOT NULL AUTO_INCREMENT,
								   `host_desc` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器描述',
								   `host_address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机器地址',
								   `host_port` int(11) NULL DEFAULT NULL COMMENT 'port',
								   `host_username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
								   `host_password` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
								   `secret_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥',
								   `host_app_id` int(11) NULL DEFAULT NULL COMMENT '应用id',
								   `host_os` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机系统',
								   `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
								   `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
								   `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
								   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
								   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
								   PRIMARY KEY (`host_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for import_history_file_info
-- ----------------------------
DROP TABLE IF EXISTS `import_history_file_info`;
CREATE TABLE `import_history_file_info`  (
		 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
		 `file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件名称',
		 `file_blob` mediumblob NULL COMMENT '文件内容',
		 `excel_count` int NOT NULL DEFAULT 0 COMMENT 'API导入信息总计',
		 `excel_success` int NOT NULL DEFAULT 0 COMMENT 'API导入信息成功数',
		 `excel_fail` int NOT NULL DEFAULT 0 COMMENT 'API导入信息失败数',
		 `excel_success_remark` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成功序列（1,2,3）',
		 `excel_fail_remark` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '失败序列（4,5,6）',
		 `batch_number` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '批次号',
		 `create_time` datetime NOT NULL COMMENT '创建时间',
		 `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
		 `operator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
		 `operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
		 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for emergency_host_archive
-- ----------------------------
DROP TABLE IF EXISTS `emergency_host_archive`;
CREATE TABLE `emergency_host_archive`  (
	   `id` bigint NOT NULL AUTO_INCREMENT,
	   `host_id` bigint NOT NULL COMMENT '主机id',
	   `activity_id` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程任务编号',
	   `business_key` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '绑定的业务id',
	   `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
	   `host_desc` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器描述',
	   `host_address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机器地址',
	   `host_port` int NULL DEFAULT NULL COMMENT 'port',
	   `host_username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
	   `host_password` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
	   `secret_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥',
	   `host_os` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机系统',
	   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务主机存档表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for emergency_tag
-- ----------------------------
DROP TABLE IF EXISTS `emergency_tag`;
CREATE TABLE `emergency_tag`  (
								  `tag_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
								  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
								  `tag` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签',
								  `tag_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签中文',
								  `entity_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '实体类型',
								  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
								  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
								  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
								  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
								  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
								  PRIMARY KEY (`tag_id`) USING BTREE,
								  UNIQUE INDEX `uk_tag`(`tag` ASC, `entity_type` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_case
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_case`;
CREATE TABLE `hacp_emergency_case`  (
	`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
	`workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
	`case_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
	`case_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
	`case_deploy_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
	`operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
	`operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
	`create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
	`update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
	`status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
	`tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '应急案例表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_node_recode
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_node_recode`;
CREATE TABLE `hacp_emergency_node_recode`  (
		   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
		   `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
		   `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
		   `case_id` bigint(20) NOT NULL COMMENT '案例编号',
		   `task_id` bigint(20) NOT NULL COMMENT '任务编号',
		   `task_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
		   `task_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
		   `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
		   `business_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务KEY',
		   `task_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型',
		   `activity_node_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动节点ID',
		   `task_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
		   `result_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '响应参数',
		   `execute_result` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行结果',
		   `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
		   `duration` int(11) NULL DEFAULT 0 COMMENT '耗时',
		   `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
		   `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
		   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '节点调用流水表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_service_node_recode
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_service_node_recode`;
CREATE TABLE `hacp_emergency_service_node_recode`  (
				   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
				   `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
				   `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
				   `case_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
				   `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
				   `node_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '节点类型',
				   `activity_node_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动节点ID',
				   `execute_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行ID',
				   `request_param` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求参数',
				   `response_param` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '响应参数',
				   `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
				   `duration` int(11) NULL DEFAULT NULL COMMENT '耗时',
				   `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
				   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '服务节点调用流水表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_task
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_task`;
CREATE TABLE `hacp_emergency_task`  (
	`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
	`workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
	`task_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
	`task_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型：人工、系统应急调度、接口调用、shell',
	`task_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
	`task_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务参数JSON',
	`task_operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务执行人',
	`operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
	`operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
	`create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
	`update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
	`status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
	`tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '应急案例任务表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for emergency_task_script
-- ----------------------------
DROP TABLE IF EXISTS `emergency_task_script`;
CREATE TABLE `emergency_task_script`  (
	  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
	  `script_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本名称',
	  `script_describe` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本描述',
	  `script_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本内容',
	  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
	  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
	  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
	  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
	  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
	  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;


DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant`
(
	`tenant_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`tenant_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户名称',
	`dept_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '部门编号',
	`create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time` datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time` datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`tenant_id`) USING BTREE,
	UNIQUE INDEX `tenant_name_pk` (`tenant_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_user`;
CREATE TABLE `tenant_user`
(
	`id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`tenant_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`user_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`tenant_user_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '租户用户类型(1-租户管理员，0-普通用户)',
	`create_user`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`      datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`      datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `tenant_user_pk` (`tenant_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户成员表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace`;
CREATE TABLE `tenant_workspace`
(
	`id`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`tenant_id`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`create_user`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`  datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`  datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `tenant_workplace_pk` (`tenant_id` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户项目关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_role
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_role`;
CREATE TABLE `tenant_workspace_role`
(
	`workspace_role_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`workspace_role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色名称',
	`workspace_role_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色类型',
	`workspace_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`create_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`         datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`         datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`workspace_role_id`) USING BTREE,
	UNIQUE INDEX `workplace_role_pk` (`workspace_role_name` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目角色表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_role_menu`;
CREATE TABLE `tenant_workspace_role_menu`
(
	`id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`menu_id`           int(11)                                                       NOT NULL COMMENT '菜单ID',
	`create_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`       datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`       datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_role_menu_pk` (`workspace_role_id` ASC, `menu_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目角色菜单关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_user`;
CREATE TABLE `tenant_workspace_user`
(
	`id`                  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`user_id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`workspace_user_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '项目成员类型(1-项目管理员，0-普通用户)',
	`create_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`         datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`         datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_user_pk` (`workspace_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目成员表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_user_role
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_user_role`;
CREATE TABLE `tenant_workspace_user_role`
(
	`id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`user_id`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`create_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`       datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`       datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_user_role_pk` (`workspace_role_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目用户角色关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for workspace
-- ----------------------------
DROP TABLE IF EXISTS `workspace`;
CREATE TABLE `workspace`
(
	`workspace_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`workspace_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目名称',
	`profile`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '环境',
	`create_user`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`    datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`    datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`workspace_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目表'
  ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `cmft_dispatch_config`;
CREATE TABLE `cmft_dispatch_config`  (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
                                         `external_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公钥',
                                         `external_private_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '私钥',
                                         `cmft_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '双活公钥',
                                         `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '在双活的项目id',
                                         `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
                                         `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
                                         `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                         `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                         `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_node_recode
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_node_recode`;
CREATE TABLE `hacp_emergency_node_recode`  (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                               `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
                                               `workspace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
                                               `case_id` bigint(20) NOT NULL COMMENT '案例编号',
                                               `task_id` bigint(20) NOT NULL COMMENT '任务编号',
                                               `task_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
                                               `task_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
                                               `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
                                               `business_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务KEY',
                                               `task_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型',
                                               `activity_node_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动节点ID',
                                               `task_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
                                               `result_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '响应参数',
                                               `execute_result` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行结果',
                                               `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                               `duration` int(11) NULL DEFAULT 0 COMMENT '耗时',
                                               `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
                                               `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '节点调用流水表' ROW_FORMAT = Dynamic;