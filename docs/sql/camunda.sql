-- 系统会自动创建，账号需要索引权限
-- ----------------------------
-- Table structure for ACT_GE_PROPERTY
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_PROPERTY`;
CREATE TABLE `ACT_GE_PROPERTY`  (
                                    `NAME_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `VALUE_` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `REV_` int(11) NULL DEFAULT NULL,
                                    PRIMARY KEY (`NAME_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_GE_SCHEMA_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_SCHEMA_LOG`;
CREATE TABLE `ACT_GE_SCHEMA_LOG`  (
                                      `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `TIMESTAMP_` datetime NULL DEFAULT NULL,
                                      `VERSION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_ACTINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_ACTINST`;
CREATE TABLE `ACT_HI_ACTINST`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `PARENT_ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CALL_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CALL_CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `START_TIME_` datetime NOT NULL,
                                   `END_TIME_` datetime NULL DEFAULT NULL,
                                   `DURATION_` bigint(20) NULL DEFAULT NULL,
                                   `ACT_INST_STATE_` int(11) NULL DEFAULT NULL,
                                   `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACTINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_START_END`(`START_TIME_` ASC, `END_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_END`(`END_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_PROCINST`(`PROC_INST_ID_` ASC, `ACT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_COMP`(`EXECUTION_ID_` ASC, `ACT_ID_` ASC, `END_TIME_` ASC, `ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_STATS`(`PROC_DEF_ID_` ASC, `PROC_INST_ID_` ASC, `ACT_ID_` ASC, `END_TIME_` ASC, `ACT_INST_STATE_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_AI_PDEFID_END_TIME`(`PROC_DEF_ID_` ASC, `END_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_ACT_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_ATTACHMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_ATTACHMENT`;
CREATE TABLE `ACT_HI_ATTACHMENT`  (
                                      `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `REV_` int(11) NULL DEFAULT NULL,
                                      `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `URL_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `CONTENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                      `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                      PRIMARY KEY (`ID_`) USING BTREE,
                                      INDEX `ACT_IDX_HI_ATTACHMENT_CONTENT`(`CONTENT_ID_` ASC) USING BTREE,
                                      INDEX `ACT_IDX_HI_ATTACHMENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                      INDEX `ACT_IDX_HI_ATTACHMENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                      INDEX `ACT_IDX_HI_ATTACHMENT_TASK`(`TASK_ID_` ASC) USING BTREE,
                                      INDEX `ACT_IDX_HI_ATTACHMENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_BATCH
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_BATCH`;
CREATE TABLE `ACT_HI_BATCH`  (
                                 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `TOTAL_JOBS_` int(11) NULL DEFAULT NULL,
                                 `JOBS_PER_SEED_` int(11) NULL DEFAULT NULL,
                                 `INVOCATIONS_PER_JOB_` int(11) NULL DEFAULT NULL,
                                 `SEED_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `MONITOR_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `BATCH_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `START_TIME_` datetime NOT NULL,
                                 `END_TIME_` datetime NULL DEFAULT NULL,
                                 `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                 PRIMARY KEY (`ID_`) USING BTREE,
                                 INDEX `ACT_HI_BAT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_CASEACTINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_CASEACTINST`;
CREATE TABLE `ACT_HI_CASEACTINST`  (
                                       `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `PARENT_ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `CASE_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                       `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CALL_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CALL_CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CASE_ACT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CASE_ACT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       `CREATE_TIME_` datetime NOT NULL,
                                       `END_TIME_` datetime NULL DEFAULT NULL,
                                       `DURATION_` bigint(20) NULL DEFAULT NULL,
                                       `STATE_` int(11) NULL DEFAULT NULL,
                                       `REQUIRED_` tinyint(1) NULL DEFAULT NULL,
                                       `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                       PRIMARY KEY (`ID_`) USING BTREE,
                                       INDEX `ACT_IDX_HI_CAS_A_I_CREATE`(`CREATE_TIME_` ASC) USING BTREE,
                                       INDEX `ACT_IDX_HI_CAS_A_I_END`(`END_TIME_` ASC) USING BTREE,
                                       INDEX `ACT_IDX_HI_CAS_A_I_COMP`(`CASE_ACT_ID_` ASC, `END_TIME_` ASC, `ID_` ASC) USING BTREE,
                                       INDEX `ACT_IDX_HI_CAS_A_I_CASEINST`(`CASE_INST_ID_` ASC, `CASE_ACT_ID_` ASC) USING BTREE,
                                       INDEX `ACT_IDX_HI_CAS_A_I_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_CASEINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_CASEINST`;
CREATE TABLE `ACT_HI_CASEINST`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `CREATE_TIME_` datetime NOT NULL,
                                    `CLOSE_TIME_` datetime NULL DEFAULT NULL,
                                    `DURATION_` bigint(20) NULL DEFAULT NULL,
                                    `STATE_` int(11) NULL DEFAULT NULL,
                                    `CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `SUPER_CASE_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `SUPER_PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    UNIQUE INDEX `CASE_INST_ID_`(`CASE_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_CAS_I_CLOSE`(`CLOSE_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_CAS_I_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_CAS_I_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_COMMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_COMMENT`;
CREATE TABLE `ACT_HI_COMMENT`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TIME_` datetime NOT NULL,
                                   `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACTION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `MESSAGE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `FULL_MSG_` longblob NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_COMMENT_TASK`(`TASK_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_COMMENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_COMMENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_COMMENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DECINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DECINST`;
CREATE TABLE `ACT_HI_DECINST`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `DEC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `DEC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `DEC_DEF_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `EVAL_TIME_` datetime NOT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   `COLLECT_VALUE_` double NULL DEFAULT NULL,
                                   `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ROOT_DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DEC_REQ_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DEC_REQ_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_ID`(`DEC_DEF_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_KEY`(`DEC_DEF_KEY_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_PI`(`PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_CI`(`CASE_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_ACT`(`ACT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_ACT_INST`(`ACT_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_TIME`(`EVAL_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_ROOT_ID`(`ROOT_DEC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_REQ_ID`(`DEC_REQ_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_REQ_KEY`(`DEC_REQ_KEY_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DEC_IN
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DEC_IN`;
CREATE TABLE `ACT_HI_DEC_IN`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `CLAUSE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CLAUSE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `DOUBLE_` double NULL DEFAULT NULL,
                                  `LONG_` bigint(20) NULL DEFAULT NULL,
                                  `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE,
                                  INDEX `ACT_IDX_HI_DEC_IN_INST`(`DEC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DEC_IN_CLAUSE`(`DEC_INST_ID_` ASC, `CLAUSE_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DEC_IN_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DEC_IN_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DEC_OUT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DEC_OUT`;
CREATE TABLE `ACT_HI_DEC_OUT`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `DEC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `CLAUSE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CLAUSE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `RULE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `RULE_ORDER_` int(11) NULL DEFAULT NULL,
                                   `VAR_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DOUBLE_` double NULL DEFAULT NULL,
                                   `LONG_` bigint(20) NULL DEFAULT NULL,
                                   `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_OUT_INST`(`DEC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_OUT_RULE`(`RULE_ORDER_` ASC, `CLAUSE_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_OUT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_DEC_OUT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_DETAIL
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_DETAIL`;
CREATE TABLE `ACT_HI_DETAIL`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `VAR_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `VAR_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `REV_` int(11) NULL DEFAULT NULL,
                                  `TIME_` datetime NOT NULL,
                                  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `DOUBLE_` double NULL DEFAULT NULL,
                                  `LONG_` bigint(20) NULL DEFAULT NULL,
                                  `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                                  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `OPERATION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                  `INITIAL_` tinyint(1) NULL DEFAULT NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_PROC_INST`(`PROC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_ACT_INST`(`ACT_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_CASE_EXEC`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_TIME`(`TIME_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_NAME`(`NAME_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_TASK_ID`(`TASK_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_BYTEAR`(`BYTEARRAY_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_TASK_BYTEAR`(`BYTEARRAY_ID_` ASC, `TASK_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_DETAIL_VAR_INST_ID`(`VAR_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_EXT_TASK_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_EXT_TASK_LOG`;
CREATE TABLE `ACT_HI_EXT_TASK_LOG`  (
                                        `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `TIMESTAMP_` timestamp NOT NULL,
                                        `EXT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `RETRIES_` int(11) NULL DEFAULT NULL,
                                        `TOPIC_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `WORKER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
                                        `ERROR_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ERROR_DETAILS_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `STATE_` int(11) NULL DEFAULT NULL,
                                        `REV_` int(11) NULL DEFAULT NULL,
                                        `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                        PRIMARY KEY (`ID_`) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_EXTTASKLOG_ERRORDET`(`ERROR_DETAILS_ID_` ASC) USING BTREE,
                                        INDEX `ACT_HI_EXT_TASK_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_IDENTITYLINK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_IDENTITYLINK`;
CREATE TABLE `ACT_HI_IDENTITYLINK`  (
                                        `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `TIMESTAMP_` timestamp NOT NULL,
                                        `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `OPERATION_TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ASSIGNER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                        PRIMARY KEY (`ID_`) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_USER`(`USER_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_GROUP`(`GROUP_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LINK_TASK`(`TASK_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LINK_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_HI_IDENT_LNK_TIMESTAMP`(`TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_INCIDENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_INCIDENT`;
CREATE TABLE `ACT_HI_INCIDENT`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CREATE_TIME_` timestamp NOT NULL,
                                    `END_TIME_` timestamp NULL DEFAULT NULL,
                                    `INCIDENT_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `INCIDENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `FAILED_ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ROOT_CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `HISTORY_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `INCIDENT_STATE_` int(11) NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_CREATE_TIME`(`CREATE_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_INCIDENT_END_TIME`(`END_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_JOB_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_JOB_LOG`;
CREATE TABLE `ACT_HI_JOB_LOG`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `TIMESTAMP_` datetime NOT NULL,
                                   `JOB_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `JOB_DUEDATE_` datetime NULL DEFAULT NULL,
                                   `JOB_RETRIES_` int(11) NULL DEFAULT NULL,
                                   `JOB_PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
                                   `JOB_EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `JOB_EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `JOB_STATE_` int(11) NULL DEFAULT NULL,
                                   `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `JOB_DEF_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `JOB_DEF_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `FAILED_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROCESS_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROCESS_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `HOSTNAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_PROCINST`(`PROCESS_INSTANCE_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_PROCDEF`(`PROCESS_DEF_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_JOB_DEF_ID`(`JOB_DEF_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_PROC_DEF_KEY`(`PROCESS_DEF_KEY_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_EX_STACK`(`JOB_EXCEPTION_STACK_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_JOB_LOG_JOB_CONF`(`JOB_DEF_CONFIGURATION_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_OP_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_OP_LOG`;
CREATE TABLE `ACT_HI_OP_LOG`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `JOB_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `BATCH_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TIMESTAMP_` timestamp NOT NULL,
                                  `OPERATION_TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `OPERATION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ENTITY_TYPE_` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROPERTY_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ORG_VALUE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `NEW_VALUE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                  `CATEGORY_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `EXTERNAL_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_TASK`(`TASK_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_TIMESTAMP`(`TIMESTAMP_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_USER_ID`(`USER_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_OP_TYPE`(`OPERATION_TYPE_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_HI_OP_LOG_ENTITY_TYPE`(`ENTITY_TYPE_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_PROCINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_PROCINST`;
CREATE TABLE `ACT_HI_PROCINST`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `START_TIME_` datetime NOT NULL,
                                    `END_TIME_` datetime NULL DEFAULT NULL,
                                    `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                    `DURATION_` bigint(20) NULL DEFAULT NULL,
                                    `START_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `START_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `END_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `SUPER_PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `SUPER_CASE_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `STATE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    UNIQUE INDEX `PROC_INST_ID_`(`PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_END`(`END_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_I_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_PROC_TIME`(`START_TIME_` ASC, `END_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PI_PDEFID_END_TIME`(`PROC_DEF_ID_` ASC, `END_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_PRO_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_TASKINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_TASKINST`;
CREATE TABLE `ACT_HI_TASKINST`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `START_TIME_` datetime NOT NULL,
                                    `END_TIME_` datetime NULL DEFAULT NULL,
                                    `DURATION_` bigint(20) NULL DEFAULT NULL,
                                    `DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PRIORITY_` int(11) NULL DEFAULT NULL,
                                    `DUE_DATE_` datetime NULL DEFAULT NULL,
                                    `FOLLOW_UP_DATE_` datetime NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASKINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASK_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASK_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASKINST_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASKINSTID_PROCINST`(`ID_` ASC, `PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASK_INST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASK_INST_START`(`START_TIME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_HI_TASK_INST_END`(`END_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_HI_VARINST
-- ----------------------------
DROP TABLE IF EXISTS `ACT_HI_VARINST`;
CREATE TABLE `ACT_HI_VARINST`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                   `REV_` int(11) NULL DEFAULT NULL,
                                   `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DOUBLE_` double NULL DEFAULT NULL,
                                   `LONG_` bigint(20) NULL DEFAULT NULL,
                                   `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `STATE_` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_HI_VARINST_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_PROCVAR_PROC_INST`(`PROC_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_PROCVAR_NAME_TYPE`(`NAME_` ASC, `VAR_TYPE_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_CASEVAR_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VAR_INST_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VAR_INST_PROC_DEF_KEY`(`PROC_DEF_KEY_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VARINST_BYTEAR`(`BYTEARRAY_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VARINST_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VAR_PI_NAME_TYPE`(`PROC_INST_ID_` ASC, `NAME_` ASC, `VAR_TYPE_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VARINST_NAME`(`NAME_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_HI_VARINST_ACT_INST_ID`(`ACT_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_GROUP
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_GROUP`;
CREATE TABLE `ACT_ID_GROUP`  (
                                 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                 `REV_` int(11) NULL DEFAULT NULL,
                                 `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_INFO
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_INFO`;
CREATE TABLE `ACT_ID_INFO`  (
                                `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                `REV_` int(11) NULL DEFAULT NULL,
                                `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `VALUE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PASSWORD_` longblob NULL,
                                `PARENT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_ID_TENANT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_TENANT`;
CREATE TABLE `ACT_ID_TENANT`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `REV_` int(11) NULL DEFAULT NULL,
                                  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_USER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_USER`;
CREATE TABLE `ACT_ID_USER`  (
                                `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                `REV_` int(11) NULL DEFAULT NULL,
                                `FIRST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `LAST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `EMAIL_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PWD_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `SALT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
                                `ATTEMPTS_` int(11) NULL DEFAULT NULL,
                                `PICTURE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RE_CAMFORMDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_CAMFORMDEF`;
CREATE TABLE `ACT_RE_CAMFORMDEF`  (
                                      `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `REV_` int(11) NULL DEFAULT NULL,
                                      `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `VERSION_` int(11) NOT NULL,
                                      `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RE_DECISION_REQ_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DECISION_REQ_DEF`;
CREATE TABLE `ACT_RE_DECISION_REQ_DEF`  (
                                            `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                            `REV_` int(11) NULL DEFAULT NULL,
                                            `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                            `VERSION_` int(11) NOT NULL,
                                            `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            PRIMARY KEY (`ID_`) USING BTREE,
                                            INDEX `ACT_IDX_DEC_REQ_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RE_DEPLOYMENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DEPLOYMENT`;
CREATE TABLE `ACT_RE_DEPLOYMENT`  (
                                      `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `DEPLOY_TIME_` datetime NULL DEFAULT NULL,
                                      `SOURCE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                      PRIMARY KEY (`ID_`) USING BTREE,
                                      INDEX `ACT_IDX_DEPLOYMENT_NAME`(`NAME_` ASC) USING BTREE,
                                      INDEX `ACT_IDX_DEPLOYMENT_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_AUTHORIZATION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_AUTHORIZATION`;
CREATE TABLE `ACT_RU_AUTHORIZATION`  (
                                         `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                         `REV_` int(11) NOT NULL,
                                         `TYPE_` int(11) NOT NULL,
                                         `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         `RESOURCE_TYPE_` int(11) NOT NULL,
                                         `RESOURCE_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         `PERMS_` int(11) NULL DEFAULT NULL,
                                         `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                         `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         PRIMARY KEY (`ID_`) USING BTREE,
                                         UNIQUE INDEX `ACT_UNIQ_AUTH_USER`(`USER_ID_` ASC, `TYPE_` ASC, `RESOURCE_TYPE_` ASC, `RESOURCE_ID_` ASC) USING BTREE,
                                         UNIQUE INDEX `ACT_UNIQ_AUTH_GROUP`(`GROUP_ID_` ASC, `TYPE_` ASC, `RESOURCE_TYPE_` ASC, `RESOURCE_ID_` ASC) USING BTREE,
                                         INDEX `ACT_IDX_AUTH_GROUP_ID`(`GROUP_ID_` ASC) USING BTREE,
                                         INDEX `ACT_IDX_AUTH_RESOURCE_ID`(`RESOURCE_ID_` ASC) USING BTREE,
                                         INDEX `ACT_IDX_AUTH_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                         INDEX `ACT_IDX_AUTH_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RE_CASE_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_CASE_DEF`;
CREATE TABLE `ACT_RE_CASE_DEF`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `REV_` int(11) NULL DEFAULT NULL,
                                    `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `VERSION_` int(11) NOT NULL,
                                    `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `HISTORY_TTL_` int(11) NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    INDEX `ACT_IDX_CASE_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_CASE_EXECUTION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_CASE_EXECUTION`;
CREATE TABLE `ACT_RU_CASE_EXECUTION`  (
                                          `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                          `REV_` int(11) NULL DEFAULT NULL,
                                          `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `SUPER_CASE_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `SUPER_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `PARENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          `PREV_STATE_` int(11) NULL DEFAULT NULL,
                                          `CURRENT_STATE_` int(11) NULL DEFAULT NULL,
                                          `REQUIRED_` tinyint(1) NULL DEFAULT NULL,
                                          `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                          PRIMARY KEY (`ID_`) USING BTREE,
                                          INDEX `ACT_IDX_CASE_EXEC_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
                                          INDEX `ACT_IDX_CASE_EXE_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
                                          INDEX `ACT_FK_CASE_EXE_PARENT`(`PARENT_ID_` ASC) USING BTREE,
                                          INDEX `ACT_FK_CASE_EXE_CASE_DEF`(`CASE_DEF_ID_` ASC) USING BTREE,
                                          INDEX `ACT_IDX_CASE_EXEC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_CASE_DEF` FOREIGN KEY (`CASE_DEF_ID_`) REFERENCES `ACT_RE_CASE_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_CASE_EXECUTION` ADD CONSTRAINT `ACT_FK_CASE_EXE_PARENT` FOREIGN KEY (`PARENT_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_CASE_SENTRY_PART
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_CASE_SENTRY_PART`;
CREATE TABLE `ACT_RU_CASE_SENTRY_PART`  (
                                            `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                            `REV_` int(11) NULL DEFAULT NULL,
                                            `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `CASE_EXEC_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `SENTRY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `SOURCE_CASE_EXEC_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `STANDARD_EVENT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `SOURCE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `VARIABLE_EVENT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `VARIABLE_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            `SATISFIED_` tinyint(1) NULL DEFAULT NULL,
                                            `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                            PRIMARY KEY (`ID_`) USING BTREE,
                                            INDEX `ACT_FK_CASE_SENTRY_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE,
                                            INDEX `ACT_FK_CASE_SENTRY_CASE_EXEC`(`CASE_EXEC_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_CASE_SENTRY_PART` ADD CONSTRAINT `ACT_FK_CASE_SENTRY_CASE_EXEC` FOREIGN KEY (`CASE_EXEC_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_CASE_SENTRY_PART` ADD CONSTRAINT `ACT_FK_CASE_SENTRY_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;


-- ----------------------------
-- Table structure for ACT_RE_PROCDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_PROCDEF`;
CREATE TABLE `ACT_RE_PROCDEF`  (
                                   `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `REV_` int(11) NULL DEFAULT NULL,
                                   `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                   `VERSION_` int(11) NOT NULL,
                                   `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `HAS_START_FORM_KEY_` tinyint(4) NULL DEFAULT NULL,
                                   `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                   `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `VERSION_TAG_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                   `HISTORY_TTL_` int(11) NULL DEFAULT NULL,
                                   `STARTABLE_` tinyint(1) NOT NULL DEFAULT 1,
                                   PRIMARY KEY (`ID_`) USING BTREE,
                                   INDEX `ACT_IDX_PROCDEF_DEPLOYMENT_ID`(`DEPLOYMENT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_PROCDEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                   INDEX `ACT_IDX_PROCDEF_VER_TAG`(`VERSION_TAG_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ACT_RU_EXECUTION
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EXECUTION`;
CREATE TABLE `ACT_RU_EXECUTION`  (
                                     `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                     `REV_` int(11) NULL DEFAULT NULL,
                                     `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `PARENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `SUPER_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `SUPER_CASE_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `IS_ACTIVE_` tinyint(4) NULL DEFAULT NULL,
                                     `IS_CONCURRENT_` tinyint(4) NULL DEFAULT NULL,
                                     `IS_SCOPE_` tinyint(4) NULL DEFAULT NULL,
                                     `IS_EVENT_SCOPE_` tinyint(4) NULL DEFAULT NULL,
                                     `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                     `CACHED_ENT_STATE_` int(11) NULL DEFAULT NULL,
                                     `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                                     `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     PRIMARY KEY (`ID_`) USING BTREE,
                                     INDEX `ACT_IDX_EXEC_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_EXEC_BUSKEY`(`BUSINESS_KEY_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_EXEC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                     INDEX `ACT_FK_EXE_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                     INDEX `ACT_FK_EXE_PARENT`(`PARENT_ID_` ASC) USING BTREE,
                                     INDEX `ACT_FK_EXE_SUPER`(`SUPER_EXEC_` ASC) USING BTREE,
                                     INDEX `ACT_FK_EXE_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PARENT` FOREIGN KEY (`PARENT_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_EXECUTION` ADD CONSTRAINT `ACT_FK_EXE_SUPER` FOREIGN KEY (`SUPER_EXEC_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_TASK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_TASK`;
CREATE TABLE `ACT_RU_TASK`  (
                                `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                `REV_` int(11) NULL DEFAULT NULL,
                                `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `CASE_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `DELEGATION_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                `PRIORITY_` int(11) NULL DEFAULT NULL,
                                `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                `DUE_DATE_` datetime NULL DEFAULT NULL,
                                `FOLLOW_UP_DATE_` datetime NULL DEFAULT NULL,
                                `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                PRIMARY KEY (`ID_`) USING BTREE,
                                INDEX `ACT_IDX_TASK_CREATE`(`CREATE_TIME_` ASC) USING BTREE,
                                INDEX `ACT_IDX_TASK_ASSIGNEE`(`ASSIGNEE_` ASC) USING BTREE,
                                INDEX `ACT_IDX_TASK_OWNER`(`OWNER_` ASC) USING BTREE,
                                INDEX `ACT_IDX_TASK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                INDEX `ACT_FK_TASK_EXE`(`EXECUTION_ID_` ASC) USING BTREE,
                                INDEX `ACT_FK_TASK_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                INDEX `ACT_FK_TASK_PROCDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
                                INDEX `ACT_FK_TASK_CASE_EXE`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
                                INDEX `ACT_FK_TASK_CASE_DEF`(`CASE_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_CASE_DEF` FOREIGN KEY (`CASE_DEF_ID_`) REFERENCES `ACT_RE_CASE_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_CASE_EXE` FOREIGN KEY (`CASE_EXECUTION_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_TASK` ADD CONSTRAINT `ACT_FK_TASK_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_FILTER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_FILTER`;
CREATE TABLE `ACT_RU_FILTER`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `REV_` int(11) NOT NULL,
                                  `RESOURCE_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `QUERY_` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `PROPERTIES_` longtext CHARACTER SET utf8 COLLATE utf8_bin NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_JOBDEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_JOBDEF`;
CREATE TABLE `ACT_RU_JOBDEF`  (
                                  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `REV_` int(11) NULL DEFAULT NULL,
                                  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `JOB_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                  `JOB_CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                  `JOB_PRIORITY_` bigint(20) NULL DEFAULT NULL,
                                  `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                  PRIMARY KEY (`ID_`) USING BTREE,
                                  INDEX `ACT_IDX_JOBDEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                  INDEX `ACT_IDX_JOBDEF_PROC_DEF_ID`(`PROC_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_METER_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_METER_LOG`;
CREATE TABLE `ACT_RU_METER_LOG`  (
                                     `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                     `NAME_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                     `REPORTER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `VALUE_` bigint(20) NULL DEFAULT NULL,
                                     `TIMESTAMP_` datetime NULL DEFAULT NULL,
                                     `MILLISECONDS_` bigint(20) NULL DEFAULT 0,
                                     PRIMARY KEY (`ID_`) USING BTREE,
                                     INDEX `ACT_IDX_METER_LOG_MS`(`MILLISECONDS_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_METER_LOG_NAME_MS`(`NAME_` ASC, `MILLISECONDS_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_METER_LOG_REPORT`(`NAME_` ASC, `REPORTER_` ASC, `MILLISECONDS_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_METER_LOG_TIME`(`TIMESTAMP_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_METER_LOG`(`NAME_` ASC, `TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_RU_TASK_METER_LOG
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_TASK_METER_LOG`;
CREATE TABLE `ACT_RU_TASK_METER_LOG`  (
                                          `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                          `ASSIGNEE_HASH_` bigint(20) NULL DEFAULT NULL,
                                          `TIMESTAMP_` datetime NULL DEFAULT NULL,
                                          PRIMARY KEY (`ID_`) USING BTREE,
                                          INDEX `ACT_IDX_TASK_METER_LOG_TIME`(`TIMESTAMP_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ACT_ID_MEMBERSHIP
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_MEMBERSHIP`;
CREATE TABLE `ACT_ID_MEMBERSHIP`  (
                                      `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      `GROUP_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                      PRIMARY KEY (`USER_ID_`, `GROUP_ID_`) USING BTREE,
                                      INDEX `ACT_FK_MEMB_GROUP`(`GROUP_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_ID_MEMBERSHIP` ADD CONSTRAINT `ACT_FK_MEMB_GROUP` FOREIGN KEY (`GROUP_ID_`) REFERENCES `ACT_ID_GROUP` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_MEMBERSHIP` ADD CONSTRAINT `ACT_FK_MEMB_USER` FOREIGN KEY (`USER_ID_`) REFERENCES `ACT_ID_USER` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_ID_TENANT_MEMBER
-- ----------------------------
DROP TABLE IF EXISTS `ACT_ID_TENANT_MEMBER`;
CREATE TABLE `ACT_ID_TENANT_MEMBER`  (
                                         `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                         `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                         `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         `GROUP_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                         PRIMARY KEY (`ID_`) USING BTREE,
                                         UNIQUE INDEX `ACT_UNIQ_TENANT_MEMB_USER`(`TENANT_ID_` ASC, `USER_ID_` ASC) USING BTREE,
                                         UNIQUE INDEX `ACT_UNIQ_TENANT_MEMB_GROUP`(`TENANT_ID_` ASC, `GROUP_ID_` ASC) USING BTREE,
                                         INDEX `ACT_FK_TENANT_MEMB_USER`(`USER_ID_` ASC) USING BTREE,
                                         INDEX `ACT_FK_TENANT_MEMB_GROUP`(`GROUP_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB` FOREIGN KEY (`TENANT_ID_`) REFERENCES `ACT_ID_TENANT` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB_GROUP` FOREIGN KEY (`GROUP_ID_`) REFERENCES `ACT_ID_GROUP` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_ID_TENANT_MEMBER` ADD CONSTRAINT `ACT_FK_TENANT_MEMB_USER` FOREIGN KEY (`USER_ID_`) REFERENCES `ACT_ID_USER` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_GE_BYTEARRAY
-- ----------------------------
DROP TABLE IF EXISTS `ACT_GE_BYTEARRAY`;
CREATE TABLE `ACT_GE_BYTEARRAY`  (
                                     `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                     `REV_` int(11) NULL DEFAULT NULL,
                                     `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `BYTES_` longblob NULL,
                                     `GENERATED_` tinyint(4) NULL DEFAULT NULL,
                                     `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `TYPE_` int(11) NULL DEFAULT NULL,
                                     `CREATE_TIME_` datetime NULL DEFAULT NULL,
                                     `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                     `REMOVAL_TIME_` datetime NULL DEFAULT NULL,
                                     PRIMARY KEY (`ID_`) USING BTREE,
                                     INDEX `ACT_FK_BYTEARR_DEPL`(`DEPLOYMENT_ID_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_BYTEARRAY_ROOT_PI`(`ROOT_PROC_INST_ID_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_BYTEARRAY_RM_TIME`(`REMOVAL_TIME_` ASC) USING BTREE,
                                     INDEX `ACT_IDX_BYTEARRAY_NAME`(`NAME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_GE_BYTEARRAY` ADD CONSTRAINT `ACT_FK_BYTEARR_DEPL` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `ACT_RE_DEPLOYMENT` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RE_DECISION_DEF
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RE_DECISION_DEF`;
CREATE TABLE `ACT_RE_DECISION_DEF`  (
                                        `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `REV_` int(11) NULL DEFAULT NULL,
                                        `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `VERSION_` int(11) NOT NULL,
                                        `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `DEC_REQ_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `DEC_REQ_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `HISTORY_TTL_` int(11) NULL DEFAULT NULL,
                                        `VERSION_TAG_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        PRIMARY KEY (`ID_`) USING BTREE,
                                        INDEX `ACT_IDX_DEC_DEF_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_DEC_DEF_REQ_ID`(`DEC_REQ_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RE_DECISION_DEF` ADD CONSTRAINT `ACT_FK_DEC_REQ` FOREIGN KEY (`DEC_REQ_ID_`) REFERENCES `ACT_RE_DECISION_REQ_DEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_BATCH
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_BATCH`;
CREATE TABLE `ACT_RU_BATCH`  (
                                 `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                 `REV_` int(11) NOT NULL,
                                 `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `TOTAL_JOBS_` int(11) NULL DEFAULT NULL,
                                 `JOBS_CREATED_` int(11) NULL DEFAULT NULL,
                                 `JOBS_PER_SEED_` int(11) NULL DEFAULT NULL,
                                 `INVOCATIONS_PER_JOB_` int(11) NULL DEFAULT NULL,
                                 `SEED_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `BATCH_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `MONITOR_JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                 `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 `CREATE_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                 PRIMARY KEY (`ID_`) USING BTREE,
                                 INDEX `ACT_IDX_BATCH_SEED_JOB_DEF`(`SEED_JOB_DEF_ID_` ASC) USING BTREE,
                                 INDEX `ACT_IDX_BATCH_MONITOR_JOB_DEF`(`MONITOR_JOB_DEF_ID_` ASC) USING BTREE,
                                 INDEX `ACT_IDX_BATCH_JOB_DEF`(`BATCH_JOB_DEF_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_JOB_DEF` FOREIGN KEY (`BATCH_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_MONITOR_JOB_DEF` FOREIGN KEY (`MONITOR_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_BATCH` ADD CONSTRAINT `ACT_FK_BATCH_SEED_JOB_DEF` FOREIGN KEY (`SEED_JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_EVENT_SUBSCR
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EVENT_SUBSCR`;
CREATE TABLE `ACT_RU_EVENT_SUBSCR`  (
                                        `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `REV_` int(11) NULL DEFAULT NULL,
                                        `EVENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `EVENT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `CREATED_` datetime NOT NULL,
                                        `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        PRIMARY KEY (`ID_`) USING BTREE,
                                        INDEX `ACT_IDX_EVENT_SUBSCR_CONFIG_`(`CONFIGURATION_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_EVENT_SUBSCR_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                        INDEX `ACT_FK_EVENT_EXEC`(`EXECUTION_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_EVENT_SUBSCR_EVT_NAME`(`EVENT_NAME_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EVENT_SUBSCR` ADD CONSTRAINT `ACT_FK_EVENT_EXEC` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_EXT_TASK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_EXT_TASK`;
CREATE TABLE `ACT_RU_EXT_TASK`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `REV_` int(11) NOT NULL,
                                    `WORKER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TOPIC_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `RETRIES_` int(11) NULL DEFAULT NULL,
                                    `ERROR_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ERROR_DETAILS_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
                                    `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
                                    `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
                                    `LAST_FAILURE_LOG_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    INDEX `ACT_IDX_EXT_TASK_TOPIC`(`TOPIC_NAME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_EXT_TASK_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_EXT_TASK_PRIORITY`(`PRIORITY_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_EXT_TASK_ERR_DETAILS`(`ERROR_DETAILS_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_EXT_TASK_EXEC`(`EXECUTION_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_EXT_TASK` ADD CONSTRAINT `ACT_FK_EXT_TASK_ERROR_DETAILS` FOREIGN KEY (`ERROR_DETAILS_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_EXT_TASK` ADD CONSTRAINT `ACT_FK_EXT_TASK_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_IDENTITYLINK
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_IDENTITYLINK`;
CREATE TABLE `ACT_RU_IDENTITYLINK`  (
                                        `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                        `REV_` int(11) NULL DEFAULT NULL,
                                        `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                        PRIMARY KEY (`ID_`) USING BTREE,
                                        INDEX `ACT_IDX_IDENT_LNK_USER`(`USER_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_IDENT_LNK_GROUP`(`GROUP_ID_` ASC) USING BTREE,
                                        INDEX `ACT_IDX_ATHRZ_PROCEDEF`(`PROC_DEF_ID_` ASC) USING BTREE,
                                        INDEX `ACT_FK_TSKASS_TASK`(`TASK_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_IDENTITYLINK` ADD CONSTRAINT `ACT_FK_ATHRZ_PROCEDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_IDENTITYLINK` ADD CONSTRAINT `ACT_FK_TSKASS_TASK` FOREIGN KEY (`TASK_ID_`) REFERENCES `ACT_RU_TASK` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_INCIDENT
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_INCIDENT`;
CREATE TABLE `ACT_RU_INCIDENT`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `REV_` int(11) NOT NULL,
                                    `INCIDENT_TIMESTAMP_` datetime NOT NULL,
                                    `INCIDENT_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `INCIDENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `FAILED_ACTIVITY_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ROOT_CAUSE_INCIDENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `ANNOTATION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    INDEX `ACT_IDX_INC_CONFIGURATION`(`CONFIGURATION_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_JOB_DEF`(`JOB_DEF_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_CAUSEINCID`(`CAUSE_INCIDENT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_EXID`(`EXECUTION_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_PROCDEFID`(`PROC_DEF_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_PROCINSTID`(`PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_INC_ROOTCAUSEINCID`(`ROOT_CAUSE_INCIDENT_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_CAUSE` FOREIGN KEY (`CAUSE_INCIDENT_ID_`) REFERENCES `ACT_RU_INCIDENT` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_JOB_DEF` FOREIGN KEY (`JOB_DEF_ID_`) REFERENCES `ACT_RU_JOBDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_PROCDEF` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `ACT_RE_PROCDEF` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_INCIDENT` ADD CONSTRAINT `ACT_FK_INC_RCAUSE` FOREIGN KEY (`ROOT_CAUSE_INCIDENT_ID_`) REFERENCES `ACT_RU_INCIDENT` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for ACT_RU_VARIABLE
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_VARIABLE`;
CREATE TABLE `ACT_RU_VARIABLE`  (
                                    `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `REV_` int(11) NULL DEFAULT NULL,
                                    `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `CASE_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `BATCH_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `DOUBLE_` double NULL DEFAULT NULL,
                                    `LONG_` bigint(20) NULL DEFAULT NULL,
                                    `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    `VAR_SCOPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                    `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                                    `IS_CONCURRENT_LOCAL_` tinyint(4) NULL DEFAULT NULL,
                                    `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID_`) USING BTREE,
                                    UNIQUE INDEX `ACT_UNIQ_VARIABLE`(`VAR_SCOPE_` ASC, `NAME_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_VARIABLE_TASK_ID`(`TASK_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_VARIABLE_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_VARIABLE_TASK_NAME_TYPE`(`TASK_ID_` ASC, `NAME_` ASC, `TYPE_` ASC) USING BTREE,
                                    INDEX `ACT_FK_VAR_EXE`(`EXECUTION_ID_` ASC) USING BTREE,
                                    INDEX `ACT_FK_VAR_PROCINST`(`PROC_INST_ID_` ASC) USING BTREE,
                                    INDEX `ACT_FK_VAR_BYTEARRAY`(`BYTEARRAY_ID_` ASC) USING BTREE,
                                    INDEX `ACT_IDX_BATCH_ID`(`BATCH_ID_` ASC) USING BTREE,
                                    INDEX `ACT_FK_VAR_CASE_EXE`(`CASE_EXECUTION_ID_` ASC) USING BTREE,
                                    INDEX `ACT_FK_VAR_CASE_INST`(`CASE_INST_ID_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_BATCH` FOREIGN KEY (`BATCH_ID_`) REFERENCES `ACT_RU_BATCH` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_BYTEARRAY` FOREIGN KEY (`BYTEARRAY_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_CASE_EXE` FOREIGN KEY (`CASE_EXECUTION_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_CASE_INST` FOREIGN KEY (`CASE_INST_ID_`) REFERENCES `ACT_RU_CASE_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_EXE` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `ACT_RU_VARIABLE` ADD CONSTRAINT `ACT_FK_VAR_PROCINST` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `ACT_RU_EXECUTION` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for ACT_RU_JOB
-- ----------------------------
DROP TABLE IF EXISTS `ACT_RU_JOB`;
CREATE TABLE `ACT_RU_JOB`  (
                               `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                               `REV_` int(11) NULL DEFAULT NULL,
                               `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                               `LOCK_EXP_TIME_` datetime NULL DEFAULT NULL,
                               `LOCK_OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
                               `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `PROCESS_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `PROCESS_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `RETRIES_` int(11) NULL DEFAULT NULL,
                               `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `FAILED_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `DUEDATE_` datetime NULL DEFAULT NULL,
                               `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `REPEAT_OFFSET_` bigint(20) NULL DEFAULT 0,
                               `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `SUSPENSION_STATE_` int(11) NOT NULL DEFAULT 1,
                               `JOB_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `PRIORITY_` bigint(20) NOT NULL DEFAULT 0,
                               `SEQUENCE_COUNTER_` bigint(20) NULL DEFAULT NULL,
                               `TENANT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               `CREATE_TIME_` datetime NULL DEFAULT NULL,
                               `LAST_FAILURE_LOG_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
                               PRIMARY KEY (`ID_`) USING BTREE,
                               INDEX `ACT_IDX_JOB_EXECUTION_ID`(`EXECUTION_ID_` ASC) USING BTREE,
                               INDEX `ACT_IDX_JOB_HANDLER`(`HANDLER_TYPE_`(100) ASC, `HANDLER_CFG_`(155) ASC) USING BTREE,
                               INDEX `ACT_IDX_JOB_PROCINST`(`PROCESS_INSTANCE_ID_` ASC) USING BTREE,
                               INDEX `ACT_IDX_JOB_TENANT_ID`(`TENANT_ID_` ASC) USING BTREE,
                               INDEX `ACT_IDX_JOB_JOB_DEF_ID`(`JOB_DEF_ID_` ASC) USING BTREE,
                               INDEX `ACT_FK_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_` ASC) USING BTREE,
                               INDEX `ACT_IDX_JOB_HANDLER_TYPE`(`HANDLER_TYPE_` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

ALTER TABLE `ACT_RU_JOB` ADD CONSTRAINT `ACT_FK_JOB_EXCEPTION` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `ACT_GE_BYTEARRAY` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE `act_hi_varinst` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;
ALTER TABLE `act_ru_variable` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;
ALTER TABLE `act_hi_detail` MODIFY COLUMN `TEXT_` text CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `LONG_`;