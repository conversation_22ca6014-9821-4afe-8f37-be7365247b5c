-- 添加后需要增加权限
-- ----------------------------
-- Table structure for api_location
-- ----------------------------
DROP TABLE IF EXISTS `api_location`;
CREATE TABLE `api_location`
(
	`api_location_id`    int(11)                                                NOT NULL AUTO_INCREMENT COMMENT '接口ID',
	`workspace_id`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`api_location_cn`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL,
	`api_location_name`  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT 'api location名称',
	`api_desc`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT 'api描叙',
	`uri_matches`        json                                                   NOT NULL COMMENT 'URI匹配规则',
	`app_service`        int(11)                                                NOT NULL COMMENT '业务服务',
	`app_instance_group` int(11)                                                NOT NULL COMMENT '业务节点组',
	`status`             tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`        datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`        datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`api_location_id`) USING BTREE,
	UNIQUE INDEX `apiapi_location_name-workspace_id` (`workspace_id` ASC, `api_location_name` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '业务接口表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for api_location_api_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_location_api_tag`;
CREATE TABLE `api_location_api_tag`
(
	`api_location_id` int(11)                                                NOT NULL COMMENT '接口ID',
	`api_tag_id`      int(11)                                                NOT NULL COMMENT '接口tag ID',
	`workspace_id`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`status`          tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`     datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`     datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`api_location_id`, `api_tag_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '1接口标签关系'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for api_tag
-- ----------------------------
DROP TABLE IF EXISTS `api_tag`;
CREATE TABLE `api_tag`
(
	`api_tag_id`    int(11)                                                NOT NULL AUTO_INCREMENT COMMENT '接口tag ID',
	`workspace_id`  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`api_tag`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '接口tag',
	`api_tag_name`  varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'api名称',
	`status`        tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`   datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`   datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`api_tag_id`) USING BTREE,
	UNIQUE INDEX `api_tag-workspace_id` (`workspace_id` ASC, `api_tag` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '2接口标签'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_instance_group
-- ----------------------------
DROP TABLE IF EXISTS `app_instance_group`;
CREATE TABLE `app_instance_group`
(
	`instance_group_id`   int(11)                                                NOT NULL AUTO_INCREMENT COMMENT '业务服务组ID',
	`workspace_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`instance_group_cn`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL,
	`instance_group_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '业务服务组名称',
	`instance_group_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '业务服务组描述',
	`advanced_switch`     tinyint(4)                                             NULL     DEFAULT 0 COMMENT '0:禁用  1:启用',
	`status`              tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`         datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`         datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`instance_group_id`) USING BTREE,
	UNIQUE INDEX `instance_group_name-workspace_id` (`workspace_id` ASC, `instance_group_name` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '业务节点组'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_instance_machine
-- ----------------------------
DROP TABLE IF EXISTS `app_instance_machine`;
CREATE TABLE `app_instance_machine`
(
	`instance_machine_id` int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '业务服务机器ID',
	`workspace_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`instance_group_id`   int(11)                                                 NOT NULL COMMENT '业务服务组ID',
	`zone_id`             bigint(20)                                              NOT NULL COMMENT '机房ID',
	`address`             varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务节点IP端口',
	`weight`              smallint(6)                                             NULL     DEFAULT NULL COMMENT '权重',
	`is_backup`           smallint(6)                                             NULL     DEFAULT NULL COMMENT '是否备份',
	`status`              tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`         datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`         datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`instance_machine_id`) USING BTREE,
	UNIQUE INDEX `address-instance_group_id` (`instance_group_id` ASC, `address` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '业务节点机器'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_instance_zone
-- ----------------------------
DROP TABLE IF EXISTS `app_instance_zone`;
CREATE TABLE `app_instance_zone`
(
	`instance_zone_id`        int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '业务节点机房ID',
	`workspace_id`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`instance_group_id`       int(11)                                                 NOT NULL COMMENT '业务服务组ID',
	`zone_id`                 bigint(20)                                              NOT NULL COMMENT '机房ID',
	`external_router_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '机房分流地址',
	`check_switch`            tinyint(4)                                              NULL     DEFAULT 0 COMMENT '0:禁用  1:启用',
	`check_interval`          smallint(6)                                             NULL     DEFAULT NULL COMMENT '检查间隔(毫秒)',
	`check_fall`              smallint(6)                                             NULL     DEFAULT NULL COMMENT '连续失败次数',
	`check_rise`              smallint(6)                                             NULL     DEFAULT NULL COMMENT '连续成功次数',
	`check_timeout`           smallint(6)                                             NULL     DEFAULT NULL COMMENT '检查超时时间(毫秒)',
	`check_port`              smallint(6)                                             NULL     DEFAULT NULL COMMENT '检查端口',
	`check_http_send`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT 'Http检查报文',
	`check_http_expect_alive` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT 'Http检查预期状态码',
	`status`                  tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`             varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`             datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`             datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`instance_zone_id`) USING BTREE,
	UNIQUE INDEX `instance_group_id-zone_id` (`zone_id` ASC, `instance_group_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '业务节点机房'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_service
-- ----------------------------
DROP TABLE IF EXISTS `app_service`;
CREATE TABLE `app_service`
(
	`app_service_id`   int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '业务服务ID',
	`workspace_id`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`app_service_cn`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '中文名',
	`app_service_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '业务服务名称',
	`app_service_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '业务服务描述',
	`listen`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '监听端口',
	`domain_names`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '域名列表',
	`extra_configs`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '扩展配置',
	`status`           tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`      datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`      datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`app_service_id`) USING BTREE,
	UNIQUE INDEX `app_service_name-workspace_id` (`workspace_id` ASC, `app_service_name` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '业务服务'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch
-- ----------------------------
DROP TABLE IF EXISTS `dispatch`;
CREATE TABLE `dispatch`
(
	`dispatch_id`     int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '调度ID',
	`workspace_id`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`dispatch_cn`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度中文',
	`dispatch_name`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度名称',
	`dispatch_desc`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度描叙',
	`api_location_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '接口ID',
	`api_tag`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '接口标签',
	`api_rule_ids`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流量规则列表',
	`status`          tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`is_emergency`    tinyint(4)                                              NULL     DEFAULT 0 COMMENT '0:常规调度  1:应急调度',
	`priority_level`  tinyint(4)                                              NULL     DEFAULT 0 COMMENT '调度优先级',
	`operator_id`     varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`     datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`     datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`dispatch_id`) USING BTREE,
	UNIQUE INDEX `dispatch_name-workspace_id` (`workspace_id` ASC, `dispatch_name` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_config
-- ----------------------------
DROP TABLE IF EXISTS `dispatch_config`;
CREATE TABLE `dispatch_config`
(
	`dispatch_config_id`  int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '调度配置ID',
	`dispatch_version`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '配置调度版本',
	`workspace_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`is_current_version`  tinyint(4)                                              NULL     DEFAULT 0 COMMENT 'DISABLE:否  ENABLE:是',
	`push_times`          tinyint(4)                                              NULL     DEFAULT 0 COMMENT '推送次数',
	`push_start_time`     datetime                                                NULL     DEFAULT NULL COMMENT '推送开始时间',
	`push_end_time`       datetime                                                NULL     DEFAULT NULL COMMENT '推送结束时间',
	`config_desc`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '配置说明',
	`app_instance_groups` json                                                    NULL COMMENT '业务节点组列表',
	`app_services`        json                                                    NULL COMMENT '业务服务列表',
	`app_api_locations`   json                                                    NULL COMMENT '业务接口列表',
	`app_rules`           json                                                    NULL COMMENT '业务流量规则',
	`dispatches`          json                                                    NULL COMMENT '调度列表',
	`api_tag_map`         json                                                    NULL COMMENT 'api标签map',
	`operator_id`         varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`       varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`status`              tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`create_time`         datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`         datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	`config_hex`          varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '配置调度版本指纹',
	PRIMARY KEY (`dispatch_config_id`) USING BTREE,
	UNIQUE INDEX `dispatch_version-workspace_id` (`workspace_id` ASC, `dispatch_version` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度配置'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_node
-- ----------------------------
DROP TABLE IF EXISTS `dispatch_node`;
CREATE TABLE `dispatch_node`
(
	`dispatch_node_id`           int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '调度节点ID',
	`dispatch_node_name`         varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度节点名称',
	`workspace_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`zone_id`                    bigint(20)                                              NOT NULL COMMENT '机房ID',
	`ip_port`                    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代理机房IP端口',
	`gray_dispatch_config_id`    int(11)                                                 NULL     DEFAULT NULL COMMENT '灰度配置调度版本ID',
	`dispatch_config_id`         int(11)                                                 NULL     DEFAULT NULL COMMENT '全局配置调度版本ID',
	`running_dispatch_config_id` int(11)                                                 NULL     DEFAULT NULL COMMENT '运行的调度版本ID',
	`gray_dispatch_version`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '灰度配置调度版本ID',
	`dispatch_version`           varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '全局配置调度版本',
	`running_dispatch_version`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '运行的调度版本',
	`status`                     tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`                varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`              varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`push_times`                 tinyint(4)                                              NULL     DEFAULT 0 COMMENT '推送次数',
	`push_time`                  datetime                                                NULL     DEFAULT NULL COMMENT '推送时间',
	`create_time`                datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`                datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`dispatch_node_id`) USING BTREE,
	UNIQUE INDEX `dispatch_node_id-zone_id-ip_port` (`workspace_id` ASC, `zone_id` ASC, `ip_port` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度节点'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_push_history
-- ----------------------------
DROP TABLE IF EXISTS `dispatch_push_history`;
CREATE TABLE `dispatch_push_history`
(
	`push_history_id`    int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '推送历史ID',
	`dispatch_config_id` int(11)                                                 NOT NULL COMMENT '调度配置ID',
	`push_times`         tinyint(4)                                              NULL DEFAULT 0 COMMENT '推送次数',
	`dispatch_node_id`   int(11)                                                 NOT NULL COMMENT '调度节点ID',
	`dispatch_node_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '调度节点名称',
	`zone_id`            bigint(20)                                              NOT NULL COMMENT '机房ID',
	`ip_port`            varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '代理机房IP端口',
	`push_start_time`    datetime                                                NULL DEFAULT NULL COMMENT '推送开始时间',
	`push_end_time`      datetime                                                NULL DEFAULT NULL COMMENT '推送结束时间',
	`config_desc`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配置说明',
	`operator_id`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '操作员',
	`operator_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '操作员名称',
	`push_status`        tinyint(4)                                              NULL DEFAULT NULL COMMENT '0:推送异常  1:推送成功',
	`msg_cd`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL DEFAULT NULL COMMENT '推送agent返回消息码',
	`msg_info`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '推送agent返回消息信息',
	PRIMARY KEY (`push_history_id`) USING BTREE,
	UNIQUE INDEX `dispatch_config_id-dispatch_node_id-push_times` (`dispatch_config_id` ASC, `dispatch_node_id` ASC, `push_times` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度推送历史'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_rule
-- ----------------------------
-- 调度规则
DROP TABLE IF EXISTS `dispatch_rule`;
CREATE TABLE `dispatch_rule`
(
	`dispatch_rule_id`   int(11)                                                NOT NULL AUTO_INCREMENT COMMENT '调度规则ID',
	`workspace_id`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`dispatch_rule_cn`   varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL,
	`dispatch_rule_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '业务规则名称',
	`dispatch_rule_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '业务规则描述',
	`dispatch_rule_type` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '分流类型',
	`status`             tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`        datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`        datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`dispatch_rule_id`) USING BTREE,
	UNIQUE INDEX `dispatch_rule_name-workspace_id` (`workspace_id` ASC, `dispatch_rule_name` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度规则'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_test
-- ----------------------------
DROP TABLE IF EXISTS `dispatch_test`;
CREATE TABLE `dispatch_test`
(
	`dispatch_test_id`   int(11)                                                 NOT NULL AUTO_INCREMENT COMMENT '调度测试ID',
	`workspace_id`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '项目空间ID',
	`dispatch_id`        int(11)                                                 NULL     DEFAULT NULL COMMENT '调度ID',
	`dispatch_test_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度测试名称',
	`scheme`             varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT 'http' COMMENT '请求类型',
	`address`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '请求地址',
	`port`               int(11)                                                 NULL     DEFAULT 0 COMMENT '请求端口',
	`host`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '请求地址',
	`app_service_id`     int(11)                                                 NULL     DEFAULT NULL COMMENT '业务服务ID',
	`method`             varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '参数名/取值表达式',
	`urlPath`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '请求路径',
	`queryParams`        json                                                    NULL COMMENT '查询参数',
	`cookies`            json                                                    NULL COMMENT 'Cookie',
	`requestHeaders`     json                                                    NULL COMMENT '请求头',
	`requestBody`        text CHARACTER SET utf8 COLLATE utf8_general_ci         NULL COMMENT '请求体',
	`status`             tinyint(4)                                              NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`        datetime                                                NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`        datetime                                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	`dispatch_test_desc` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NULL     DEFAULT NULL COMMENT '调度测试描述',
	PRIMARY KEY (`dispatch_test_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度规则表达式'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dispatch_zone
-- ----------------------------
DROP TABLE IF EXISTS `dispatch_zone`;
CREATE TABLE `dispatch_zone`
(
	`id`            bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '机房id',
	`workspace_id`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目id',
	`zone_name`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '机房名称',
	`zone_label`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '机房标识',
	`operator_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '操作员Id',
	`operator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '操作员名称',
	`create_time`   datetime                                                      NOT NULL COMMENT '创建时间',
	`update_time`   datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注信息',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `zone_label_unique` (`workspace_id` ASC, `zone_label` ASC) USING BTREE COMMENT '机房标签在项目中唯一'
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for rule_expression
-- ----------------------------
DROP TABLE IF EXISTS `rule_expression`;
CREATE TABLE `rule_expression`
(
	`rule_expression_id` int(11)                                                NOT NULL AUTO_INCREMENT COMMENT '调度规则表达式ID',
	`workspace_id`       varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '项目空间ID',
	`dispatch_rule_id`   int(11)                                                NOT NULL COMMENT '调度规则ID',
	`dispatch_rule_type` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分流类型',
	`priority_level`     tinyint(4)                                             NULL     DEFAULT 0 COMMENT '规则优先级',
	`param_extractor`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '参数名/取值表达式',
	`calc_operator`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '计算操作符',
	`result_value`       varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '参数值',
	`zone_weights`       json                                                   NOT NULL COMMENT '分机房分流权重（机房标识：权重）',
	`status`             tinyint(4)                                             NULL     DEFAULT 1 COMMENT '0:禁用  1:启用',
	`operator_id`        varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员',
	`operator_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL     DEFAULT NULL COMMENT '操作员名称',
	`create_time`        datetime                                               NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time`        datetime                                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
	PRIMARY KEY (`rule_expression_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '调度规则表达式'
  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO `sys_menu` VALUES (189046, 0, '应用流量调度', NULL, '/applicationTrafficScheduling', 'applicationTrafficScheduling:admin', 'D', 'ForkOutlined', 1, NULL, '2024-06-21 10:40:45.000000', '2024-07-30 15:01:54.154359', '{\"noCache\":\"false\",\"hiddenTenantHeader\":\"true\",\"name\":\"Application Traffic Scheduling\"}', 'routerView', '', 'ApplicationTrafficScheduling', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189047, 189046, '机房管理', NULL, '/serverZoneManagement/index', 'ServerZoneManagement:admin', 'M', 'DatabaseOutlined', 1, NULL, '2024-06-21 11:36:06.000000', '2024-06-21 11:36:06.000000', '{\"noCache\":false}', '/transaction/ServerZoneManagement/index', NULL, 'ServerZoneManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189053, 189046, '版本管理', NULL, '/versionManagement/index', 'VersionManagement:admin', 'M', 'QrcodeOutlined', 9, NULL, '2024-06-21 17:48:44.000000', '2024-06-21 17:48:44.000000', '{\"noCache\":false}', '/transaction/VersionManagement/index', NULL, 'VersionManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189054, 189046, '调度节点管理', NULL, '/schedulingNodeManagement/index', 'SchedulingNodeManagement:admin', 'M', 'PartitionOutlined', 3, NULL, '2024-06-21 17:51:04.000000', '2024-06-21 17:51:04.000000', '{\"noCache\":false}', '/transaction/SchedulingNodeManagement/index', NULL, 'SchedulingNodeManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189056, 189046, '业务节点管理', NULL, '/businessNodeManagement', 'BusinessNodeManagement:admin', 'M', 'UngroupOutlined', 4, NULL, '2024-06-21 17:59:12.000000', '2024-06-21 17:59:12.000000', '{\"noCache\":false}', 'routerView', '/businessNodeManagement/index', 'BusinessNodeManagement', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189057, 189046, '业务服务管理', NULL, '/businessServiceManagement/index', 'BusinessServiceManagement:admin', 'M', 'QrcodeOutlined', 5, NULL, '2024-06-21 17:59:58.000000', '2024-06-21 17:59:58.000000', '{\"noCache\":false}', '/transaction/BusinessServiceManagement/index', NULL, 'BusinessServiceManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189058, 189046, '规则管理', NULL, '/rulesManager', 'RulesManager:admin', 'M', 'GatewayOutlined', 6, NULL, '2024-06-21 18:00:58.000000', '2024-06-21 18:00:58.000000', '{\"noCache\":false}', 'routerView', '/rulesManager/index', 'RulesManager', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189059, 189046, '接口管理', NULL, '/interfacesManagement/index', 'InterfacesManagement:admin', 'M', 'NodeIndexOutlined', 6, NULL, '2024-06-21 18:01:53.000000', '2024-06-21 18:01:53.000000', '{\"noCache\":false}', '/transaction/InterfacesManagement/index', NULL, 'InterfacesManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189060, 189046, '常规调度管理', NULL, '/regularScheduling/index', 'RegularScheduling:admin', 'M', 'CloudServerOutlined', 7, NULL, '2024-06-21 18:02:34.000000', '2024-06-21 18:02:34.000000', '{\"noCache\":false}', '/transaction/RegularScheduling/index', NULL, 'RegularScheduling', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (189063, 189047, '查询', NULL, '/', 'dispatch:dispatch-zone:query', 'B', NULL, 0, NULL, '2024-06-25 11:27:18.000000', '2024-06-25 11:27:18.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189064, 189047, '删除', NULL, '/', 'dispatch:dispatch-zone:delete', 'B', NULL, 0, NULL, '2024-06-25 11:32:50.000000', '2024-06-25 11:32:50.000000', '{}', 'routerView', NULL, 'delete', NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189065, 189047, '更新', NULL, '/', 'dispatch:dispatch-zone:update', 'B', NULL, 0, NULL, '2024-06-25 11:33:27.000000', '2024-06-25 11:33:27.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189066, 189047, '新增', NULL, '/', 'dispatch:dispatch-zone:add', 'B', NULL, 0, NULL, '2024-06-25 11:33:55.000000', '2024-06-25 11:33:55.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189068, 189056, '业务节点管理新增', NULL, '/businessNodeManagement/edit', 'BusinessNodeManagementEdit:admin', 'M', NULL, 1, NULL, '2024-06-25 17:59:05.000000', '2024-06-25 17:59:05.000000', '{\"noCache\":false}', '/transaction/BusinessNodeManagement/edit', NULL, 'BusinessNodeManagementEdit', NULL, 0, 1, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189069, 189056, '业务节点管理查询', NULL, '/businessNodeManagement/index', 'BusinessNodeManagementQuery:admin', 'M', NULL, 0, NULL, '2024-06-25 18:00:09.000000', '2024-06-25 18:00:09.000000', '{\"noCache\":false}', '/transaction/BusinessNodeManagement/index', NULL, 'BusinessNodeManagementQuery', NULL, 0, 1, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189070, 189058, '规则管理查询', NULL, '/rulesManager/index', 'RulesManagerQuery:admin', 'M', NULL, 0, NULL, '2024-06-26 09:35:42.000000', '2024-06-26 09:35:42.000000', '{\"noCache\":false}', '/transaction/RulesManager/index', NULL, 'RulesManagerQuery', NULL, 0, 0, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189071, 189058, '规则管理新增', NULL, '/rulesManager/edit', 'RulesManagerEdit:admin', 'M', NULL, 1, NULL, '2024-06-26 09:36:48.275166', '2024-06-26 09:36:48.275166', '{\"noCache\":false}', '/transaction/RulesManager/edit', NULL, 'RulesManagerEdit', NULL, 0, 0, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189072, 4, '启用', NULL, 'sys:user:enable', 'sys:user:enable', 'B', NULL, 0, NULL, '2024-07-01 10:12:16.444094', '2024-07-01 10:12:16.444094', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189073, 4, '禁用', NULL, 'sys:user:disable', 'sys:user:disable', 'B', NULL, 0, NULL, '2024-07-01 10:12:35.387455', '2024-07-01 10:12:35.387455', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189076, 189046, '调度测试', NULL, '/schedulingTesting/index', 'SchedulingTesting:admin', 'M', 'BuildOutlined', 10, NULL, '2024-07-16 10:07:44.211870', '2024-07-16 10:07:44.211870', '{}', '/transaction/SchedulingTesting/index', NULL, 'SchedulingTesting', NULL, 0, 0, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189079, 189054, '查询', NULL, 'DispatchNodeController.getDispatchNodePage', 'dispatch:dispatch-node:query', 'B', NULL, 0, NULL, '2024-07-30 09:21:50.000000', '2024-07-30 09:21:50.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189080, 189054, '新增', NULL, '/', 'dispatch:dispatch-node:add', 'B', NULL, 0, NULL, '2024-07-30 09:22:48.399193', '2024-07-30 09:22:48.399193', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189081, 189054, '删除', NULL, '/', 'dispatch:dispatch-node:delete', 'B', NULL, 0, NULL, '2024-07-30 09:23:06.290127', '2024-07-30 09:23:06.290127', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189082, 189054, '更新', NULL, '/', 'dispatch:dispatch-node:update', 'B', NULL, 0, NULL, '2024-07-30 09:23:23.397796', '2024-07-30 09:23:23.397796', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189083, 189069, '查询', NULL, '/', 'dispatch:instance-group:query', 'B', NULL, 0, NULL, '2024-07-30 09:26:22.000000', '2024-07-30 09:26:22.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189084, 189069, '删除', NULL, '/', 'dispatch:instance-group:delete', 'B', NULL, 0, NULL, '2024-07-30 09:26:54.000000', '2024-07-30 09:26:54.000000', '{}', NULL, NULL, '', NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189085, 189068, '更新', NULL, '/', 'dispatch:instance-group:update', 'B', NULL, 0, NULL, '2024-07-30 09:27:09.000000', '2024-07-30 09:27:09.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189086, 189068, '新增', NULL, '/', 'dispatch:instance-group:add', 'B', NULL, 0, NULL, '2024-07-30 09:28:08.000000', '2024-07-30 09:28:08.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189087, 189057, '新增', NULL, '/', 'dispatch:api-service:add', 'B', NULL, 0, NULL, '2024-07-30 09:28:57.626679', '2024-07-30 09:28:57.626679', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189088, 189057, '查询', NULL, '/', 'dispatch:api-service:query', 'B', NULL, 0, NULL, '2024-07-30 09:29:14.113413', '2024-07-30 09:29:14.113413', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189089, 189057, '删除', NULL, '/', 'dispatch:api-service:delete', 'B', NULL, 0, NULL, '2024-07-30 09:29:31.378163', '2024-07-30 09:29:31.378163', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189090, 189057, '更新', NULL, '/', 'dispatch:api-service:update', 'B', NULL, 0, NULL, '2024-07-30 09:29:50.387460', '2024-07-30 09:29:50.387460', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189091, 189070, '查询', NULL, '/', 'dispatch:dispatch-rule:query', 'B', NULL, 0, NULL, '2024-07-30 09:30:27.926815', '2024-07-30 09:30:27.926815', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189092, 189070, '删除', NULL, '/', 'dispatch:dispatch-rule:delete', 'B', NULL, 0, NULL, '2024-07-30 09:30:44.754066', '2024-07-30 09:30:44.754066', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189093, 189071, '更新', NULL, '/', 'dispatch:dispatch-rule:update', 'B', NULL, 0, NULL, '2024-07-30 09:31:05.000000', '2024-07-30 09:31:05.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189094, 189071, '新增', NULL, '/', 'dispatch:dispatch-rule:add', 'B', NULL, 0, NULL, '2024-07-30 09:31:32.000000', '2024-07-30 09:31:32.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189095, 189059, '查询', NULL, '/', 'dispatch:api:query', 'B', NULL, 0, NULL, '2024-07-30 09:31:59.149487', '2024-07-30 09:31:59.149487', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189096, 189059, '删除', NULL, '/', 'dispatch:api:delete', 'B', NULL, 0, NULL, '2024-07-30 09:32:10.029489', '2024-07-30 09:32:10.029489', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189097, 189059, '更新', NULL, '/', 'dispatch:api:update', 'B', NULL, 0, NULL, '2024-07-30 09:32:22.911907', '2024-07-30 09:32:22.911907', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189098, 189059, '新增', NULL, '/', 'dispatch:api:add', 'B', NULL, 0, NULL, '2024-07-30 09:32:34.378547', '2024-07-30 09:32:34.378547', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189099, 189053, '快速创建', NULL, '/', 'dispatch:dispatch-config:generate', 'B', NULL, 0, NULL, '2024-07-30 09:33:42.697075', '2024-07-30 09:33:42.697075', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189100, 189053, '发布', NULL, '/', 'dispatch:dispatch-config:publish', 'B', NULL, 0, NULL, '2024-07-30 09:33:58.937551', '2024-07-30 09:33:58.937551', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189101, 189053, '推送', NULL, '/', 'dispatch:dispatch-config:publish-push', 'B', NULL, 0, NULL, '2024-07-30 09:34:22.046610', '2024-07-30 09:34:22.046610', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189102, 189053, '删除', NULL, '/', 'dispatch:dispatch-config:delete', 'B', NULL, 0, NULL, '2024-07-30 09:34:35.647833', '2024-07-30 09:34:35.647833', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189103, 189053, '查询', NULL, '/', 'dispatch:dispatch-config:query', 'B', NULL, 0, NULL, '2024-07-30 09:35:00.890977', '2024-07-30 09:35:00.890977', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189104, 189053, '详情', NULL, '/', 'dispatch:dispatch-config:info', 'B', NULL, 0, NULL, '2024-07-30 09:35:21.000000', '2024-07-30 09:35:21.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189105, 189060, '查询', NULL, '/', 'dispatch:dispatch:query', 'B', NULL, 0, NULL, '2024-07-30 09:35:43.027275', '2024-07-30 09:35:43.027275', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189106, 189060, '删除', NULL, '/', 'dispatch:dispatch:delete', 'B', NULL, 0, NULL, '2024-07-30 09:35:57.226497', '2024-07-30 09:35:57.226497', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189107, 189060, '更新', NULL, '/', 'dispatch:dispatch:update', 'B', NULL, 0, NULL, '2024-07-30 09:36:11.042428', '2024-07-30 09:36:11.042428', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189108, 189060, '新增', NULL, '/', 'dispatch:dispatch:add', 'B', NULL, 0, NULL, '2024-07-30 09:36:23.735673', '2024-07-30 09:36:23.735673', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189109, 189077, '查询', NULL, '/', 'dispatch:dispatch-emergency:query', 'B', NULL, 0, NULL, '2024-07-30 09:36:46.980658', '2024-07-30 09:36:46.980658', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189110, 189077, '删除', NULL, '/', 'dispatch:dispatch-emergency:delete', 'B', NULL, 0, NULL, '2024-07-30 09:37:03.545009', '2024-07-30 09:37:03.545009', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189111, 189077, '更新', NULL, '/', 'dispatch:dispatch-emergency:update', 'B', NULL, 0, NULL, '2024-07-30 09:37:17.821058', '2024-07-30 09:37:17.821058', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189112, 189077, '新增', NULL, '/', 'dispatch:dispatch-emergency:add', 'B', NULL, 0, NULL, '2024-07-30 09:37:27.988932', '2024-07-30 09:37:27.988932', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189113, 189077, '禁用/启用', NULL, '/', 'dispatch:dispatch-emergency:state', 'B', NULL, 0, NULL, '2024-07-30 09:37:51.000000', '2024-07-30 09:37:51.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189115, 189060, '禁用/启用', NULL, '/', 'dispatch:dispatch:state', 'B', NULL, 0, NULL, '2024-07-30 09:38:16.000000', '2024-07-30 09:38:16.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189117, 189076, '查询', NULL, '/', 'dispatch:dispatch-test:query', 'B', NULL, 0, NULL, '2024-07-30 09:38:49.493435', '2024-07-30 09:38:49.493435', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189118, 189076, '删除', NULL, '/', 'dispatch:dispatch-test:delete', 'B', NULL, 0, NULL, '2024-07-30 09:39:02.191294', '2024-07-30 09:39:02.191294', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189119, 189076, '更新', NULL, '/', 'dispatch:dispatch-test:update', 'B', NULL, 0, NULL, '2024-07-30 09:39:14.363463', '2024-07-30 09:39:14.363463', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189120, 189076, '新增', NULL, '/', 'dispatch:dispatch-test:add', 'B', NULL, 0, NULL, '2024-07-30 09:39:25.349893', '2024-07-30 09:39:25.349893', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189121, 189076, '执行', NULL, '/', 'dispatch:dispatch-test:call', 'B', NULL, 0, NULL, '2024-07-30 09:39:42.347456', '2024-07-30 09:39:42.347456', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189134, 189059, '新增标签', NULL, '/', 'dispatch:api-tag:add', 'B', NULL, 0, NULL, '2024-07-30 09:56:13.000000', '2024-07-30 09:56:13.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189135, 189059, '删除标签', NULL, '/', 'dispatch:api-tag:delete', 'B', NULL, 0, NULL, '2024-07-30 09:56:30.000000', '2024-07-30 09:56:30.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189136, 189068, '详情', NULL, '/', 'dispatch:instance-group:info', 'B', NULL, 0, NULL, '2024-07-30 14:09:58.397176', '2024-07-30 14:09:58.397176', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189137, 189071, '详情', NULL, '/', 'dispatch:dispatch-rule:info', 'B', NULL, 0, NULL, '2024-07-30 14:10:49.857849', '2024-07-30 14:10:49.857849', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);

INSERT INTO `sys_role_menu` VALUES ('1-189046', 1, 189046);
INSERT INTO `sys_role_menu` VALUES ('1-189047', 1, 189047);
INSERT INTO `sys_role_menu` VALUES ('1-189053', 1, 189053);
INSERT INTO `sys_role_menu` VALUES ('1-189054', 1, 189054);
INSERT INTO `sys_role_menu` VALUES ('1-189056', 1, 189056);
INSERT INTO `sys_role_menu` VALUES ('1-189057', 1, 189057);
INSERT INTO `sys_role_menu` VALUES ('1-189058', 1, 189058);
INSERT INTO `sys_role_menu` VALUES ('1-189059', 1, 189059);
INSERT INTO `sys_role_menu` VALUES ('1-189060', 1, 189060);
INSERT INTO `sys_role_menu` VALUES ('1-189063', 1, 189063);
INSERT INTO `sys_role_menu` VALUES ('1-189064', 1, 189064);
INSERT INTO `sys_role_menu` VALUES ('1-189065', 1, 189065);
INSERT INTO `sys_role_menu` VALUES ('1-189066', 1, 189066);
INSERT INTO `sys_role_menu` VALUES ('1-189068', 1, 189068);
INSERT INTO `sys_role_menu` VALUES ('1-189069', 1, 189069);
INSERT INTO `sys_role_menu` VALUES ('1-189070', 1, 189070);
INSERT INTO `sys_role_menu` VALUES ('1-189071', 1, 189071);
INSERT INTO `sys_role_menu` VALUES ('1-189076', 1, 189076);
INSERT INTO `sys_role_menu` VALUES ('1-189077', 1, 189077);
INSERT INTO `sys_role_menu` VALUES ('1-189079', 1, 189079);
INSERT INTO `sys_role_menu` VALUES ('1-189080', 1, 189080);
INSERT INTO `sys_role_menu` VALUES ('1-189081', 1, 189081);
INSERT INTO `sys_role_menu` VALUES ('1-189082', 1, 189082);
INSERT INTO `sys_role_menu` VALUES ('1-189083', 1, 189083);
INSERT INTO `sys_role_menu` VALUES ('1-189084', 1, 189084);
INSERT INTO `sys_role_menu` VALUES ('1-189085', 1, 189085);
INSERT INTO `sys_role_menu` VALUES ('1-189086', 1, 189086);
INSERT INTO `sys_role_menu` VALUES ('1-189087', 1, 189087);
INSERT INTO `sys_role_menu` VALUES ('1-189088', 1, 189088);
INSERT INTO `sys_role_menu` VALUES ('1-189089', 1, 189089);
INSERT INTO `sys_role_menu` VALUES ('1-189090', 1, 189090);
INSERT INTO `sys_role_menu` VALUES ('1-189091', 1, 189091);
INSERT INTO `sys_role_menu` VALUES ('1-189092', 1, 189092);
INSERT INTO `sys_role_menu` VALUES ('1-189093', 1, 189093);
INSERT INTO `sys_role_menu` VALUES ('1-189094', 1, 189094);
INSERT INTO `sys_role_menu` VALUES ('1-189095', 1, 189095);
INSERT INTO `sys_role_menu` VALUES ('1-189096', 1, 189096);
INSERT INTO `sys_role_menu` VALUES ('1-189097', 1, 189097);
INSERT INTO `sys_role_menu` VALUES ('1-189098', 1, 189098);
INSERT INTO `sys_role_menu` VALUES ('1-189099', 1, 189099);
INSERT INTO `sys_role_menu` VALUES ('1-189100', 1, 189100);
INSERT INTO `sys_role_menu` VALUES ('1-189101', 1, 189101);
INSERT INTO `sys_role_menu` VALUES ('1-189102', 1, 189102);
INSERT INTO `sys_role_menu` VALUES ('1-189103', 1, 189103);
INSERT INTO `sys_role_menu` VALUES ('1-189104', 1, 189104);
INSERT INTO `sys_role_menu` VALUES ('1-189105', 1, 189105);
INSERT INTO `sys_role_menu` VALUES ('1-189106', 1, 189106);
INSERT INTO `sys_role_menu` VALUES ('1-189107', 1, 189107);
INSERT INTO `sys_role_menu` VALUES ('1-189108', 1, 189108);
INSERT INTO `sys_role_menu` VALUES ('1-189109', 1, 189109);
INSERT INTO `sys_role_menu` VALUES ('1-189110', 1, 189110);
INSERT INTO `sys_role_menu` VALUES ('1-189111', 1, 189111);
INSERT INTO `sys_role_menu` VALUES ('1-189112', 1, 189112);
INSERT INTO `sys_role_menu` VALUES ('1-189113', 1, 189113);
INSERT INTO `sys_role_menu` VALUES ('1-189115', 1, 189115);
INSERT INTO `sys_role_menu` VALUES ('1-189117', 1, 189117);
INSERT INTO `sys_role_menu` VALUES ('1-189118', 1, 189118);
INSERT INTO `sys_role_menu` VALUES ('1-189119', 1, 189119);
INSERT INTO `sys_role_menu` VALUES ('1-189120', 1, 189120);
INSERT INTO `sys_role_menu` VALUES ('1-189121', 1, 189121);
INSERT INTO `sys_role_menu` VALUES ('1-189134', 1, 189134);
INSERT INTO `sys_role_menu` VALUES ('1-189135', 1, 189135);
INSERT INTO `sys_role_menu` VALUES ('1-189136', 1, 189136);
INSERT INTO `sys_role_menu` VALUES ('1-189137', 1, 189137);


INSERT INTO `sys_role_menu` VALUES ('1076-189046', 1076, 189046);
INSERT INTO `sys_role_menu` VALUES ('1076-189047', 1076, 189047);
INSERT INTO `sys_role_menu` VALUES ('1076-189053', 1076, 189053);
INSERT INTO `sys_role_menu` VALUES ('1076-189054', 1076, 189054);
INSERT INTO `sys_role_menu` VALUES ('1076-189056', 1076, 189056);
INSERT INTO `sys_role_menu` VALUES ('1076-189057', 1076, 189057);
INSERT INTO `sys_role_menu` VALUES ('1076-189058', 1076, 189058);
INSERT INTO `sys_role_menu` VALUES ('1076-189059', 1076, 189059);
INSERT INTO `sys_role_menu` VALUES ('1076-189060', 1076, 189060);
INSERT INTO `sys_role_menu` VALUES ('1076-189063', 1076, 189063);
INSERT INTO `sys_role_menu` VALUES ('1076-189064', 1076, 189064);
INSERT INTO `sys_role_menu` VALUES ('1076-189065', 1076, 189065);
INSERT INTO `sys_role_menu` VALUES ('1076-189066', 1076, 189066);
INSERT INTO `sys_role_menu` VALUES ('1076-189068', 1076, 189068);
INSERT INTO `sys_role_menu` VALUES ('1076-189069', 1076, 189069);
INSERT INTO `sys_role_menu` VALUES ('1076-189070', 1076, 189070);
INSERT INTO `sys_role_menu` VALUES ('1076-189071', 1076, 189071);
INSERT INTO `sys_role_menu` VALUES ('1076-189076', 1076, 189076);
INSERT INTO `sys_role_menu` VALUES ('1076-189077', 1076, 189077);
INSERT INTO `sys_role_menu` VALUES ('1076-189079', 1076, 189079);
INSERT INTO `sys_role_menu` VALUES ('1076-189080', 1076, 189080);
INSERT INTO `sys_role_menu` VALUES ('1076-189081', 1076, 189081);
INSERT INTO `sys_role_menu` VALUES ('1076-189082', 1076, 189082);
INSERT INTO `sys_role_menu` VALUES ('1076-189083', 1076, 189083);
INSERT INTO `sys_role_menu` VALUES ('1076-189084', 1076, 189084);
INSERT INTO `sys_role_menu` VALUES ('1076-189085', 1076, 189085);
INSERT INTO `sys_role_menu` VALUES ('1076-189086', 1076, 189086);
INSERT INTO `sys_role_menu` VALUES ('1076-189087', 1076, 189087);
INSERT INTO `sys_role_menu` VALUES ('1076-189088', 1076, 189088);
INSERT INTO `sys_role_menu` VALUES ('1076-189089', 1076, 189089);
INSERT INTO `sys_role_menu` VALUES ('1076-189090', 1076, 189090);
INSERT INTO `sys_role_menu` VALUES ('1076-189091', 1076, 189091);
INSERT INTO `sys_role_menu` VALUES ('1076-189092', 1076, 189092);
INSERT INTO `sys_role_menu` VALUES ('1076-189093', 1076, 189093);
INSERT INTO `sys_role_menu` VALUES ('1076-189094', 1076, 189094);
INSERT INTO `sys_role_menu` VALUES ('1076-189095', 1076, 189095);
INSERT INTO `sys_role_menu` VALUES ('1076-189096', 1076, 189096);
INSERT INTO `sys_role_menu` VALUES ('1076-189097', 1076, 189097);
INSERT INTO `sys_role_menu` VALUES ('1076-189098', 1076, 189098);
INSERT INTO `sys_role_menu` VALUES ('1076-189099', 1076, 189099);
INSERT INTO `sys_role_menu` VALUES ('1076-189100', 1076, 189100);
INSERT INTO `sys_role_menu` VALUES ('1076-189101', 1076, 189101);
INSERT INTO `sys_role_menu` VALUES ('1076-189102', 1076, 189102);
INSERT INTO `sys_role_menu` VALUES ('1076-189103', 1076, 189103);
INSERT INTO `sys_role_menu` VALUES ('1076-189104', 1076, 189104);
INSERT INTO `sys_role_menu` VALUES ('1076-189105', 1076, 189105);
INSERT INTO `sys_role_menu` VALUES ('1076-189106', 1076, 189106);
INSERT INTO `sys_role_menu` VALUES ('1076-189107', 1076, 189107);
INSERT INTO `sys_role_menu` VALUES ('1076-189108', 1076, 189108);
INSERT INTO `sys_role_menu` VALUES ('1076-189109', 1076, 189109);
INSERT INTO `sys_role_menu` VALUES ('1076-189110', 1076, 189110);
INSERT INTO `sys_role_menu` VALUES ('1076-189111', 1076, 189111);
INSERT INTO `sys_role_menu` VALUES ('1076-189112', 1076, 189112);
INSERT INTO `sys_role_menu` VALUES ('1076-189113', 1076, 189113);
INSERT INTO `sys_role_menu` VALUES ('1076-189115', 1076, 189115);
INSERT INTO `sys_role_menu` VALUES ('1076-189117', 1076, 189117);
INSERT INTO `sys_role_menu` VALUES ('1076-189118', 1076, 189118);
INSERT INTO `sys_role_menu` VALUES ('1076-189119', 1076, 189119);
INSERT INTO `sys_role_menu` VALUES ('1076-189120', 1076, 189120);
INSERT INTO `sys_role_menu` VALUES ('1076-189121', 1076, 189121);
INSERT INTO `sys_role_menu` VALUES ('1076-189134', 1076, 189134);
INSERT INTO `sys_role_menu` VALUES ('1076-189135', 1076, 189135);
INSERT INTO `sys_role_menu` VALUES ('1076-189136', 1076, 189136);
INSERT INTO `sys_role_menu` VALUES ('1076-189137', 1076, 189137);

INSERT INTO `sys_dict` VALUES ('7030BC94B6294A7BBBC8231B596B8F45', 'serviceStatus', '服务状态', 'parent', '业务服务管理-服务状态', 22, '0', '1', '2024-06-26 11:21:14', '1', '2024-06-26 11:21:14', '手动添加', '0');
INSERT INTO `sys_dict` VALUES ('79BDA71B83A4423296296743493A827C', 'dispatchOverrider', '调度映射', 'parent', '首页流量调度映射', 28, '0', '1', '2024-08-28 16:13:41', '1', '2024-08-28 16:13:41', '首页流量调度映射', '0');
INSERT INTO `sys_dict` VALUES ('D6303FB786FF4AA4A79AAC45AD27FA16', 'successfulHttpCode', '成功HTTP返回码', 'parent', '成功HTTP返回码', 26, '0', '1', '2024-07-22 10:51:00', '1', '2024-07-22 10:51:00', '手动添加', '0');

INSERT INTO `sys_dict` VALUES ('6FB55A02491D4B8589FCD7499F4841C1', '1', '正常', 'serviceStatus', '正常', 1, '7030BC94B6294A7BBBC8231B596B8F45', '1', '2024-06-25 14:17:59', '1', '2024-06-25 14:17:59', '正常', '0');
INSERT INTO `sys_dict` VALUES ('6FF33F4D2D3F4465BC1DF6F2B887A3DC', '0', '下线', 'serviceStatus', '下线', 0, '7030BC94B6294A7BBBC8231B596B8F45', '1', '2024-06-25 14:18:16', '1', '2024-06-25 14:18:16', '下线', '0');
INSERT INTO `sys_dict` VALUES ('B1FB5744BCB54268A62137363E1E50E2', '200', '200', 'successfulHttpCode', '200', 0, 'D6303FB786FF4AA4A79AAC45AD27FA16', '1', '2024-07-22 10:51:21', '1', '2024-07-22 10:51:21', '200', '0');
INSERT INTO `sys_dict` VALUES ('322B30FE28FF42E3A432770EF0961565', '302', '302', 'successfulHttpCode', '302', 3, 'D6303FB786FF4AA4A79AAC45AD27FA16', '1', '2024-08-12 14:27:42', '1', '2024-08-12 14:27:42', '302', '0');
INSERT INTO `sys_dict` VALUES ('3742FCE55B774480A7653B4C6D9A21E7', 'hafr-agent:9527', 'hafr-agent', 'dispatchOverrider', 'hafr-agent:9527', 0, '79BDA71B83A4423296296743493A827C', '1', '2024-08-28 16:09:59', '1', '2024-08-28 16:09:59', 'hafr-agent:9527', '0');
INSERT INTO `sys_dict` VALUES ('42649EBA7893463BB0DB16650E726C0D', '301', '301', 'successfulHttpCode', '301', 2, 'D6303FB786FF4AA4A79AAC45AD27FA16', '1', '2024-08-12 14:27:15', '1', '2024-08-12 14:27:15', '301', '0');

INSERT INTO `sys_dict` VALUES ('101', '0', '系统-应急调度', 'taskType', '任务管理-任务类型(0-系统应急调度,1-系统接口调用,2-人工,3-发送通知)', 4, '100', 'system', '2023-06-29 15:23:26', '1', '2023-06-29 15:23:26', '任务管理字典项，SQL语句导入。', '0');