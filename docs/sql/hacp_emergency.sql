DROP TABLE IF EXISTS `emergency_task_script`;
CREATE TABLE `emergency_task_script`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `script_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本名称',
  `script_describe` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本描述',
  `script_content` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本内容',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;
-- ----------------------------
-- Table structure for emergency_app
-- ----------------------------
DROP TABLE IF EXISTS `emergency_app`;
CREATE TABLE `emergency_app`  (
  `host_app_id` int(11) NOT NULL AUTO_INCREMENT,
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `host_app` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `host_app_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`host_app_id`) USING BTREE,
  UNIQUE INDEX `un_host_app`(`host_app` ASC ,`workspace_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_container
-- ----------------------------
DROP TABLE IF EXISTS `emergency_container`;
CREATE TABLE `emergency_container`  (
  `container_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端id',
  `client_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密钥',
  `grant_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '授权类型',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `root_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '端点根url',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目空间ID',
  `cloud_workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '云平台项目空间ID',
  `cloud_clusters` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '云平台项目集群名称',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`container_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_entity_tag
-- ----------------------------
DROP TABLE IF EXISTS `emergency_entity_tag`;
CREATE TABLE `emergency_entity_tag`  (
  `entity_id` int(11) NOT NULL COMMENT '实体ID',
  `tag_id` int(11) NOT NULL COMMENT 'tag ID',
  `entity_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '实体类型',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目空间ID',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`entity_id`, `tag_id`, `entity_type`,`workspace_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '实体标签关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for emergency_host
-- ----------------------------
DROP TABLE IF EXISTS `emergency_host`;
CREATE TABLE `emergency_host`  (
  `host_id` int(11) NOT NULL AUTO_INCREMENT,
  `host_desc` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器描述',
  `host_address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机器地址',
  `host_port` int(11) NULL DEFAULT NULL COMMENT 'port',
  `host_username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `host_password` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `secret_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥',
  `host_app_id` int(11) NULL DEFAULT NULL COMMENT '应用id',
  `host_os` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机系统',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`host_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for import_history_file_info
-- ----------------------------
DROP TABLE IF EXISTS `import_history_file_info`;
CREATE TABLE `import_history_file_info`  (
 `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
 `file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件名称',
 `file_blob` mediumblob NULL COMMENT '文件内容',
 `excel_count` int NOT NULL DEFAULT 0 COMMENT 'API导入信息总计',
 `excel_success` int NOT NULL DEFAULT 0 COMMENT 'API导入信息成功数',
 `excel_fail` int NOT NULL DEFAULT 0 COMMENT 'API导入信息失败数',
 `excel_success_remark` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成功序列（1,2,3）',
 `excel_fail_remark` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '失败序列（4,5,6）',
 `batch_number` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '批次号',
 `create_time` datetime NOT NULL COMMENT '创建时间',
 `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
 `operator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
 `operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for emergency_host_archive
-- ----------------------------
DROP TABLE IF EXISTS `emergency_host_archive`;
CREATE TABLE `emergency_host_archive`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `host_id` bigint NOT NULL COMMENT '主机id',
  `activity_id` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程任务编号',
  `business_key` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '绑定的业务id',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `host_desc` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器描述',
  `host_address` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机器地址',
  `host_port` int NULL DEFAULT NULL COMMENT 'port',
  `host_username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `host_password` varchar(254) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `secret_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥',
  `host_os` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '主机系统',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务主机存档表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for emergency_tag
-- ----------------------------
DROP TABLE IF EXISTS `emergency_tag`;
CREATE TABLE `emergency_tag`  (
  `tag_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `tag` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签',
  `tag_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签中文',
  `entity_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '实体类型',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '0:禁用  1:启用',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`tag_id`) USING BTREE,
  UNIQUE INDEX `uk_tag`(`tag` ASC, `entity_type` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_case
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_case`;
CREATE TABLE `hacp_emergency_case`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
  `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
  `case_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `case_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
  `case_deploy_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
  `operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '应急案例表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_node_recode
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_node_recode`;
CREATE TABLE `hacp_emergency_node_recode`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
  `workplace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
  `case_id` bigint(20) NOT NULL COMMENT '案例编号',
  `task_id` bigint(20) NOT NULL COMMENT '任务编号',
  `task_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `task_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
  `business_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务KEY',
  `task_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型',
  `activity_node_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动节点ID',
  `task_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
  `result_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '响应参数',
  `execute_result` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行结果',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `duration` int(11) NULL DEFAULT 0 COMMENT '耗时',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '节点调用流水表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_service_node_recode
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_service_node_recode`;
CREATE TABLE `hacp_emergency_service_node_recode`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
  `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
  `case_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部署id',
  `node_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '节点类型',
  `activity_node_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动节点ID',
  `execute_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行ID',
  `request_param` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求参数',
  `response_param` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '响应参数',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `duration` int(11) NULL DEFAULT NULL COMMENT '耗时',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '服务节点调用流水表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hacp_emergency_task
-- ----------------------------
DROP TABLE IF EXISTS `hacp_emergency_task`;
CREATE TABLE `hacp_emergency_task`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户编号',
  `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
  `task_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `task_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务类型：人工、系统应急调度、接口调用、shell',
  `task_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务描述',
  `task_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务参数JSON',
  `task_operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务执行人',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
  `operator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '应急案例任务表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for emergency_task_script
-- ----------------------------
DROP TABLE IF EXISTS `emergency_task_script`;
CREATE TABLE `emergency_task_script`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `script_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本名称',
  `script_describe` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本描述',
  `script_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '脚本内容',
  `workspace_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '项目空间ID',
  `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
  `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态',
  `tm_smp` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of emergency_task_script
-- ----------------------------
INSERT INTO `emergency_task_script` VALUES (1, '生成 Java 堆转储文件(heap dump)', '用于生成 Java 堆转储文件（heap dump）的命令。可以用来分析 Java 应用程序的内存使用情况，找出内存泄漏的原因。', 'rm -f /tmp/heapdump.hprof&&jmap -dump:format=b,file=/tmp/heapdump.hprof $(cat application.pid)&echo \"文件地址：/tmp/heapdump.hprof\"', '8296A9028999', '09f1ca36f4354bde947be04f52d7362c', 'test', '2024-10-23 14:55:21', '2024-10-30 10:15:37', '0', '20241030101536');
INSERT INTO `emergency_task_script` VALUES (2, '打印JAVA Core', '用于打印出给定Java进程ID或核心文件或远程调试服务器的Java堆栈跟踪。这个工具可以用来分析Java应用程序的线程堆栈，以便定位问题如死锁、内存泄漏等。\n间隔三秒打印一次，总共打印5次', 'rm -f /tmp/javacore.log;for i in {1..5}; do jstack $(cat application.pid) > /tmp/javacore.log; sleep 3; done;echo \'执行成功，文件地址：/tmp/javacore.log\'', '8296A9028999', '09f1ca36f4354bde947be04f52d7362c', 'test', '2024-10-23 15:08:08', '2024-10-30 10:15:24', '0', '20241030101524');

INSERT INTO `sys_menu` VALUES (189048, 0, '应急预案', NULL, '/emergencyCase', 'emergencycase', 'D', 'ClusterOutlined', 3, NULL, '2024-06-21 16:07:20.000000', '2024-07-23 17:58:45.000000', '{}', 'routerView', '/EmergencyCase/CaseManagement/index', 'EmergencyCase', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189049, 189048, '预案原子任务', NULL, '/emergencyCase/taskDefinition/index', 'emergencycase:taskdefinition', 'M', 'BuildOutlined', 1, NULL, '2024-06-21 16:10:52.000000', '2024-07-01 10:44:25.000000', '{}', '/EmergencyCase/TaskDefinition/index', '', 'TaskDefinition', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189072, 4, '启用', NULL, 'sys:user:enable', 'sys:user:enable', 'B', NULL, 0, NULL, '2024-07-01 10:12:16.444094', '2024-07-01 10:12:16.444094', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189073, 4, '禁用', NULL, 'sys:user:disable', 'sys:user:disable', 'B', NULL, 0, NULL, '2024-07-01 10:12:35.387455', '2024-07-01 10:12:35.387455', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189074, 189048, '预案管理', NULL, '/EmergencyCase/CaseManagement/index', 'CaseManagement:admin', 'M', 'ClusterOutlined', 2, NULL, '2024-07-08 10:03:09.000000', '2024-07-08 10:03:09.000000', '{}', '/EmergencyCase/CaseManagement/index', '', 'CaseManagement', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189075, 189048, '预案执行', NULL, '/EmergencyCase/EmergencyProcessManagement/index', 'EmergencyProcessManagement:admin', 'M', 'AppstoreAddOutlined', 3, NULL, '2024-07-11 14:39:45.000000', '2024-11-28 15:25:49.000000', '{}', '/EmergencyCase/EmergencyProcessManagement/index', '', 'EmergencyProcessManagement', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189138, 189049, '查询', NULL, '/', 'emergency:task:query', 'B', NULL, 0, NULL, '2024-07-30 14:51:45.668263', '2024-07-30 14:51:45.668263', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189139, 189049, '删除', NULL, '/', 'emergency:task:delete', 'B', NULL, 0, NULL, '2024-07-30 14:52:27.499724', '2024-07-30 14:52:27.499724', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189140, 189049, '修改', NULL, '/', 'emergency:task:update', 'B', NULL, 0, NULL, '2024-07-30 14:52:55.565993', '2024-07-30 14:52:55.565993', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189141, 189049, '新增', NULL, '/', 'emergency:task:add', 'B', NULL, 0, NULL, '2024-07-30 14:53:18.334738', '2024-07-30 14:53:18.334738', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189146, 189048, '主机管理', NULL, '/hostMaintenance/index', 'HostMaintenance:admin', 'M', 'SlidersOutlined', 4, NULL, '2024-08-21 15:17:02.000000', '2024-08-21 15:17:02.000000', '{}', '/EmergencyCase/HostMaintenance/index', NULL, 'HostMaintenance', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189147, 189146, '查询', NULL, 'EmergencyHostController', 'emergency:host:query', 'B', NULL, 0, NULL, '2024-08-27 14:22:00.785271', '2024-08-27 14:22:00.785271', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189148, 189146, '删除', NULL, 'EmergencyHostController', 'emergency:host:delete', 'B', NULL, 0, NULL, '2024-08-27 14:22:20.857559', '2024-08-27 14:22:20.857559', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189149, 189146, '更新', NULL, 'EmergencyHostController', 'emergency:host:update', 'B', NULL, 0, NULL, '2024-08-27 14:22:41.153718', '2024-08-27 14:22:41.153718', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189150, 189146, '新增', NULL, 'EmergencyHostController', 'emergency:host:add', 'B', NULL, 0, NULL, '2024-08-27 14:22:56.081742', '2024-08-27 14:22:56.081742', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189151, 189146, '下载模板', NULL, 'EmergencyHostController', 'emergency:host:template', 'B', NULL, 0, NULL, '2024-08-27 14:23:22.222667', '2024-08-27 14:23:22.222667', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189152, 189146, '导入主机', NULL, 'EmergencyHostController', 'emergency:host:import', 'B', NULL, 0, NULL, '2024-08-27 14:23:50.690950', '2024-08-27 14:23:50.690950', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189154, 189146, '新增标签', NULL, 'EmergencyTagController', 'emergency:tag:add', 'B', NULL, 0, NULL, '2024-08-27 14:31:08.000000', '2024-08-27 14:31:08.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189155, 189146, '删除标签', NULL, 'EmergencyTagController', 'emergency:tag:delete', 'B', NULL, 0, NULL, '2024-08-27 14:31:27.000000', '2024-08-27 14:31:27.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189158, 189146, '新增实例', NULL, 'EmergencyAppController', 'emergency:app:add', 'B', NULL, 0, NULL, '2024-08-27 14:34:25.833743', '2024-08-27 14:34:25.833743', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189159, 189146, '删除实例', NULL, 'EmergencyAppController', 'emergency:app:delete', 'B', NULL, 0, NULL, '2024-08-27 14:34:47.005251', '2024-08-27 14:34:47.005251', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189168, 189049, '新增标签', NULL, 'EmergencyTaskTagController', 'emergency:task:tag:add', 'B', NULL, 0, NULL, '2024-09-06 19:07:02.671632', '2024-09-06 19:07:02.671632', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189169, 189049, '删除标签', NULL, 'EmergencyTaskTagController', 'emergency:task:tag:delete', 'B', NULL, 0, NULL, '2024-09-06 19:07:22.000000', '2024-09-06 19:07:22.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189170, 189074, '新增标签', NULL, 'EmergencyCaseTagController', 'emergency:case:tag:add', 'B', NULL, 0, NULL, '2024-09-06 19:07:42.747095', '2024-09-06 19:07:42.747095', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189171, 189074, '删除标签', NULL, 'EmergencyCaseTagController', 'emergency:case:tag:delete', 'B', NULL, 0, NULL, '2024-09-06 19:08:01.643738', '2024-09-06 19:08:01.643738', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189172, 189048, '云平台管理', NULL, '/emergencyCase/cloudPlatformManagement/index', 'cloudPlatformManagement:admin', 'M', 'RadarChartOutlined', 5, NULL, '2024-09-10 09:10:24.795404', '2024-09-10 09:10:24.795404', '{}', '/EmergencyCase/CloudPlatformManagement/index', NULL, 'CloudPlatformManagement', NULL, 1, 0, 1, 0, 1);
INSERT INTO `sys_menu` VALUES (189173, 189172, '新增', NULL, 'ContainerConfigController', 'container:config:add', 'B', NULL, 0, NULL, '2024-09-10 09:51:28.000000', '2024-09-10 09:51:28.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189174, 189172, '删除', NULL, 'ContainerConfigController', 'container:config:delete', 'B', NULL, 0, NULL, '2024-09-10 10:18:14.761993', '2024-09-10 10:18:14.761993', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189175, 189172, '修改', NULL, 'ContainerConfigController', 'container:config:update', 'B', NULL, 0, NULL, '2024-09-10 10:18:31.259446', '2024-09-10 10:18:31.259446', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189176, 189172, '查询', NULL, 'ContainerConfigController', 'container:config:query', 'B', NULL, 0, NULL, '2024-09-10 10:18:52.948869', '2024-09-10 10:18:52.948869', '{}', NULL, NULL, 'container:config:query', NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189178, 189048, '预案详情', NULL, '/emergencyCase/caseManagement/view', 'CaseManagementView:admin', 'M', NULL, 1, NULL, '2024-09-12 09:31:55.036199', '2024-09-12 09:31:55.036199', '{}', '/EmergencyCase/CaseManagement/view', NULL, 'CaseManagementView', NULL, 0, 1, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189179, 189074, '查询', NULL, '/', 'emergency:case:query', 'B', NULL, 0, NULL, '2024-09-12 09:32:45.430891', '2024-09-12 09:32:45.430891', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189180, 189074, '删除', NULL, '/', 'emergency:case:delete', 'B', NULL, 0, NULL, '2024-09-12 09:33:06.344736', '2024-09-12 09:33:06.344736', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189181, 189074, '更新', NULL, '/', 'emergency:case:update', 'B', NULL, 0, NULL, '2024-09-12 09:33:34.941484', '2024-09-12 09:33:34.941484', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189182, 189074, '新增', NULL, '/', 'emergency:case:add', 'B', NULL, 0, NULL, '2024-09-12 09:33:54.360862', '2024-09-12 09:33:54.360862', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189183, 189074, '启动案例', NULL, '/', 'emergency:case:start', 'B', NULL, 0, NULL, '2024-09-12 09:34:34.244897', '2024-09-12 09:34:34.244897', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189184, 189074, '新增标签', NULL, '/', 'emergency:task:case:tag:add', 'B', NULL, 0, NULL, '2024-09-12 09:35:38.207861', '2024-09-12 09:35:38.207861', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189185, 189074, '删除标签', NULL, '/', 'emergency:task:case:tag:delete', 'B', NULL, 0, NULL, '2024-09-12 09:39:33.102534', '2024-09-12 09:39:33.102534', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189186, 0, '系统配置', NULL, '/configManagement', 'config', 'D', 'ContainerOutlined', 3, NULL, '2024-09-13 13:35:37.000000', '2024-09-13 13:35:37.000000', '{}', 'routerView', '/hostMaintenance/index', 'configManagement', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189188, 189048, '执行详情', NULL, '/emergencyCase/emergencyProcessManagement/view', 'EmergencyProcessManagementView:admin', 'M', NULL, 0, NULL, '2024-09-14 13:22:47.196755', '2024-09-14 13:22:47.196755', '{}', '/EmergencyCase/EmergencyProcessManagement/view', NULL, 'EmergencyProcessManagementView', NULL, 0, 1, 1, 0, 0);
INSERT INTO `sys_menu` VALUES (189189, 189075, '查询', NULL, '/', 'emergency:process:query', 'B', NULL, 0, NULL, '2024-09-14 13:24:12.319016', '2024-09-14 13:24:12.319016', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189190, 189075, '处理', NULL, '/', 'emergency:process:dispose', 'B', NULL, 0, NULL, '2024-09-14 13:24:40.000000', '2024-09-14 13:24:40.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);
INSERT INTO `sys_menu` VALUES (189191, 189186, '脚本维护', NULL, '/emergencyCase/scriptManagement/index', 'ScriptManagement:admin', 'M', 'FileTextOutlined', 3, NULL, '2024-10-10 15:21:47.000000', '2024-10-10 15:21:47.000000', '{}', '/EmergencyCase/ScriptManagement/index', NULL, 'ScriptManagement', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189192, 189191, '查询', NULL, 'emergency:script:query', 'emergency:script:query', 'B', NULL, 0, NULL, '2024-10-10 15:58:39.934922', '2024-10-10 15:58:39.934922', '{}', NULL, NULL, NULL, NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189193, 189191, '删除', NULL, 'emergency:script:delete', 'emergency:script:delete', 'B', NULL, 0, NULL, '2024-10-10 15:59:13.000000', '2024-10-10 15:59:13.000000', '{}', NULL, NULL, NULL, NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189194, 189191, '更新', NULL, 'emergency:script:update', 'emergency:script:update', 'B', NULL, 0, NULL, '2024-10-10 15:59:33.000000', '2024-10-10 15:59:33.000000', '{}', NULL, NULL, NULL, NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189195, 189191, '新增', NULL, 'emergency:script:add', 'emergency:script:add', 'B', NULL, 0, NULL, '2024-10-10 15:59:53.927609', '2024-10-10 15:59:53.927609', '{}', NULL, NULL, NULL, NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189196, 189075, '删除', NULL, '/', 'emergency:process:delete', 'B', NULL, 0, NULL, '2024-10-30 16:46:42.331046', '2024-10-30 16:46:42.331046', '{}', NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1);

INSERT INTO `sys_role_menu` VALUES ('1-189048', 1, 189048);
INSERT INTO `sys_role_menu` VALUES ('1-189049', 1, 189049);
INSERT INTO `sys_role_menu` VALUES ('1-189074', 1, 189074);
INSERT INTO `sys_role_menu` VALUES ('1-189075', 1, 189075);
INSERT INTO `sys_role_menu` VALUES ('1-189138', 1, 189138);
INSERT INTO `sys_role_menu` VALUES ('1-189139', 1, 189139);
INSERT INTO `sys_role_menu` VALUES ('1-189140', 1, 189140);
INSERT INTO `sys_role_menu` VALUES ('1-189141', 1, 189141);
INSERT INTO `sys_role_menu` VALUES ('1-189146', 1, 189146);
INSERT INTO `sys_role_menu` VALUES ('1-189147', 1, 189147);
INSERT INTO `sys_role_menu` VALUES ('1-189148', 1, 189148);
INSERT INTO `sys_role_menu` VALUES ('1-189149', 1, 189149);
INSERT INTO `sys_role_menu` VALUES ('1-189150', 1, 189150);
INSERT INTO `sys_role_menu` VALUES ('1-189151', 1, 189151);
INSERT INTO `sys_role_menu` VALUES ('1-189152', 1, 189152);
INSERT INTO `sys_role_menu` VALUES ('1-189154', 1, 189154);
INSERT INTO `sys_role_menu` VALUES ('1-189155', 1, 189155);
INSERT INTO `sys_role_menu` VALUES ('1-189158', 1, 189158);
INSERT INTO `sys_role_menu` VALUES ('1-189159', 1, 189159);
INSERT INTO `sys_role_menu` VALUES ('1-189168', 1, 189168);
INSERT INTO `sys_role_menu` VALUES ('1-189169', 1, 189169);
INSERT INTO `sys_role_menu` VALUES ('1-189170', 1, 189170);
INSERT INTO `sys_role_menu` VALUES ('1-189171', 1, 189171);
INSERT INTO `sys_role_menu` VALUES ('1-189172', 1, 189172);
INSERT INTO `sys_role_menu` VALUES ('1-189173', 1, 189173);
INSERT INTO `sys_role_menu` VALUES ('1-189174', 1, 189174);
INSERT INTO `sys_role_menu` VALUES ('1-189175', 1, 189175);
INSERT INTO `sys_role_menu` VALUES ('1-189176', 1, 189176);
INSERT INTO `sys_role_menu` VALUES ('1-189178', 1, 189178);
INSERT INTO `sys_role_menu` VALUES ('1-189179', 1, 189179);
INSERT INTO `sys_role_menu` VALUES ('1-189180', 1, 189180);
INSERT INTO `sys_role_menu` VALUES ('1-189181', 1, 189181);
INSERT INTO `sys_role_menu` VALUES ('1-189182', 1, 189182);
INSERT INTO `sys_role_menu` VALUES ('1-189183', 1, 189183);
INSERT INTO `sys_role_menu` VALUES ('1-189184', 1, 189184);
INSERT INTO `sys_role_menu` VALUES ('1-189185', 1, 189185);
INSERT INTO `sys_role_menu` VALUES ('1-189186', 1, 189186);
INSERT INTO `sys_role_menu` VALUES ('1-189188', 1, 189188);
INSERT INTO `sys_role_menu` VALUES ('1-189189', 1, 189189);
INSERT INTO `sys_role_menu` VALUES ('1-189190', 1, 189190);
INSERT INTO `sys_role_menu` VALUES ('1-189191', 1, 189191);
INSERT INTO `sys_role_menu` VALUES ('1-189192', 1, 189192);
INSERT INTO `sys_role_menu` VALUES ('1-189193', 1, 189193);
INSERT INTO `sys_role_menu` VALUES ('1-189194', 1, 189194);
INSERT INTO `sys_role_menu` VALUES ('1-189195', 1, 189195);
INSERT INTO `sys_role_menu` VALUES ('1-189196', 1, 189196);

INSERT INTO `sys_role_menu` VALUES ('1076-189048', 1076, 189048);
INSERT INTO `sys_role_menu` VALUES ('1076-189049', 1076, 189049);
INSERT INTO `sys_role_menu` VALUES ('1076-189074', 1076, 189074);
INSERT INTO `sys_role_menu` VALUES ('1076-189075', 1076, 189075);
INSERT INTO `sys_role_menu` VALUES ('1076-189138', 1076, 189138);
INSERT INTO `sys_role_menu` VALUES ('1076-189139', 1076, 189139);
INSERT INTO `sys_role_menu` VALUES ('1076-189140', 1076, 189140);
INSERT INTO `sys_role_menu` VALUES ('1076-189141', 1076, 189141);
INSERT INTO `sys_role_menu` VALUES ('1076-189146', 1076, 189146);
INSERT INTO `sys_role_menu` VALUES ('1076-189147', 1076, 189147);
INSERT INTO `sys_role_menu` VALUES ('1076-189148', 1076, 189148);
INSERT INTO `sys_role_menu` VALUES ('1076-189149', 1076, 189149);
INSERT INTO `sys_role_menu` VALUES ('1076-189150', 1076, 189150);
INSERT INTO `sys_role_menu` VALUES ('1076-189151', 1076, 189151);
INSERT INTO `sys_role_menu` VALUES ('1076-189152', 1076, 189152);
INSERT INTO `sys_role_menu` VALUES ('1076-189154', 1076, 189154);
INSERT INTO `sys_role_menu` VALUES ('1076-189155', 1076, 189155);
INSERT INTO `sys_role_menu` VALUES ('1076-189158', 1076, 189158);
INSERT INTO `sys_role_menu` VALUES ('1076-189159', 1076, 189159);
INSERT INTO `sys_role_menu` VALUES ('1076-189168', 1076, 189168);
INSERT INTO `sys_role_menu` VALUES ('1076-189169', 1076, 189169);
INSERT INTO `sys_role_menu` VALUES ('1076-189170', 1076, 189170);
INSERT INTO `sys_role_menu` VALUES ('1076-189171', 1076, 189171);
INSERT INTO `sys_role_menu` VALUES ('1076-189172', 1076, 189172);
INSERT INTO `sys_role_menu` VALUES ('1076-189173', 1076, 189173);
INSERT INTO `sys_role_menu` VALUES ('1076-189174', 1076, 189174);
INSERT INTO `sys_role_menu` VALUES ('1076-189175', 1076, 189175);
INSERT INTO `sys_role_menu` VALUES ('1076-189176', 1076, 189176);
INSERT INTO `sys_role_menu` VALUES ('1076-189178', 1076, 189178);
INSERT INTO `sys_role_menu` VALUES ('1076-189179', 1076, 189179);
INSERT INTO `sys_role_menu` VALUES ('1076-189180', 1076, 189180);
INSERT INTO `sys_role_menu` VALUES ('1076-189181', 1076, 189181);
INSERT INTO `sys_role_menu` VALUES ('1076-189182', 1076, 189182);
INSERT INTO `sys_role_menu` VALUES ('1076-189183', 1076, 189183);
INSERT INTO `sys_role_menu` VALUES ('1076-189184', 1076, 189184);
INSERT INTO `sys_role_menu` VALUES ('1076-189185', 1076, 189185);
INSERT INTO `sys_role_menu` VALUES ('1076-189186', 1076, 189186);
INSERT INTO `sys_role_menu` VALUES ('1076-189188', 1076, 189188);
INSERT INTO `sys_role_menu` VALUES ('1076-189189', 1076, 189189);
INSERT INTO `sys_role_menu` VALUES ('1076-189190', 1076, 189190);
INSERT INTO `sys_role_menu` VALUES ('1076-189191', 1076, 189191);
INSERT INTO `sys_role_menu` VALUES ('1076-189192', 1076, 189192);
INSERT INTO `sys_role_menu` VALUES ('1076-189193', 1076, 189193);
INSERT INTO `sys_role_menu` VALUES ('1076-189194', 1076, 189194);
INSERT INTO `sys_role_menu` VALUES ('1076-189195', 1076, 189195);
INSERT INTO `sys_role_menu` VALUES ('1076-189196', 1076, 189196);


INSERT INTO `sys_dict` VALUES ('102', '2', '系统-接口调用', 'taskType', '任务管理-任务类型(0-系统应急调度,1-系统接口调用,2-人工,3-发送通知)', 5, '100', '1', '2024-09-03 17:37:38', '1', '2024-09-03 17:37:38', '任务管理字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('103', '1', '人工审批', 'taskType', '任务管理-任务类型(0-系统应急调度,1-系统接口调用,2-人工,3-发送通知)', 2, '100', '1', '2024-09-03 17:43:17', '1', '2024-09-03 17:43:17', '任务管理字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('104', '3', '系统-发送通知', 'taskType', '任务管理-任务类型(0-系统应急调度,1-系统接口调用,2-人工,3-发送通知)', 6, '100', '1', '2024-09-03 17:37:58', '1', '2024-09-03 17:37:58', '任务管理字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('105', '5', '系统-容器重启', 'taskType', '系统-容器重启', 1, '100', '1', '2024-09-03 17:38:32', '1', '2024-09-03 17:38:32', '系统-容器重启', '0');
INSERT INTO `sys_dict` VALUES ('106', '6', '系统-玲珑框架应用重启', 'taskType', '系统-玲珑框架应用重启', 0, '100', '1', '2024-09-03 17:38:46', '1', '2024-09-03 17:38:46', '系统-玲珑框架应用重启', '0');
INSERT INTO `sys_dict` VALUES ('107', '4', '系统-脚本调用', 'taskType', '系统-脚本调用', 4, '100', '1', '2024-09-03 17:38:20', '1', '2024-09-03 17:38:20', '系统-脚本调用', '0');