dependencies {
    api project(':common:high-availability-common')
    api project(':interface:high-availability-base-interface')
    api project(':interface:high-availability-system-interface')

    api('com.cmpay:lemon-framework-starter-context')
    api('com.cmpay:lemon-framework-starter-idgenerator')
    api('com.cmpay:lemon-framework-starter-datasource')
    api('com.cmpay:lemon-framework-starter-mybatis')
    api('com.cmpay:lemon-framework-starter-cache-jcache')
    api('com.cmpay:file-client-starter')
    api('com.cmpay:lemon-common')
    api('com.cmpay:alerting-starter')
    api('com.cmpay:lemon-framework-starter-security')

    // 使用分布式本地缓存
    api("com.cmpay:lemon-framework-starter-session-hazelcast")

    api("cn.hutool:hutool-all:${hutoolVersion}")
    compileOnly 'org.apache.httpcomponents:httpclient'
    api("com.cmpay:lemon-common-lang")
    api('com.cmpay:lemon-common-beanutils')
    api("com.cmpay:lemon-smx")

}
