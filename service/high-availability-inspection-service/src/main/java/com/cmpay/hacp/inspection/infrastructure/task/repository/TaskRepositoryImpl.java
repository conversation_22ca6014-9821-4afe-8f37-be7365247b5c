package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.task.model.Task;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRepository;
import com.cmpay.hacp.inspection.infrastructure.task.converter.TaskConverter;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskDO;
import com.cmpay.hacp.inspection.infrastructure.task.repository.mapper.TaskMapper;
import com.cmpay.hacp.inspection.infrastructure.utils.IdGenUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class TaskRepositoryImpl implements TaskRepository {
    private final TaskMapper mapper;
    private final TaskConverter converter;

    @Override
    public Task getByTaskId(String taskId) {
        return converter.toTask(mapper.selectOne(Wrappers.<TaskDO>lambdaQuery().eq(TaskDO::getTaskId, taskId)));
    }

    @Override
    public Task getByTaskName(String taskName) {
        return converter.toTask(mapper.selectOne(Wrappers.<TaskDO>lambdaQuery().eq(TaskDO::getName, taskName)));
    }

    @Override
    public void save(Task task) {
        TaskDO taskDO = converter.toTaskDO(task);
        taskDO.setTaskId(IdGenUtil.generateTempId());
        mapper.insert(taskDO);
        String taskId = IdGenUtil.generateTaskId(taskDO.getId());

        // 推荐写法
        LambdaUpdateWrapper<TaskDO> updateWrapper = Wrappers.<TaskDO>lambdaUpdate()
                .eq(TaskDO::getId, taskDO.getId())
                .set(TaskDO::getTaskId, taskId);

        mapper.update(null, updateWrapper);

        task.setTaskId(taskId);
    }

    @Override
    public boolean existsOtherTaskWithName(String name, String excludeTaskId) {
        return mapper.selectCount(Wrappers.<TaskDO>lambdaQuery().eq(TaskDO::getName, name).ne(TaskDO::getTaskId, excludeTaskId)) > 0;
    }

    @Override
    public boolean updateTask(InspectionTask inspectionTask) {
        Task task = new Task();
        task.setTaskId(inspectionTask.getTaskId());
        task.setName(inspectionTask.getName());
        task.setDescription(inspectionTask.getDescription());
        task.setStatus(inspectionTask.getStatus());
        TaskDO taskDO = converter.toTaskDO(task);
        return SqlHelper.retBool(mapper.update(taskDO, Wrappers.lambdaUpdate(TaskDO.class)
                .eq(TaskDO::getTaskId, taskDO.getTaskId())
                .set(TaskDO::getName, taskDO.getName())
                .set(TaskDO::getDescription, taskDO.getDescription())
                .set(TaskDO::getStatus, taskDO.getStatus())));
    }

    @Override
    public boolean removeTask(String taskId) {
        return SqlHelper.retBool(mapper.delete(Wrappers.<TaskDO>lambdaQuery().eq(TaskDO::getTaskId, taskId)));
    }

    @Override
    public IPage<Task> pageQuery(long current, long size, InspectionTask queryCondition) {
        Page<TaskDO> mpPage = new Page<>(current, size);

        // 使用自定义SQL查询，包含LEFT JOIN获取执行状态
        IPage<TaskDO> taskDOPage = mapper.pageQueryWithExecutionStatus(
                mpPage,
                queryCondition.getName(),
                queryCondition.getDescription(),
                queryCondition.getStatus(),
                queryCondition.getExecutionStatus(),
                queryCondition.getStartTime(),
                queryCondition.getEndTime()
        );

        // 转换为领域对象
        List<Task> tasks = taskDOPage.getRecords().stream()
                .map(converter::toTask)
                .collect(Collectors.toList());

        IPage<Task> taskPage = new Page<>(taskDOPage.getCurrent(), taskDOPage.getSize(), taskDOPage.getTotal());
        taskPage.setRecords(tasks);
        return taskPage;
    }
}
