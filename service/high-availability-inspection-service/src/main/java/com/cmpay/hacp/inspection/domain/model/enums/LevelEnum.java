package com.cmpay.hacp.inspection.domain.model.enums;

public enum LevelEnum {
    /**
     * 差
     */
    BAD("差"),
    /**
     * 中等
     */
    MEDIUM("中等") ,
    /**
     * 良好
     */
    GOOD("良好"),
    /**
     * 优秀
     */
    EXCELLENT("优秀");
    private final String label;

    LevelEnum(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
}
