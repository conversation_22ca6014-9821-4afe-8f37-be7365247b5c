package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.SSHGateway;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionAbstractExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SSHExecutor extends InspectionAbstractExecutor {
    private final SSHGateway sshGateway;
    private final PluginScriptRepository pluginScriptRepository;
    private final RulePluginParamRepository rulePluginParamRepository;


    private static final String SCRIPT_CONTENT = "scriptContent";
    private static final String EXECUTION_MSG = "脚本执行失败";

    public SSHExecutor(RuleMatchingService ruleMatchingService,
            SSHGateway sshGateway,
            PluginScriptRepository pluginScriptRepository,
            RulePluginParamRepository rulePluginParamRepository) {
        super(ruleMatchingService);
        this.sshGateway = sshGateway;
        this.pluginScriptRepository = pluginScriptRepository;
        this.rulePluginParamRepository = rulePluginParamRepository;
    }

    @Override
    protected boolean verifyParam(RuleExecution ruleExecution) {
        String ruleId = ruleExecution.getRuleId();
        String pluginId = ruleExecution.getPluginId();
        log.info("Executing inspection script, Rule ID: {}, Plugin ID: {}, Target: {}", ruleId, pluginId, ruleExecution.getTargetHost());

        PluginScript pluginScript = pluginScriptRepository.getByPluginId(pluginId);
        if (pluginScript == null) {
            return false;
        }

        String scriptContent = pluginScript.getScriptContent();
        ruleExecution.getParams().put(SCRIPT_CONTENT,scriptContent);
        return true;
    }

    @Override
    protected InspectionResult verifyFailMessage(RuleExecution ruleExecution) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .executionDuration(0L)
                .pluginName(ruleExecution.getPluginName())
                .message("Plugin script not found")
                .details("Plugin script not found for pluginName: " + ruleExecution.getPluginName())
                .build();
    }

    @Override
    protected ExecutionResult doExecute(RuleExecution ruleExecution) {
        String ruleId = ruleExecution.getRuleId();
        String scriptContent = (String) ruleExecution.getParams().get(SCRIPT_CONTENT);
        scriptContent = processScriptParameters(scriptContent, ruleId);

        // 执行远程脚本
        TargetHost targetHost = ruleExecution.getTargetHost();

        return sshGateway.executeScript(
                targetHost.toSshConnectionConfig(),
                ScriptExecutionRequest.builder()
                        .scriptContent(scriptContent)
                        .scriptType(ruleExecution.getPluginType() == PluginType.SHELL_SCRIPT ? ScriptExecutionRequest.ScriptType.SHELL : ScriptExecutionRequest.ScriptType.PYTHON)
                        .build()
        );
    }

    /**
     * 处理脚本参数替换
     */
    private String processScriptParameters(String scriptContent, String ruleId) {
        // 获取规则插件参数
        List<RulePluginParam> rulePluginParams = rulePluginParamRepository.findByRuleId(ruleId);

        // 转换为Map<String, String>
        Map<String, String> paramMap = rulePluginParams != null ?
                rulePluginParams.stream()
                        .collect(Collectors.toMap(
                                RulePluginParam::getPluginParamName,
                                RulePluginParam::getPluginParamValue
                        )) :
                Collections.emptyMap();

        // 替换脚本中的参数
        String processedScript = scriptContent;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            processedScript = processedScript.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        log.debug("脚本参数替换完成，参数数量: {}", paramMap.size());
        return processedScript;
    }

    @Override
    protected InspectionResult buildExecuteFailureResult(RuleExecution ruleExecution, ExecutionResult result) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .message(EXECUTION_MSG)
                .executionDuration(result.getExecutionTime())
                .pluginName(ruleExecution.getPluginName())
                .details(String.format("退出码: %d, 错误信息: %s, 标准输出: %s",
                        result.getExitCode(),
                        result.getErrorMessage(),
                        result.getStdout()))
                .build();
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return (context.getPluginType() == PluginType.SHELL_SCRIPT ||
                context.getPluginType() == PluginType.PYTHON_SCRIPT) &&
                context.getResourceType() == ResourceType.VIRTUAL_MACHINE;
    }

    @Override
    protected String getExecutionMsg() {
        return EXECUTION_MSG;
    }
}
