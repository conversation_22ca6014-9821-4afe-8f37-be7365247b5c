package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginMappingRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 资源相关执行策略 - 领域服务
 * 处理VM和Container类型的资源执行
 */
@Service
@Slf4j
public class ResourceBasedExecutionStrategy extends AbstractRuleExecutionStrategy {
    private final PluginExecutionDomainService pluginExecutionDomainService;

    public ResourceBasedExecutionStrategy(RulePluginMappingRepository rulePluginMappingRepository,
            PluginRepository pluginRepository,
            RuleRepository ruleRepository,
            PluginExecutionDomainService pluginExecutionDomainService) {
        super(rulePluginMappingRepository, pluginRepository, ruleRepository);
        this.pluginExecutionDomainService = pluginExecutionDomainService;
    }

    @Override
    public boolean supports(ResourceType resourceType) {
        return resourceType == ResourceType.VIRTUAL_MACHINE ||
                resourceType == ResourceType.CONTAINER;
    }

    @Override
    protected void doExecute(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        pluginExecutionDomainService.executeForResources(ruleExecution, successCount, failCount);
    }
}
