package com.cmpay.hacp.inspection.domain.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum TimeRangeEnum {
    LESS_THAN_1_SECOND("<1秒"),
    LESS_THAN_3_SECOND ("1-3秒"),
    LESS_THAN_5_SECOND ("3-5秒"),
    LESS_THAN_10_SECOND ("5-10秒"),
    /**
     * >10秒
     */
    GREATER_THAN_10_SECOND(">10秒");

    private final String label;

    TimeRangeEnum(String description) {
        this.label = description;
    }

    public String getLabel() {
        return label;
    }

    public static Map<String, Integer> GenerateRangeTimeMap(){
        return Arrays.stream(TimeRangeEnum.values()).map(TimeRangeEnum::getLabel).collect((Collectors.toMap(k->k,v->0)));
    }

}
