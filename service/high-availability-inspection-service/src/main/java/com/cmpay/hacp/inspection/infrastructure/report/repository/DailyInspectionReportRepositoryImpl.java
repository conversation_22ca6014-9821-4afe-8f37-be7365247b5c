package com.cmpay.hacp.inspection.infrastructure.report.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.report.model.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.report.model.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.domain.report.repository.DailyInspectionReportRepository;
import com.cmpay.hacp.inspection.infrastructure.config.InspectionTenantHandler;
import com.cmpay.hacp.inspection.infrastructure.report.converter.DailyInspectionReportConverter;
import com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.report.repository.mapper.DailyInspectionReportMapper;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 按日巡检报告数据访问层
 */
@Repository
@RequiredArgsConstructor
public class DailyInspectionReportRepositoryImpl extends CrudRepository<DailyInspectionReportMapper, DailyInspectionReportDO> implements DailyInspectionReportRepository {

    private final DailyInspectionReportDetailRepository dailyInspectionReportDetailRepository;
    private final DailyInspectionReportConverter dailyInspectionReportConverter;

    @Override
    public Page<DailyInspectionReport> page(PageDTO<?> page, DailyInspectionReport queryCondition) {

        // 转换领域对象为DO对象用于查询
        DailyInspectionReportDO queryConditionDO = dailyInspectionReportConverter.toDailyInspectionReportDO(queryCondition);

        // 构建查询条件
        LambdaQueryWrapper<DailyInspectionReportDO> queryWrapper = Wrappers.lambdaQuery(DailyInspectionReportDO.class);

        if (queryConditionDO != null) {
            // 报告ID模糊查询
            if (StringUtils.isNotBlank(queryConditionDO.getReportId())) {
                queryWrapper.like(DailyInspectionReportDO::getReportId, queryConditionDO.getReportId());
            }

            // 报告日期精确查询
            if (queryConditionDO.getReportDate() != null) {
                queryWrapper.eq(DailyInspectionReportDO::getReportDate, queryConditionDO.getReportDate());
            }

            // 通过率范围查询
            if (queryConditionDO.getPassRate() != null) {
                queryWrapper.ge(DailyInspectionReportDO::getPassRate, queryConditionDO.getPassRate());
            }

            // 报告状态查询
            if (null != queryConditionDO.getReportStatus()) {
                queryWrapper.eq(DailyInspectionReportDO::getReportStatus, queryConditionDO.getReportStatus());
            }
        }

        // 按报告日期倒序排列
        queryWrapper.orderByDesc(DailyInspectionReportDO::getReportDate);

        // 执行分页查询
        Page<DailyInspectionReportDO> pageParam = new Page<>(page.getCurrent(), page.getSize());
        IPage<DailyInspectionReportDO> doResult = this.page(pageParam, queryWrapper);

        // 转换DO结果为领域对象
        List<DailyInspectionReport> domainRecords = dailyInspectionReportConverter.toDailyInspectionReportList(doResult.getRecords());

        // 构建领域对象分页结果
        Page<DailyInspectionReport> result = new Page<>(doResult.getCurrent(), doResult.getSize(), doResult.getTotal());
        result.setRecords(domainRecords);

        return result;
    }

    @Override
    public DailyInspectionReportDetail getDetail(String reportId) {
        DailyInspectionReportDO reportDO = this.getOne(Wrappers.lambdaQuery(DailyInspectionReportDO.class).eq(DailyInspectionReportDO::getReportId, reportId));
        DailyInspectionReportDetailDO reportDetailDO = dailyInspectionReportDetailRepository.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDetailDO.class)
                        .eq(DailyInspectionReportDetailDO::getReportId, reportId)
        );

        // 转换DO为领域对象
        return dailyInspectionReportConverter.toDailyInspectionReportDetail(reportDetailDO,reportDO);
    }

    @Override
    public List<DailyInspectionReport> queryFailedListAndIgnoreWorkspaceId(LocalDate startDate,LocalDate endDate) {
        InspectionTenantHandler.addNotIgnoreTable("inspection_daily_report");
        List<DailyInspectionReportDO> failedReports = this.list(
                Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                        .eq(DailyInspectionReportDO::getReportStatus, "FAILED")
                        .between(DailyInspectionReportDO::getReportDate, startDate, endDate)
        );
        InspectionTenantHandler.removeNotIgnoreTable();
        return dailyInspectionReportConverter.toDailyInspectionReportList(failedReports);
    }
}
