package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginOutputFieldRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginOutputFieldConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginOutputFieldMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 插件脚本结构化输出Repository
 */
@Repository
@RequiredArgsConstructor
public class PluginOutputFieldRepositoryImpl extends CrudRepository<PluginOutputFieldMapper, PluginOutputFieldDO> implements PluginOutputFieldRepository {
    private final PluginOutputFieldConverter pluginOutputFieldConverter;
    @Override
    public void saveBatch(List<PluginOutputField> pluginOutputFieldList, String pluginId) {
        if (pluginOutputFieldList == null || pluginOutputFieldList.isEmpty()) {
            return;
        }

        List<PluginOutputFieldDO> results = pluginOutputFieldList.stream()
                .map(scriptResult -> pluginOutputFieldConverter.toPluginOutputFieldDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.saveBatch(results, 10);
    }

    @Override
    public List<PluginOutputField> listByPluginId(String pluginId) {
        return pluginOutputFieldConverter.toPluginOutputFieldList(this.list(
                Wrappers.lambdaQuery(PluginOutputFieldDO.class)
                        .eq(PluginOutputFieldDO::getPluginId, pluginId)));
    }

    @Override
    public void updateBatchByName(List<PluginOutputField> pluginOutputFieldList, String pluginId) {
        if (pluginOutputFieldList == null || pluginOutputFieldList.isEmpty()) {
            return;
        }

        List<PluginOutputFieldDO> results = pluginOutputFieldList.stream()
                .map(scriptResult -> pluginOutputFieldConverter.toPluginOutputFieldDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.updateBatchById(results, 10);
    }

    @Override
    public void removeByFieldNames(List<String> fieldNames, String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginOutputFieldDO.class)
                .eq(PluginOutputFieldDO::getPluginId, pluginId)
                .in(PluginOutputFieldDO::getFieldName, fieldNames));
    }

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginOutputFieldDO.class)
                .eq(PluginOutputFieldDO::getPluginId, pluginId));
    }

    @Override
    public PluginOutputField queryByFieldName(String fieldName, String pluginId) {
        return pluginOutputFieldConverter.toPluginOutputField(this.getOne(
                Wrappers.lambdaQuery(PluginOutputFieldDO.class)
                        .eq(PluginOutputFieldDO::getPluginId, pluginId)
                        .eq(PluginOutputFieldDO::getFieldName, fieldName)));
    }
}
