package com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatusEnum;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 报告内容结构化数据类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "inspection_report_detail", autoResultMap = true)
public class PerInspectionReportDetailDO extends BaseInsertDO {
    private String reportId;
    /**
     * 执行概况
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExecutionSummary executionSummary;

    /**
     * 执行结果分布（饼图数据）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExecutionDistribution executionDistribution;

    /**
     * 执行时间分布（柱状图数据）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExecutionTimeDistribution timeDistribution;

    /**
     * 规则检查详情列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<RuleCheckDetail> ruleCheckDetails;

    /**
     * 异常详情列表（问题汇总）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ExceptionDetail> exceptionDetails;

    /**
     * 执行概况
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionSummary {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 执行耗时（秒）
         */
        private Long duration;

        /**
         * 执行耗时字符串（如：38秒）
         */
        private String durationStr;

        /**
         * 规则总数
         */
        private Integer totalRules;

        /**
         * 成功规则数
         */
        private Integer successRules;
    }

    /**
     * 执行结果分布（饼图数据）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionDistribution {
        /**
         * 总检查数
         */
        private Integer total;

        /**
         * 通过率百分比
         */
        private Integer successRate;

        /**
         * 各状态数量
         */
        private List<StatusCount> statusCounts;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class StatusCount {
            /**
             * 状态名称（通过、告警、错误）
             */
            private ReportStatusEnum status;

            /**
             * 状态编码（passed、warning、failed）
             */
            private String statusCode;

            /**
             * 数量
             */
            private Integer count;
        }
    }

    /**
     * 执行时间分布（柱状图数据）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionTimeDistribution {
        /**
         * 时间区间数据
         */
        private List<TimeRange> timeRanges;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TimeRange {
            /**
             * 时间区间标签（<1秒、1-3秒、3-5秒、5-10秒、>10秒）
             */
            private String label;

            /**
             * 该区间的数量
             */
            private Integer count;
        }
    }

    /**
     * 规则检查详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleCheckDetail {

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 资源名称（web-server-1、db-server-1等）
         */
        private String resourceName;

        /**
         * 资源类型（HOST、SERVICE等）
         */
        private ResourceType resourceType;

        /**
         * 执行结果详情
         */
        private String executionResult;
        /**
         * 匹配状态 0:失败 1:成功
         */
        private boolean matchStatus;
        /**
         * 报表状态
         */
        private ReportStatusEnum reportStatus;

        /**
         * 执行状态 0:失败 1:成功
         */
        private ExecutionResult executionStatus;
        /**
         * 执行时间
         */
        private LocalDateTime executionTime;
        /**
         * 执行结果详情
         */
        private String details;

        /**
         * 条件描述
         */
        private String conditionMsg;

        /**
         * 执行耗时(毫秒)
         */
        private Long executionDuration;
    }


    /**
     * 异常详情（问题汇总）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionDetail {
        /**
         * 规则ID
         */
        private String ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 异常描述
         */
        private String description;

        /**
         * 资源类型
         */
        private ResourceType resourceType;

        /**
         * 资源名称
         */
        private String resourceName;

        /**
         * 条件描述
         */
        private String conditionMsg;

        /**
         * 执行结果详情
         */
        private String details;

        /**
         * 建议措施
         */
        private String suggest;
    }
}
