package com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatusEnum;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 按日巡检报告详细内容结构化数据类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "inspection_daily_report_detail",autoResultMap = true)
public class DailyInspectionReportDetailDO extends BaseInsertDO {
    private String reportId;

    /**
     * 巡检分类统计（用于饼图展示）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CategoryStatistics> categoryStatistics;

    /**
     * 近7日通过率趋势（用于柱状图展示）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DailyPassRateTrend> passRateTrends;

    /**
     * 异常详情列表（异常项标签页）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExceptionDetails exceptionDetails;

    /**
     * 趋势分析数据（趋势分析标签页）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private TrendAnalysis trendAnalysis;

    /**
     * 巡检分类统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryStatistics {
        /**
         * 分类名称（如：数据库、网络、应用、页面等）
         */
        private String categoryName;

        /**
         * 该分类的检查数量
         */
        private Integer checkCount;

        /**
         * 占比（百分比）
         */
        private Double percentage;
    }

    /**
     * 近7日通过率趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyPassRateTrend {
        /**
         * 日期
         */
        private LocalDate date;

        /**
         * 通过率
         */
        private Double passRate;

        /**
         * 通过率级别（用于颜色区分：≥95% 优秀、≥90% 良好、≥80% 中等、<80% 差）
         */
        private String level;
    }

    /**
     * 异常详情（异常项标签页）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionDetails {
        /**
         * 总异常数
         */
        private Integer totalCount;

        /**
         * 错误数
         */
        private Integer errorCount;

        /**
         * 警告数
         */
        private Integer warningCount;

        /**
         * 异常列表
         */
        private List<ExceptionItem> exceptions;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExceptionItem {
            /**
             * 执行ID
             */
            private Long ruleExecutionId;

            /**
             * 异常类型（错误、警告、信息）
             */
            private ReportStatusEnum status;

            /**
             * 异常标题
             */
            private String title;

            /**
             * 异常描述
             */
            private String description;

            /**
             * 资源类型（如：页面、数据库、接口等）
             */
            private ResourceType resourceType;

            /**
             * 资源名称
             */
            private String resourceName;

            /**
             * 建议操作
             */
            private String suggest;
            /**
             * 执行结果详情
             */
            private String details;
            /**
             * 检查条件描述
             */
            private String conditionMsg;

            /**
             * 发生时间
             */
            private LocalDateTime occurTime;
        }
    }

    /**
     * 趋势分析（趋势分析标签页）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        /**
         * 通过率趋势数据
         */
        private List<TrendPoint> passRateTrend;

        /**
         * 响应时间趋势数据
         */
        private List<TrendPoint> responseTimeTrend;

        /**
         * 异常数量趋势数据
         */
        private ExceptionTrend exceptionTrend;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TrendPoint {
            /**
             * 日期
             */
            private LocalDate date;

            /**
             * 日期显示
             */
            private String dateDisplay;

            /**
             * 数值（通过率或响应时间）
             */
            private Double value;

            /**
             * 单位（%或ms）
             */
            private String unit;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExceptionTrend {
            /**
             * 错误趋势
             */
            private List<TrendPoint> errorTrend;

            /**
             * 警告趋势
             */
            private List<TrendPoint> warningTrend;
        }
    }
}
