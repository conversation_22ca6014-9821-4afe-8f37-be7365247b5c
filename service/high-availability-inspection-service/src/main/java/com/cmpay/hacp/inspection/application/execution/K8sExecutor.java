package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.K8sGateway;
import com.cmpay.hacp.inspection.domain.execution.model.*;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class K8sExecutor extends InspectionAbstractExecutor {

    private final K8sGateway k8sGateway;
    private final PluginScriptRepository pluginScriptRepository;
    private final RulePluginParamRepository rulePluginParamRepository;

    private static final String SCRIPT_CONTENT = "scriptContent";
    private static final String EXECUTION_MSG = "脚本执行失败";

    public K8sExecutor(RuleMatchingService ruleMatchingService,
            K8sGateway k8sGateway,
            PluginScriptRepository pluginScriptRepository,
            RulePluginParamRepository rulePluginParamRepository) {
        super(ruleMatchingService);
        this.k8sGateway = k8sGateway;
        this.pluginScriptRepository = pluginScriptRepository;
        this.rulePluginParamRepository = rulePluginParamRepository;
    }

    @Override
    protected InspectionResult verifyFailMessage(RuleExecution ruleExecution) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .message("执行失败")
                .details("插件脚本不存在: " + ruleExecution.getPluginName())
                .build();
    }

    @Override
    protected InspectionResult buildExecuteFailureResult(RuleExecution ruleExecution, ExecutionResult result) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .message("脚本执行失败")
                .details(String.format("退出码: %d, 错误信息: %s, 标准输出: %s",
                        result.getExitCode(),
                        result.getErrorMessage(),
                        result.getStdout()))
                .build();
    }

    @Override
    protected boolean verifyParam(RuleExecution ruleExecution) {
        String pluginId = ruleExecution.getPluginId();
        PluginScript pluginScript = pluginScriptRepository.getByPluginId(pluginId);
        if (pluginScript == null) {
            log.error("插件脚本不存在，插件ID: {}", pluginId);
            return false;
        }

        String scriptContent = pluginScript.getScriptContent();
        ruleExecution.getParams().put(SCRIPT_CONTENT,scriptContent);
        return true;
    }

    @Override
    protected ExecutionResult doExecute(RuleExecution ruleExecution) {
        log.info("开始执行K8s脚本巡检，规则ID: {}, 插件ID: {}",
                ruleExecution.getRuleId(), ruleExecution.getPluginId());
        String scriptContent = (String) ruleExecution.getParams().get(SCRIPT_CONTENT);
        try {
            // 2. 处理脚本参数替换
            scriptContent = processScriptParameters(scriptContent, ruleExecution.getRuleId());

            // 4. 构建脚本执行请求
            ScriptExecutionRequest request = buildScriptExecutionRequest(scriptContent, ruleExecution.getPluginType());

            // 5. 执行脚本
            K8sConnectionConfig k8sConfig = ruleExecution.getK8sConfig();
            log.debug("在Pod {}/{} 中执行脚本", k8sConfig.getNamespace(), k8sConfig.getPodName());
            // 6. 处理执行结果
            return k8sGateway.executeScript(k8sConfig, request);
        } catch (Exception e) {
            log.error("K8s脚本执行异常，规则ID: {}", ruleExecution.getRuleId(), e);
            return ExecutionResult.builder().success(false)
                    .stderr("脚本执行异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return (context.getPluginType() == PluginType.SHELL_SCRIPT ||
                context.getPluginType() == PluginType.PYTHON_SCRIPT) &&
                context.getResourceType() == ResourceType.CONTAINER;
    }

    /**
     * 处理脚本参数替换
     */
    private String processScriptParameters(String scriptContent, String ruleId) {
        // 获取规则插件参数
        List<RulePluginParam> rulePluginParams = rulePluginParamRepository.findByRuleId(ruleId);

        // 转换为Map<String, String>
        Map<String, String> paramMap = rulePluginParams != null ?
                rulePluginParams.stream()
                        .collect(Collectors.toMap(
                                RulePluginParam::getPluginParamName,
                                RulePluginParam::getPluginParamValue
                        )) :
                Collections.emptyMap();

        // 替换脚本中的参数
        String processedScript = scriptContent;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            processedScript = processedScript.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        log.debug("脚本参数替换完成，参数数量: {}", paramMap.size());
        return processedScript;
    }

    /**
     * 构建脚本执行请求
     */
    private ScriptExecutionRequest buildScriptExecutionRequest(String scriptContent, PluginType pluginType) {
        ScriptExecutionRequest.ScriptType scriptType = pluginType == PluginType.SHELL_SCRIPT ?
                ScriptExecutionRequest.ScriptType.SHELL :
                ScriptExecutionRequest.ScriptType.PYTHON;

        return ScriptExecutionRequest.builder()
                .scriptContent(scriptContent)
                .scriptType(scriptType)
                .timeoutSeconds(60) // 默认60秒超时
                .workingDirectory("/tmp")
                .build();
    }

    @Override
    protected String getExecutionMsg() {
        return EXECUTION_MSG;
    }
}