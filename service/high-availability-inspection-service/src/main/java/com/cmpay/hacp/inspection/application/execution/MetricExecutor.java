package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.FireflyGateway;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionAbstractExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 指标执行器
 * 使用FireflyGateway执行萤火虫系统的查询并返回巡检结果
 */
@Slf4j
@Component
public class MetricExecutor extends InspectionAbstractExecutor {

    private final FireflyGateway fireflyGateway;

    private static final String FAIL_MESSAGE_KEY = "failMessage";

    public MetricExecutor(RuleMatchingService ruleMatchingService,
            FireflyGateway fireflyGateway) {
        super(ruleMatchingService);
        this.fireflyGateway = fireflyGateway;
    }

    @Override
    protected InspectionResult verifyFailMessage(RuleExecution ruleExecution) {
        return null;
    }

    @Override
    protected InspectionResult buildExecuteFailureResult(RuleExecution ruleExecution, ExecutionResult result) {
        String failMessage = (String) ruleExecution.getParams().get(FAIL_MESSAGE_KEY);
        return InspectionResult.builder()
                .success(false)
                .message(failMessage)
                .pluginName(ruleExecution.getPluginName())
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .build();
    }

    @Override
    protected boolean verifyParam(RuleExecution ruleExecution) {
        return true;
    }

    @Override
    protected ExecutionResult doExecute(RuleExecution ruleExecution) {
        try {
            log.info("Executing Firefly inspection for task: {}", ruleExecution.getTaskId());

            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(ruleExecution.getStartTime())
                    .endTime(ruleExecution.getEndTime())
                    .indicatorType("host")
                    .indicatorName("cpu_usage_rate")
                    .pageNum(1)
                    .pageSize(1000)
                    .build();

            // 执行萤火虫系统查询
            IndicatorQueryResponse response = fireflyGateway.getIndicatorDataList(request);

            ruleExecution.getParams().put(FAIL_MESSAGE_KEY,"Firefly查询失败: " + response.getMsgCd());
            // 处理查询结果
            log.debug("Firefly query successful, result type: {}, result count: {}",
                    response.getDataList(),
                    response.getDataList().size());

            // 这里可以根据具体的业务逻辑处理查询结果
            // 例如：检查指标值是否符合预期，生成巡检报告等
            return ExecutionResult.builder().success(response.isSuccess())
                    .stdout(JsonUtil.objToStr(response.getDataList()))
                    .executionTime(0L)
                    .build();

        } catch (Exception e) {
            log.error("Error executing Firefly inspection for task: {}", ruleExecution.getTaskId(), e);

            ruleExecution.getParams().put(FAIL_MESSAGE_KEY,"Firefly巡检执行异常: " + e.getMessage());
            return ExecutionResult.builder()
                    .success(false)
                    .stderr("Firefly巡检执行异常: " + e.getMessage())
                    .executionTime(0L)
                    .build();
        }
    }

    @Override
    public boolean supports(ExecutionContext context) {
        // TODO: 根据实际情况实现
        return false;
    }

    @Override
    protected String getExecutionMsg() {
        return "Firefly巡检执行异常";
    }
}
