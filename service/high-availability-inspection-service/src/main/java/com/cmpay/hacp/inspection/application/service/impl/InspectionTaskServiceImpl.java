package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionTaskService;
import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.task.AlarmNotification;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.TaskRuleExecution;
import com.cmpay.hacp.inspection.domain.rule.repository.RuleRepository;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.Task;
import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;
import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;
import com.cmpay.hacp.inspection.domain.task.repository.*;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleDO;
import com.cmpay.hacp.inspection.infrastructure.scheduler.SchedulerService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskServiceImpl implements InspectionTaskService {

    private final RuleRepository ruleRepository;
    private final TaskRepository taskRepository;
    private final TaskAlarmRepository taskAlarmRepository;
    private final TaskRuleRepository taskRuleRepository;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final TaskExecutionRepository taskExecutionRepository;
    private final ScheduleConfigRepository scheduleConfigRepository;
    private final SchedulerService schedulerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTask(InspectionTask inspectionTask) {
        // 新增重名校验
        if (taskRepository.getByTaskName(inspectionTask.getName()) != null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }


        Task task = new Task();
        task.setName(inspectionTask.getName());
        task.setDescription(inspectionTask.getDescription());
        task.setStatus(inspectionTask.getStatus());
        taskRepository.save(task);

        String taskId = task.getTaskId();
        inspectionTask.setTaskId(task.getTaskId());

        if (CollectionUtils.isNotEmpty(inspectionTask.getTaskRuleExecutions())) {
            List<TaskRuleMapping> mappings = inspectionTask.getTaskRuleExecutions().stream()
                    .map(ruleExecution -> {
                        TaskRuleMapping mapping = new TaskRuleMapping();
                        mapping.setTaskId(taskId);
                        mapping.setRuleId(ruleExecution.getRuleId());
                        return mapping;
                    })
                    .collect(Collectors.toList());

            taskRuleRepository.saveBatch(mappings);

            taskRuleResourceRepository.saveBatch(inspectionTask);
        }

        if (JudgeUtils.isNotNull(inspectionTask.getAlarmNotification())) {
            taskAlarmRepository.saveOrUpdate(taskId, inspectionTask.getAlarmNotification());
        }
        // 保存调度配置
        scheduleConfigRepository.saveByTaskId(taskId, inspectionTask.getScheduleConfig());

        // 最后处理调度
        // 调度任务
        try {
            schedulerService.scheduleInspectionTask(task.getTaskId(), inspectionTask.getScheduleConfig(), CommonStatusEnum.ENABLE.equals(inspectionTask.getStatus()) && inspectionTask.getScheduleConfig().isEnabled());
        } catch (SchedulerException e) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_SCHEDULE_ERROR, e);
        }

        return inspectionTask.getTaskId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTask(InspectionTask inspectionTask) {
        String taskId = inspectionTask.getTaskId();
        if (taskRepository.getByTaskId(taskId) == null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_NOT_FOUND);
        }

        // 更新重名校验
        if (taskRepository.existsOtherTaskWithName(inspectionTask.getName(), taskId)) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TAG_NAME_EXIST);
        }

        boolean updateTask = taskRepository.updateTask(inspectionTask);

        if (!updateTask) {
            log.error("Failed to update task with ID: {}", taskId);
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_UPDATE_FAILED);
        }

        // 更新规则执行信息
        if (CollectionUtils.isNotEmpty(inspectionTask.getTaskRuleExecutions())) {
            // 先删除原有规则关联
            taskRuleRepository.removeByTaskId(taskId);

            // 批量插入新规则关联
            List<TaskRuleMapping> mappings = inspectionTask.getTaskRuleExecutions().stream()
                    .map(ruleExecution -> {
                        TaskRuleMapping mapping = new TaskRuleMapping();
                        mapping.setTaskId(taskId);
                        mapping.setRuleId(ruleExecution.getRuleId());
                        return mapping;
                    })
                    .collect(Collectors.toList());
            taskRuleRepository.saveBatch(mappings);

            // 先删除原有资源关联
            taskRuleResourceRepository.deleteByTaskId(taskId);

            // 批量插入新资源关联
            taskRuleResourceRepository.saveBatch(inspectionTask);
        }

        taskAlarmRepository.saveOrUpdate(taskId, inspectionTask.getAlarmNotification());

        // 保存调度配置
        scheduleConfigRepository.updateByTaskId(taskId, inspectionTask.getScheduleConfig());

        // 调度任务
        try {
            schedulerService.scheduleInspectionTask(taskId, inspectionTask.getScheduleConfig(), CommonStatusEnum.ENABLE.equals(inspectionTask.getStatus()) && inspectionTask.getScheduleConfig().isEnabled());
        } catch (SchedulerException e) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_SCHEDULE_ERROR, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        taskRepository.removeTask(taskId);

        taskRuleRepository.removeByTaskId(taskId);

        taskRuleResourceRepository.deleteByTaskId(taskId);

        scheduleConfigRepository.removeByTaskId(taskId);

        taskAlarmRepository.removeByTaskId(taskId);

        try {
            schedulerService.deleteTask(taskId);
        } catch (SchedulerException e) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_SCHEDULE_ERROR, e);
        }
    }

    @Override
    public InspectionTask getTaskDetail(String taskId) {
        Task task = taskRepository.getByTaskId(taskId);
        if (task == null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_NAME_NOT_EXIST);
        }

        List<TaskRuleMapping> mappings = taskRuleRepository.list(taskId);

        ScheduleConfig taskScheduleConfig = scheduleConfigRepository.queryByTaskId(taskId);

        InspectionTask inspectionTask = InspectionTask.builder()
                .taskId(task.getTaskId())
                .name(task.getName())
                .description(task.getDescription())
                .status(task.getStatus())
                .scheduleConfig(taskScheduleConfig)
                .build();

        if (CollectionUtils.isEmpty(mappings)) {
            return inspectionTask;
        }
        // 把taskRuleResourceDOS按照ruleId挂到mappings上面去
        // 获取所有规则ID
        List<String> ruleIds = mappings.stream()
                .map(TaskRuleMapping::getRuleId)
                .collect(Collectors.toList());
        // 查询规则详情
        List<RuleDO> ruleDOs = ruleRepository.listByRuleIds(ruleIds);
        // 构建规则ID到规则名称的映射
        Map<String, String> ruleMap = ruleDOs.stream()
                .collect(Collectors.toMap(
                        RuleDO::getRuleId,
                        RuleDO::getName
                ));


        List<TaskRuleExecution> taskRuleExecutions = mappings.stream().map(ruleExecution -> {
            TaskRuleExecution execution = new TaskRuleExecution();
            execution.setTaskId(taskId);
            execution.setRuleId(ruleExecution.getRuleId());

            List<Resource> resourceList = taskRuleResourceRepository.list(taskId, ruleExecution.getRuleId());
            if (CollectionUtils.isNotEmpty(resourceList)) {
                execution.setResourceList(resourceList);
            }
            // 设置规则名称
            execution.setRuleName(ruleMap.get(ruleExecution.getRuleId()));
            return execution;
        }).collect(Collectors.toList());
        inspectionTask.setTaskRuleExecutions(taskRuleExecutions);

        AlarmNotification alarmNotification = taskAlarmRepository.queryByTaskId(taskId);
        inspectionTask.setAlarmNotification(alarmNotification);
        return inspectionTask;
    }

    @Override
    public IPage<InspectionTask> getTaskPage(IPage<?> page, InspectionTask queryCondition) {
        // 执行分页查询，包含执行状态
        IPage<Task> taskPage = taskRepository.pageQuery(page.getCurrent(), page.getSize(), queryCondition);

        // 转换结果为领域对象
        IPage<InspectionTask> taskIPage = taskPage.convert(task -> InspectionTask.builder()
                .taskId(task.getTaskId())
                .name(task.getName())
                .description(task.getDescription())
                .status(task.getStatus())
                .executionStatus(task.getExecutionStatus())
                .build());

        // 处理关联数据
        if (!taskIPage.getRecords().isEmpty()) {
            enrichInspectionTasks(taskIPage.getRecords());
        }

        return taskIPage;
    }

    private void enrichInspectionTasks(List<InspectionTask> records) {
        List<String> taskIds = records.stream()
                .map(InspectionTask::getTaskId)
                .collect(Collectors.toList());

        // 批量查询相关数据
        List<ScheduleConfig> scheduleConfigDOS = scheduleConfigRepository.listByTaskIds(taskIds);

        // 组装规则、资源信息
        List<TaskRuleMapping> mappings = taskRuleRepository.listByTaskIds(taskIds);

        List<String> ruleIds = mappings.stream()
                .map(TaskRuleMapping::getRuleId)
                .collect(Collectors.toList());

        List<RuleDO> rules = ruleRepository.listByRuleIds(ruleIds);

        // 构建规则ID到规则名称的映射
        Map<String, String> ruleIdToNameMap = rules.stream()
                .collect(Collectors.toMap(
                        RuleDO::getRuleId,
                        RuleDO::getName
                ));

        // 构建任务ID到规则执行列表的映射
        Map<String, List<TaskRuleExecution>> taskRuleExecutionMap = mappings.stream()
                .collect(Collectors.groupingBy(
                        TaskRuleMapping::getTaskId,
                        Collectors.mapping(taskRuleMapping -> {
                            TaskRuleExecution execution = new TaskRuleExecution();
                            execution.setTaskId(taskRuleMapping.getTaskId());
                            execution.setRuleId(taskRuleMapping.getRuleId());
                            execution.setRuleName(ruleIdToNameMap.get(taskRuleMapping.getRuleId()));
                            execution.setResourceList(taskRuleResourceRepository.list(taskRuleMapping.getTaskId(), taskRuleMapping.getRuleId()));
                            return execution;
                        }, Collectors.toList())
                ));


        // 设置调度配置信息
        Map<String, ScheduleConfig> scheduleConfigMap = scheduleConfigDOS.stream()
                .collect(Collectors.toMap(ScheduleConfig::getTaskId, Function.identity()));

        records.forEach(task -> {
            // 将规则执行信息设置到对应的任务中
            List<TaskRuleExecution> executions = taskRuleExecutionMap.get(task.getTaskId());
            task.setTaskRuleExecutions(executions);

            // 将调度配置信息设置到对应的任务中
            ScheduleConfig config = scheduleConfigMap.get(task.getTaskId());
            task.setScheduleConfig(config);

            // 设置最近的执行信息
            TaskExecution taskExecution =  taskExecutionRepository.getLatestByTaskId(task.getTaskId());
            task.setLatestExecutionTime(Optional.ofNullable(taskExecution).map(TaskExecution::getExecutionTime).orElse(null));
        });

    }

    @Override
    public void executeTaskNow(String taskId) {
        ScheduleConfig scheduleConfig = scheduleConfigRepository.queryByTaskId(taskId);
        if(!scheduleConfig.isEnabled()){
            BusinessException.throwBusinessException(ErrorCodeEnum.PLAN_CONFIG_NOT_ENABLED);
        }
        // 调用执行服务执行任务
        try {
            schedulerService.executeTaskNow(taskId);
        } catch (SchedulerException e) {
            BusinessException.throwBusinessException(ErrorCodeEnum.TASK_SCHEDULE_ERROR, e);
        }
    }
}
