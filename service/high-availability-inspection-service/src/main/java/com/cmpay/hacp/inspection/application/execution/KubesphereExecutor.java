package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.KubesphereGateway;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.GatewayCallResult;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionAbstractExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
public class KubesphereExecutor extends InspectionAbstractExecutor {

    private final KubesphereGateway kubesphereGateway;

    private static final String EXECUTION_MSG = "api远程执行失败";
    private static final String FAIL_MESSAGE_KEY = "failMessage";

    public KubesphereExecutor(RuleMatchingService ruleMatchingService,
            KubesphereGateway kubesphereGateway) {
        super(ruleMatchingService);
        this.kubesphereGateway = kubesphereGateway;
    }

    @Override
    protected InspectionResult verifyFailMessage(RuleExecution ruleExecution) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .pluginName(ruleExecution.getPluginName())
                .message("执行失败")
                .details("通过Kubesphere api执行巡检的集群必要参数不能为空")
                .build();
    }

    @Override
    protected boolean verifyParam(RuleExecution ruleExecution) {
        if (StringUtils.isAllBlank(ruleExecution.getK8sConfig().getWorkspaceId(), ruleExecution.getK8sConfig().getCluster(), ruleExecution.getK8sConfig().getNamespace())) {
            log.error("通过Kubesphere api执行巡检的集群必要参数不能为空, executionId:{}, taskId:{}, 规则ID: {}, 插件ID: {}, workspace:{}, cluster:{}, namespace:{}, service:{}",
                    ruleExecution.getExecutionId(), ruleExecution.getTaskId(), ruleExecution.getRuleId(), ruleExecution.getPluginId(),
                    ruleExecution.getK8sConfig().getWorkspaceId(), ruleExecution.getK8sConfig().getCluster(), ruleExecution.getK8sConfig().getNamespace(), ruleExecution.getK8sConfig().getService());
            return false;

        }
        return true;
    }

    @Override
    protected ExecutionResult doExecute(RuleExecution ruleExecution) {

        log.info("开始通过Kubesphere api执行巡检规则, executionId:{}, taskId:{}, 规则ID:{}, 插件ID:{}, workspace:{}, cluster:{}, namespace:{}, service:{}",
                ruleExecution.getExecutionId(), ruleExecution.getTaskId(), ruleExecution.getRuleId(), ruleExecution.getPluginId(),
                ruleExecution.getK8sConfig().getWorkspaceId(), ruleExecution.getK8sConfig().getCluster(), ruleExecution.getK8sConfig().getNamespace(), ruleExecution.getK8sConfig().getService());

        GatewayCallResult gatewayResult = kubesphereGateway.call(ruleExecution.getK8sConfig(), null);
        ruleExecution.getParams().put(FAIL_MESSAGE_KEY,gatewayResult.getMessage());
        return ExecutionResult.builder()
                .executionTime(gatewayResult.getExecutionTime())
                .stdout(Optional.ofNullable(gatewayResult.getResult()).map(JsonUtil::objToStr).orElse(null))
                .stderr(Optional.ofNullable(gatewayResult.getException()).map(Throwable::getMessage).orElse(null))
                .success(gatewayResult.isSuccess())
                .build();
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return context.getPluginType() == PluginType.K8S_RESOURCE_HEALTH_CHECK
                && context.getResourceType() == ResourceType.CONTAINER;
    }

    @Override
    protected InspectionResult buildExecuteFailureResult(RuleExecution ruleExecution, ExecutionResult result) {
        String message = (String)ruleExecution.getParams().get(FAIL_MESSAGE_KEY);
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .pluginName(ruleExecution.getPluginName())
                .message("巡检插件执行异常")
                .details(message)
                .build();
    }

    @Override
    protected String getExecutionMsg() {
        return EXECUTION_MSG;
    }
}