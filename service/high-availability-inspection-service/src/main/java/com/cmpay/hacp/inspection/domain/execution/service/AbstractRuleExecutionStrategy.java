package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginRepository;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginMapping;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginMappingRepository;
import com.cmpay.hacp.inspection.domain.rule.repository.RuleRepository;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 抽象规则执行策略 - 领域服务
 * 包含通用的执行流程，具体执行逻辑由子类实现
 */
@Slf4j
public abstract class AbstractRuleExecutionStrategy implements RuleExecutionStrategy {
    private final RulePluginMappingRepository rulePluginMappingRepository;
    private final PluginRepository pluginRepository;
    private final RuleRepository ruleRepository;

    public AbstractRuleExecutionStrategy(RulePluginMappingRepository rulePluginMappingRepository, PluginRepository pluginRepository,
            RuleRepository ruleRepository) {
        this.rulePluginMappingRepository = rulePluginMappingRepository;
        this.pluginRepository = pluginRepository;
        this.ruleRepository = ruleRepository;
    }

    @Override
    public final void execute(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        String ruleId = ruleExecution.getRuleId();
        // 获取规则关联的插件
        RulePluginMapping rulePluginMapping = rulePluginMappingRepository.findByRuleId(ruleId);
        InspectionRule inspectionRule = ruleRepository.getByRuleId(ruleId);

        if (rulePluginMapping == null) {
            log.warn("No plugin mapping found for rule: {}", ruleId);
            return;
        }

        String pluginId = rulePluginMapping.getPluginId();
        InspectionPlugin inspectionPlugin = pluginRepository.getByPluginId(pluginId);
        if (inspectionPlugin == null) {
            log.warn("Plugin not found: {}", pluginId);
            return;
        }
        ruleExecution.setPluginId(pluginId);
        ruleExecution.setPluginName(inspectionPlugin.getName());
        ruleExecution.setPluginType(inspectionPlugin.getType());
        ruleExecution.setRuleName(inspectionRule.getName());

        ExecutionContext context = new ExecutionContext();
        context.setPluginType(inspectionPlugin.getType());
        context.setResourceType(ruleExecution.getResourceType());
        ruleExecution.setExecutionContext(context);
        doExecute(ruleExecution, successCount, failCount);
    }

    /**
     * 执行具体策略 - 由子类实现
     */
    protected abstract void doExecute(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount);
}
