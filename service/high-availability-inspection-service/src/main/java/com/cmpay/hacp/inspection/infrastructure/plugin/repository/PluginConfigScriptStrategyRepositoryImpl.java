package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.cmpay.hacp.inspection.application.service.InspectionCipherService;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.*;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginOutputFieldRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class PluginConfigScriptStrategyRepositoryImpl extends PluginConfigAbstractRepository {
    private final InspectionCipherService inspectionCipherService;
    private final PluginScriptRepository pluginScriptRepository;
    private final PluginScriptParameterRepository scriptParameterRepository;

    public PluginConfigScriptStrategyRepositoryImpl(PluginOutputFieldRepository outputFiledRepository,
            InspectionCipherService inspectionCipherService,
            PluginScriptRepository pluginScriptRepository,
            PluginScriptParameterRepository scriptParameterRepository) {
        super(outputFiledRepository);
        this.inspectionCipherService = inspectionCipherService;
        this.pluginScriptRepository = pluginScriptRepository;
        this.scriptParameterRepository = scriptParameterRepository;
    }

    @Override
    protected List<PluginType> getPluginsType() {
        return Arrays.asList(PluginType.SHELL_SCRIPT, PluginType.PYTHON_SCRIPT);
    }

    @Override
    public void removeOther(String pluginId) {
        scriptParameterRepository.removeByPluginId(pluginId);
    }

    @Override
    public void saveOther(InspectionPlugin inspectionPlugin, String pluginId) {
        String cacheKey = inspectionPlugin.getKey();
        PluginConfigScript configScript = (PluginConfigScript)inspectionPlugin.getPluginConfig();
        // 保存脚本内容
        pluginScriptRepository.save(configScript, pluginId);

        // 保存参数定义
        if (CollectionUtils.isNotEmpty(configScript.getParameters())) {
            // 在service层处理加密逻辑
            String sm4Key = Optional.ofNullable(cacheKey).filter(JudgeUtils::isNotBlank).map(inspectionCipherService::getSm4RandomSalt).orElse(null);
            List<PluginScriptParameter> processedParameters = configScript.getParameters().stream()
                    .peek(scriptParameter -> {
                        if (scriptParameter.getIsEncrypted() != null && scriptParameter.getIsEncrypted()) {
                            scriptParameter.setParamValue(inspectionCipherService.decryptSysDataBySm4AndSm2AndEncrypt(sm4Key, scriptParameter.getParamValue()));
                        }
                    })
                    .collect(Collectors.toList());

            // 委托给repository处理数据库操作
            scriptParameterRepository.saveBatch(processedParameters, pluginId);
        }
    }

    @Override
    public void updateOther(InspectionPlugin inspectionPlugin, String pluginId) {
        String cacheKey = inspectionPlugin.getKey();
        PluginConfigScript configScript = (PluginConfigScript)inspectionPlugin.getPluginConfig();
        // 查询现有脚本
        PluginScript existingScript = pluginScriptRepository.getByPluginId(pluginId);

        // 更新脚本内容
        if (existingScript != null) {
            existingScript.setScriptContent(configScript.getScriptContent());
            pluginScriptRepository.updateByPluginId(existingScript);
        } else {
            // 如果不存在则创建新脚本
            pluginScriptRepository.save(configScript, pluginId);
        }

        // 4. 更新参数定义
        if (CollectionUtils.isEmpty(configScript.getParameters())) {
            // 如果参数为空，直接删除所有相关参数
            scriptParameterRepository.removeByPluginId(pluginId);
        } else {
            // 获取当前所有参数
            List<PluginScriptParameter> originList = scriptParameterRepository.listByPluginId(pluginId);

            // 创建名称到参数对象的映射，提高查找效率
            Map<String, PluginScriptParameter> originParamMap = originList.stream()
                    .collect(Collectors.toMap(PluginScriptParameter::getParamName, Function.identity()));

            List<PluginScriptParameter> insertList = new ArrayList<>();
            List<PluginScriptParameter> updateList = new ArrayList<>();
            List<String> deleteParamNames = new ArrayList<>(originParamMap.size());

            // 设置将要保留的参数名集合，用于判断删除
            Set<String> retainedParamNames = new HashSet<>();

            String key = Optional.ofNullable(cacheKey).filter(JudgeUtils::isNotBlank).map(inspectionCipherService::getSm4RandomSalt).orElse(null);
            // 处理新增和更新
            for (PluginScriptParameter scriptParameter : configScript.getParameters()) {
                String paramName = scriptParameter.getParamName();
                retainedParamNames.add(paramName);

                PluginScriptParameter existingParam = originParamMap.get(paramName);

                // 值是否加密
                if (scriptParameter.getIsEncrypted()) {
                    scriptParameter.setParamValue(inspectionCipherService.decryptSysDataBySm4AndSm2AndEncrypt(key, scriptParameter.getParamValue()));
                }
                if (existingParam == null) {
                    // 新增参数
                    insertList.add(scriptParameter);
                } else {
                    // 更新参数
                    updateList.add(scriptParameter);
                }
            }

            // 确定要删除的参数
            originParamMap.forEach((paramName, param) -> {
                if (!retainedParamNames.contains(paramName)) {
                    deleteParamNames.add(param.getParamName());
                }
            });

            if (!insertList.isEmpty()) {
                scriptParameterRepository.saveBatch(insertList, pluginId);
            }
            if (!updateList.isEmpty()) {
                scriptParameterRepository.updateBatchByPluginIdAndParamName(updateList, pluginId);
            }
            if (!deleteParamNames.isEmpty()) {
                scriptParameterRepository.removeByPluginIdAndParamNames(pluginId, deleteParamNames);
            }
        }
    }

    @Override
    protected PluginConfig queryOther(String pluginId) {
        PluginScript pluginScript = pluginScriptRepository.getByPluginId(pluginId);
        List<PluginScriptParameter> pluginScriptParameterList = scriptParameterRepository.listByPluginId(pluginId);

        // 返回前端解密
        if (JudgeUtils.isNotEmpty(pluginScriptParameterList)) {
            pluginScriptParameterList = pluginScriptParameterList.stream().peek(PluginScriptParameter -> {
                if (PluginScriptParameter.getIsEncrypted()) {
                    PluginScriptParameter.setParamValue(inspectionCipherService.dataDecrypt(PluginScriptParameter.getParamValue()));
                }
            }).collect(Collectors.toList());
        }

        PluginConfigScript pluginConfigScript = new PluginConfigScript();
        pluginConfigScript.setParameters(pluginScriptParameterList);

        if(pluginScript != null){
            pluginConfigScript.setScriptContent(pluginScript.getScriptContent());
        }

        return pluginConfigScript;
    }

}
