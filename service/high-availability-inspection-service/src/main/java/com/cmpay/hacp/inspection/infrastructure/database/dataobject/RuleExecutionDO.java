package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 巡检规则执行记录实体类
 * 记录巡检任务中每个规则在各个资源上的具体执行状态
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "inspection_rule_execution",autoResultMap = true)
public class RuleExecutionDO extends BaseInsertDO {
    /**
     * 任务执行ID
     */
    private String taskExecutionId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 资源ID
     */
    private ResourceType resourceType;

    /**
     * 资源名称
     */
    private String resourceName;
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 执行状态 0:失败 1:成功
     */
    private ExecutionResult executionStatus;

    /**
     * 匹配状态 0:失败 1:成功
     */
    private boolean matchStatus;

    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 执行结果
     */
    private String executionResult;
    /**
     * 执行结果详情
     */
    private String details;
    
    /**
     * 插件名称
     */
    private String pluginName;
    
    /**
     * 执行耗时(毫秒)
     */
    private Long executionDuration;

    /**
     * 建议措施
     */
    private String suggest;

    /**
     * 检查条件描述
     */
    private String conditionMsg;
    
    /**
     * 报表状态
     */
    private ReportStatusEnum reportStatus;
}
