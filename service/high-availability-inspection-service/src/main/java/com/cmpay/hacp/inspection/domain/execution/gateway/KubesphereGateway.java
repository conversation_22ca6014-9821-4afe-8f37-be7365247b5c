package com.cmpay.hacp.inspection.domain.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.model.GatewayCallResult;
import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Kubesphere网关接口
 * 提供基于kubectl exec的脚本执行能力
 */
public interface KubesphereGateway<T> {

    @Slf4j
    class LoggerHolder {}
    /**
     * 调用远程接口
     *
     * @param connectionConfig K8s连接配置
     * @param request 脚本执行请求
     * @return 脚本执行结果
     */
    T doRemoteCall(K8sConnectionConfig connectionConfig,
                                         ScriptExecutionRequest request);



    default GatewayCallResult call(K8sConnectionConfig connectionConfig, ScriptExecutionRequest request) {

        long start = System.currentTimeMillis(), executionTime;

        try{
            T result = this.doRemoteCall(connectionConfig, request);
            executionTime = System.currentTimeMillis() - start;
            LoggerHolder.log.debug("[{} call success] workspace:{}, cluster:{}, namespace:{}, service:{}, executionTime:{}",this.getClass().getSimpleName(),
                    connectionConfig.getWorkspaceId(), connectionConfig.getCluster(), connectionConfig.getNamespace(), connectionConfig.getService(), executionTime);
            return GatewayCallResult.builder().success(true).executionTime(executionTime).result(result).build();

        } catch (Exception e) {
            executionTime = System.currentTimeMillis() - start;
            LoggerHolder.log.error("[{} call exception] workspace:{}, cluster:{}, namespace:{}, service:{}, executionTime:{}, e:{}, msg:{}",this.getClass().getSimpleName(),
                    connectionConfig.getWorkspaceId(), connectionConfig.getCluster(), connectionConfig.getNamespace(), connectionConfig.getService(),
                    executionTime, e.getClass().getSimpleName(), e.getMessage());
            return GatewayCallResult.builder().success(false).executionTime(executionTime).message(e.getMessage()).exception(e).build();
        }
    }

}
