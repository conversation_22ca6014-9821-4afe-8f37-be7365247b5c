package com.cmpay.hacp.inspection.infrastructure.rule.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.rule.repository.RuleRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RuleMapper;
import com.cmpay.hacp.inspection.infrastructure.rule.converter.InspectionRuleConverter;
import com.cmpay.hacp.inspection.infrastructure.utils.IdGenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
@RequiredArgsConstructor
public class RuleRepositoryImpl extends CrudRepository<RuleMapper, RuleDO> implements RuleRepository {

    private final InspectionRuleConverter inspectionRuleConverter;

    @Override
    public IPage<InspectionRule> page(Page<RuleDO> mpPage, InspectionRule queryCondition, Set<String> ruleIdSet) {
        // 构造查询条件
        LambdaQueryWrapper<RuleDO> queryWrapper = Wrappers.lambdaQuery(RuleDO.class)
                .like(StringUtils.isNotBlank(queryCondition.getName()), RuleDO::getName, queryCondition.getName())
                .eq(queryCondition.getLevel() != null, RuleDO::getLevel, queryCondition.getLevel())
                .eq(queryCondition.getStatus() != null, RuleDO::getStatus, queryCondition.getStatus())
                .eq(queryCondition.getType() != null, RuleDO::getType, queryCondition.getType())
                .between(queryCondition.getStartTime() != null && queryCondition.getEndTime() != null,
                        RuleDO::getCreatedTime, queryCondition.getStartTime(), queryCondition.getEndTime())
                .orderByAsc(RuleDO::getId);
        if (CollectionUtils.isNotEmpty(ruleIdSet)) {
            queryWrapper.in(RuleDO::getRuleId, ruleIdSet);
        }
        IPage<RuleDO> ruleDOPage = this.page(mpPage, queryWrapper);
        // 转换结果为领域对象
        return ruleDOPage.convert(inspectionRuleConverter::toInspectionRule);
    }

    @Override
    public InspectionRule save(InspectionRule inspectionRule) {
        // 新增重名校验
        if (this.getOne(
                Wrappers.lambdaQuery(RuleDO.class)
                        .eq(RuleDO::getName, inspectionRule.getName())) != null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.PLUGIN_NAME_EXIST);
        }


        // 保存规则基本信息
        RuleDO ruleDO = inspectionRuleConverter.toInspectionRuleDO(inspectionRule);
        ruleDO.setRuleId(IdGenUtil.generateTempId());
        this.save(ruleDO);

        // 业务标识符
        String ruleId = IdGenUtil.generateRuleId(ruleDO.getId());
        ruleDO.setRuleId(ruleId);
        this.update(ruleDO, Wrappers.lambdaUpdate(RuleDO.class)
                .eq(RuleDO::getId, ruleDO.getId())
                .set(RuleDO::getRuleId, ruleDO.getRuleId()));

        inspectionRule.setRuleId(ruleId);
        return inspectionRule;
    }

    @Override
    public boolean checkNameExist(String ruleId, String ruleName) {
        return this.getOne(Wrappers.lambdaQuery(RuleDO.class)
                .eq(RuleDO::getName, ruleName)
                .ne(RuleDO::getRuleId, ruleId)) != null;
    }

    @Override
    public RuleDO findByRuleId(String ruleId) {
        return this.getOne(Wrappers.lambdaQuery(RuleDO.class).
                eq(RuleDO::getRuleId, ruleId));
    }

    @Override
    public boolean update(InspectionRule inspectionRule) {
        // 1. 更新规则的基本信息
        RuleDO ruleDO = inspectionRuleConverter.toInspectionRuleDO(inspectionRule);
        ruleDO.setRuleId(inspectionRule.getRuleId());
        return this.update(ruleDO, Wrappers.lambdaUpdate(RuleDO.class)
                .eq(RuleDO::getRuleId, ruleDO.getRuleId())
                .set(RuleDO::getName, ruleDO.getName())
                .set(RuleDO::getDescription, ruleDO.getDescription())
                .set(RuleDO::getLevel, ruleDO.getLevel())
                .set(RuleDO::getStatus, ruleDO.getStatus())
                .set(RuleDO::getType, ruleDO.getType())
                .set(RuleDO::getDeployEnv, ruleDO.getDeployEnv()));
    }

    @Override
    public void removeByRuleId(String ruleId) {
        this.remove(Wrappers.lambdaQuery(RuleDO.class)
                        .eq(RuleDO::getRuleId, ruleId));
    }

    @Override
    public List<InspectionRule> queryList(InspectionRule queryCondition) {
        // 转换查询条件对象
        RuleDO queryDO = inspectionRuleConverter.toInspectionRuleDO(queryCondition);

        // 构造查询条件
        LambdaQueryWrapper<RuleDO> queryWrapper = Wrappers.lambdaQuery(RuleDO.class)
                .like(StringUtils.isNotBlank(queryCondition.getName()), RuleDO::getName, queryCondition.getName())
                .eq(RuleDO::getStatus, Optional.ofNullable(queryDO.getStatus()).orElse(CommonStatusEnum.ENABLE))
                .eq(queryDO.getType() != null, RuleDO::getType, queryDO.getType())
                .orderByAsc(RuleDO::getId);
        List<RuleDO> ruleDOList = this.list(queryWrapper);

        // 转换结果为领域对象
        return inspectionRuleConverter.toInspectionRuleList(ruleDOList);
    }

    @Override
    public InspectionRule getByRuleId(String ruleId) {
        RuleDO ruleDO = this.getOne(Wrappers.lambdaQuery(RuleDO.class)
                .eq(RuleDO::getRuleId, ruleId));
        return inspectionRuleConverter.toInspectionRule(ruleDO);
    }

    @Override
    public List<RuleDO> listByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(RuleDO.class)
                .in(RuleDO::getRuleId, ruleIds));
    }
}
