package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 判断条件
 */
@Getter
@Slf4j
public enum RuleComparisonOperator {
    GREATER_THAN(1, "大于","小于等于","数值比较"),
    LESS_THAN(2, "小于","大于等于","数值比较"),
    GREATER_THAN_OR_EQUAL(3, "大于等于","小于","数值比较"),
    LESS_THAN_OR_EQUAL(4, "小于等于","大于","数值比较"),
    EQUAL(5, "等于","不等于","相等判断"),
    NOT_EQUAL(6, "不等于","等于","相等判断"),
    STR_CONTAINS(7, "包含","不包含","文本匹配"),
    STR_NOT_CONTAINS(8, "不包含","包含","文本匹配"),
    STR_STARTS_WITH(9, "开头匹配","开头不匹配","文本匹配"),
    STR_ENDS_WITH(10, "结尾匹配","结尾不匹配","文本匹配"),
    STR_MATCHES(11, "正则匹配","小于等于","文本匹配"),
    IS_NULL(12, "为空","不为空","存在性"),
    IS_NOT_NULL(13, "不为空","为空","存在性"),
    EXISTS(14, "存在","不存在","存在性"),
    NOT_EXISTS(15, "不存在","存在","存在性"),

    ;

    @JsonValue
    private final Integer code;
    private final String desc;
    private final String inverseValue;
    private final String type;

    RuleComparisonOperator(Integer code, String desc, String inverseValue, String type) {
        this.code = code;
        this.desc = desc;
        this.inverseValue = inverseValue;
        this.type = type;
    }

    private static final Map<Integer, RuleComparisonOperator> ENUM_MAP = Arrays.stream(RuleComparisonOperator.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static RuleComparisonOperator getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
