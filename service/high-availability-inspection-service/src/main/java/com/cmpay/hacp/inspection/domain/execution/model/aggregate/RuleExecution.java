package com.cmpay.hacp.inspection.domain.execution.model.aggregate;

import com.cmpay.hacp.inspection.application.execution.TargetHost;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceExecutionStrategyEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
public class RuleExecution {
    private String executionId;
    private String taskId;
    private String ruleId;
    private String ruleName;
    private String pluginId;
    private String pluginName;
    private PluginType pluginType;
    private ResourceType resourceType;

    private ResourceExecutionStrategyEnum strategy;

    private ExecutionContext executionContext;

    private TargetHost targetHost;
    private K8sConnectionConfig k8sConfig;

    private LocalDateTime startTime;
    private LocalDateTime endTime;

    private Map<Object,Object> params = new HashMap<>();

    public boolean isResourceBased() {
        return resourceType == ResourceType.VIRTUAL_MACHINE ||
                resourceType == ResourceType.CONTAINER;
    }

    public boolean isEnvironmentIndependent() {
        return resourceType == ResourceType.ENVIRONMENT_INDEPENDENT;
    }

}
