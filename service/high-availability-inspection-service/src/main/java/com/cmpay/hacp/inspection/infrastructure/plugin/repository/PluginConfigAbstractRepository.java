package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.plugin.model.InspectionPlugin;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginConfig;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginConfigRepository;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginOutputFieldRepository;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class PluginConfigAbstractRepository implements PluginConfigRepository {

    private final PluginOutputFieldRepository outputFiledRepository;

    public PluginConfigAbstractRepository(PluginOutputFieldRepository outputFiledRepository) {
        this.outputFiledRepository = outputFiledRepository;
    }

    protected abstract List<PluginType> getPluginsType();


    @Override
    public void remove(String pluginId) {
        removeOther(pluginId);
        outputFiledRepository.removeByPluginId(pluginId);
    }

    protected void removeOther(String pluginId){};

    @Override
    public void savePluginConfig(InspectionPlugin inspectionPlugin, String pluginId) {
        saveOther(inspectionPlugin,pluginId);
        PluginConfig configScript = inspectionPlugin.getPluginConfig();
        // 保存输出字段定义
        if (CollectionUtils.isNotEmpty(configScript.getResults())) {
            outputFiledRepository.saveBatch(configScript.getResults(), pluginId);
        }
    }

    protected void saveOther(InspectionPlugin inspectionPlugin, String pluginId){};

    @Override
    public void updatePluginConfig(InspectionPlugin inspectionPlugin, String pluginId) {
        updateOther(inspectionPlugin,pluginId);
        updateOutputField(inspectionPlugin, pluginId);
    }

    protected void updateOther(InspectionPlugin inspectionPlugin, String pluginId){};

    private void updateOutputField(InspectionPlugin inspectionPlugin, String pluginId) {
        PluginConfig configScript = inspectionPlugin.getPluginConfig();
        // 5. 更新输出字段定义
        if (CollectionUtils.isEmpty(configScript.getResults())) {
            // 如果输出字段为空，直接删除所有相关字段
            outputFiledRepository.removeByPluginId(pluginId);
        } else {
            // 获取当前所有输出字段
            List<PluginOutputField> originList = outputFiledRepository.listByPluginId(pluginId);

            // 创建字段名称到字段对象的映射，提高查找效率
            Map<String, PluginOutputField> originResultMap = originList.stream()
                    .collect(Collectors.toMap(PluginOutputField::getFieldName, Function.identity()));

            List<PluginOutputField> insertList = new ArrayList<>();
            List<PluginOutputField> updateList = new ArrayList<>();
            List<String> deleteFieldNames = new ArrayList<>();

            // 设置将要保留的字段名集合，用于判断删除
            Set<String> retainedFieldNames = new HashSet<>();

            // 处理新增和更新
            for (PluginOutputField scriptResult : configScript.getResults()) {
                String fieldName = scriptResult.getFieldName();
                retainedFieldNames.add(fieldName);

                PluginOutputField existingResult = originResultMap.get(fieldName);
                if (existingResult == null) {
                    // 新增字段
                    insertList.add(scriptResult);
                } else {
                    scriptResult.setId(existingResult.getId());
                    // 更新字段
                    updateList.add(scriptResult);
                }
            }

            // 确定要删除的字段
            originResultMap.forEach((fieldName, result) -> {
                if (!retainedFieldNames.contains(fieldName)) {
                    deleteFieldNames.add(result.getFieldName());
                }
            });

            if (!insertList.isEmpty()) {
                outputFiledRepository.saveBatch(insertList, pluginId);
            }
            if (!updateList.isEmpty()) {
                outputFiledRepository.updateBatchByName(updateList, pluginId);
            }
            if (!deleteFieldNames.isEmpty()) {
                outputFiledRepository.removeByFieldNames(deleteFieldNames, pluginId);
            }
        }
    }

    @Override
    public PluginConfig queryPluginConfig(String pluginId) {

        PluginConfig pluginConfig = queryOther(pluginId);
        List<PluginOutputField> pluginOutputFieldList = outputFiledRepository.listByPluginId(pluginId);
        pluginConfig.setResults(pluginOutputFieldList);
        return pluginConfig;
    }

    protected abstract PluginConfig queryOther(String pluginId);

    @Override
    public void afterPropertiesSet() throws Exception {
        PluginConfigStrategyFactory.addStrategy(getPluginsType(),this);
    }
}
