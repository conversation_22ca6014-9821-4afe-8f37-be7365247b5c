package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptParameter;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginScriptParameterConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptParameterDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginScriptParameterMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PluginScriptParameterRepositoryImpl implements PluginScriptParameterRepository {
    private final PluginScriptParameterMapper mapper;
    private final PluginScriptParameterConverter pluginScriptParameterConverter;
    private final SqlSessionFactory sqlSessionFactory;

    @Override
    public void removeByPluginId(String pluginId) {
        mapper.delete(Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                .eq(PluginScriptParameterDO::getPluginId, pluginId));
    }

    @Override
    public void saveBatch(List<PluginScriptParameter> parameters, String pluginId) {
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        List<PluginScriptParameterDO> results = parameters.stream()
                .map(scriptResult -> pluginScriptParameterConverter.toPluginScriptParameterDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        // 使用 MyBatis-Plus 的批量执行工具，真正的批量插入
        SqlHelper.executeBatch(sqlSessionFactory, (Log) log, results, 20, (sqlSession, entity) -> {
            PluginScriptParameterMapper batchMapper = sqlSession.getMapper(PluginScriptParameterMapper.class);
            batchMapper.insert(entity);
        });
    }

    @Override
    public List<PluginScriptParameter> listByPluginId(String pluginId) {
        return pluginScriptParameterConverter.toPluginScriptParameterList(mapper.selectList(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId)));
    }

    @Override
    public void removeByPluginIdAndParamNames(String pluginId, List<String> deleteParamNames) {
        mapper.delete(Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                .eq(PluginScriptParameterDO::getPluginId, pluginId)
                .in(PluginScriptParameterDO::getParamName, deleteParamNames));
    }

    @Override
    public void updateBatchByPluginIdAndParamName(List<PluginScriptParameter> parameters, String pluginId) {
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        // 根据业务主键（pluginId + paramName）逐个更新记录
        for (PluginScriptParameter parameter : parameters) {
            PluginScriptParameterDO updateDO = pluginScriptParameterConverter.toPluginScriptParameterDO(parameter, pluginId);

            mapper.update(updateDO, Wrappers.lambdaUpdate(PluginScriptParameterDO.class)
                    .eq(PluginScriptParameterDO::getPluginId, pluginId)
                    .eq(PluginScriptParameterDO::getParamName, parameter.getParamName()));
        }
    }

    @Override
    public Map<String, Object> getParameterMapByPluginId(String pluginId) {
        List<PluginScriptParameterDO> list = mapper.selectList(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .select(PluginScriptParameterDO::getParamName, PluginScriptParameterDO::getParamValue)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId)
        );

        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(
                PluginScriptParameterDO::getParamName,
                item -> item.getParamValue() != null ? item.getParamValue() : "",
                (existing, replacement) -> replacement
        ));
    }
}
