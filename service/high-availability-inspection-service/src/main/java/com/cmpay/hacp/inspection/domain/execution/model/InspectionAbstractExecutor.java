package com.cmpay.hacp.inspection.domain.execution.model;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Slf4j
public abstract class InspectionAbstractExecutor implements InspectionExecutor{
    private final RuleMatchingService ruleMatchingService;

    public InspectionAbstractExecutor(RuleMatchingService ruleMatchingService) {
        this.ruleMatchingService = ruleMatchingService;
    }

    @Override
    public InspectionResult execute(RuleExecution ruleExecution) {
        if(!verifyParam(ruleExecution)){
            return verifyFailMessage(ruleExecution);
        }
        executeBefore(ruleExecution);
        ExecutionResult result = doExecute(ruleExecution);
        executeAfter(ruleExecution,result);
        RuleMatchingResult ruleMatchingResult = matchRule(ruleExecution,result);

        return processResult(ruleExecution,result,ruleMatchingResult);
    }

    protected abstract InspectionResult verifyFailMessage(RuleExecution ruleExecution);

    /**
     * 构建脚本执行失败结果
     */
    protected abstract InspectionResult buildExecuteFailureResult(RuleExecution ruleExecution, ExecutionResult result);

    protected abstract boolean verifyParam(RuleExecution ruleExecution);
    protected void executeAfter(RuleExecution ruleExecution, ExecutionResult result){}

    protected void executeBefore(RuleExecution ruleExecution){}

    protected abstract ExecutionResult doExecute(RuleExecution ruleExecution);

    private InspectionResult processResult(RuleExecution ruleExecution, ExecutionResult result, RuleMatchingResult ruleMatchingResult) {
        // 构建最终结果
        boolean executionSuccess = result.isSuccess();
        if(!executionSuccess){
            return buildExecuteFailureResult(ruleExecution,result);
        }

        boolean ruleMatchingResultSuccess = ruleMatchingResult.isSuccess();
        boolean overallSuccess = executionSuccess && ruleMatchingResultSuccess;
        String message= buildResultMessage(executionSuccess, ruleMatchingResult);
        String details= buildResultDetails(result, ruleMatchingResult);

        return InspectionResult.builder()
                .success(overallSuccess)
                .scriptExecutionSuccess(executionSuccess)
                .ruleMatchingSuccess(ruleMatchingResultSuccess)
                .message(message)
                .details(details)
                .pluginName(ruleExecution.getPluginName())
                .ruleMatchingResult(ruleMatchingResult)
                .build();
    }

    protected RuleMatchingResult matchRule(RuleExecution ruleExecution, ExecutionResult result) {
        boolean scriptExecutionSuccess = result.isSuccess();
        log.debug("Script execution completed: success={}, exitCode={}", scriptExecutionSuccess, result.getExitCode());

        // 巡检规则匹配
        RuleMatchingResult ruleMatchingResult = null;
        if (scriptExecutionSuccess && StringUtils.hasText(result.getStdout())) {

            String ruleId = ruleExecution.getRuleId();
            String pluginId = ruleExecution.getPluginId();
            try {
                ruleMatchingResult = ruleMatchingService.matchRule(ruleId, pluginId, result.getStdout());
                log.debug("Rule matching completed: success={}, message={}, errormessage={}", ruleMatchingResult.isSuccess(), ruleMatchingResult.getMessage(), ruleMatchingResult.getErrorMessage());
            } catch (Exception e) {
                log.error("Error during rule matching for ruleId: {}, pluginId: {}", ruleId, pluginId, e);
                ruleMatchingResult = RuleMatchingResult.builder()
                        .success(false)
                        .ruleId(ruleId)
                        .pluginId(pluginId)
                        .errorMessage("Rule matching error: " + e.getMessage())
                        .build();
            }
        } else {
            log.warn("Skipping rule matching due to script execution failure or empty output");
        }
        return ruleMatchingResult;
    }

    protected abstract String getExecutionMsg();

    /**
     * 构建结果消息
     */
    protected String buildResultMessage(boolean executionSuccess,RuleMatchingResult ruleMatchingResult) {
        if (!executionSuccess) {
            return getExecutionMsg();
        }

        if (StringUtils.hasText(ruleMatchingResult.getErrorMessage())) {
            return Optional.ofNullable(ruleMatchingResult.getErrorMessage()).filter(StringUtils::hasText).orElse("巡检失败");
        }
        if (ruleMatchingResult.isSuccess()) {
            return Optional.ofNullable(ruleMatchingResult.getMessage()).filter(StringUtils::hasText).orElse("巡检失败");
        }

        return Optional.ofNullable(ruleMatchingResult.getMessage()).filter(StringUtils::hasText).orElse("巡检通过");
    }

    /**
     * 构建结果详情
     */
    protected String buildResultDetails(ExecutionResult result, RuleMatchingResult ruleMatchingResult) {
        StringBuilder details = new StringBuilder();

        // 脚本执行信息
        details.append("=== 执行结果 ===\n");
        details.append("执行状态: ").append(result.isSuccess() ? "成功" : "失败").append("\n");
        if(result.getExitCode()!=null){
            details.append("退出码: ").append(result.getExitCode()).append("\n");
        }
        details.append("执行时间: ").append(result.getExecutionTime()).append("ms\n");

        if (StringUtils.hasText(result.getStdout())) {
            details.append("标准输出:\n").append(result.getStdout()).append("\n");
        }

        if (StringUtils.hasText(result.getStderr())) {
            details.append("错误输出:\n").append(result.getStderr()).append("\n");
        }

        // 规则匹配信息
        if (ruleMatchingResult != null) {
            details.append("\n=== 规则匹配结果 ===\n");
            details.append("检查状态：").append(!ruleMatchingResult.isSuccess() ? "通过" : "告警").append("\n");

            if (StringUtils.hasText(ruleMatchingResult.getMessage())) {
                details.append("匹配信息: ").append("\n").append(ruleMatchingResult.getMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getErrorMessage())) {
                details.append("错误信息: ").append("\n").append(ruleMatchingResult.getErrorMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getSuggest())) {
                details.append("治理建议: ").append(ruleMatchingResult.getSuggest()).append("\n");
            }
        }

        return details.toString();
    }
}
