package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.emergency.bo.SubHostInfoBO;
import com.cmpay.hacp.emergency.service.SysConfigHostService;
import com.cmpay.hacp.extend.container.service.HacpKubesphereService;
import com.cmpay.hacp.inspection.application.execution.TargetHost;
import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceExecutionStrategyEnum;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.task.model.ContainerResource;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.VmResource;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleResourceRepository;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 插件执行服务 - 领域服务
 */
@Service
@Slf4j
public class PluginExecutionDomainService {
    private final ExecutorDomainService executorDomainService;
    private final Executor executor;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final InspectionTaskMonitoringService monitoringService;
    private final SysConfigHostService sysConfigHostService;
    private final HacpKubesphereService hacpKubesphereService;

    public PluginExecutionDomainService(ExecutorDomainService executorDomainService,
                                        @Qualifier("asyncInspectionHostExecutor") Executor executor, TaskRuleResourceRepository taskRuleResourceRepository, InspectionTaskMonitoringService monitoringService, SysConfigHostService sysConfigHostService,HacpKubesphereService hacpKubesphereService) {
        this.executorDomainService = executorDomainService;
        this.executor = executor;
        this.taskRuleResourceRepository = taskRuleResourceRepository;
        this.monitoringService = monitoringService;
        this.sysConfigHostService = sysConfigHostService;
        this.hacpKubesphereService = hacpKubesphereService;
    }

    public void executeForResources(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 获取检查执行器
        InspectionExecutor inspectionExecutor = executorDomainService.selectOptimalExecutor(ruleExecution.getExecutionContext());

        List<Resource> resourceList = taskRuleResourceRepository.list(ruleExecution.getTaskId(), ruleExecution.getRuleId());
        if (resourceList.isEmpty()) {
            log.warn("No resources found for rule: {}", ruleExecution.getRuleId());
            return;
        }

        // 主机维度的成功失败统计
        AtomicInteger hostSuccessCount = new AtomicInteger(0);
        AtomicInteger hostFailCount = new AtomicInteger(0);
        String workspaceId = TenantUtils.getWorkspaceId();
        // 2. 获取目标主机列表
        try {
            // 为每个资源（主机）创建CompletableFuture任务
            List<CompletableFuture<InspectionResult>> futures = resourceList.stream().flatMap(resource -> {
                        if (resource instanceof VmResource) {
                            List<TargetHost> targetHosts;
                            try {
                                targetHosts = convertToTargetHostList((VmResource) resource);
                            }catch (Exception e) {
                                log.error("error:",e);
                                return Stream.of(CompletableFuture.supplyAsync(()-> expHandler(ruleExecution, resource, "调用远程API查询主机失败: " + e.getMessage(),workspaceId),executor));
                            }
                            return targetHosts.stream().map(targetHost -> {
                                ruleExecution.setTargetHost(targetHost);
                                return CompletableFuture.supplyAsync(() -> executeForResource(ruleExecution, resource, inspectionExecutor,workspaceId), executor);
                            });
                        } else if (resource instanceof ContainerResource) {
                            // 3. 获取K8s连接配置
                            List<K8sConnectionConfig> k8sConnectionConfigs;
                            try {
                                k8sConnectionConfigs = buildK8sConnectionConfig(TenantUtils.getWorkspaceId(),
                                        (ContainerResource) resource);
                            }catch (Exception e) {
                                log.error("error:",e);
                                return Stream.of(CompletableFuture.supplyAsync(()-> expHandler(ruleExecution, resource, "调用远程API查询K8S配置失败: " + e.getMessage(),workspaceId),executor));
                            }
                            return k8sConnectionConfigs.stream().map(k8sConnectionConfig -> {
                                ruleExecution.setK8sConfig(k8sConnectionConfig);
                                return CompletableFuture.supplyAsync(() -> executeForResource(ruleExecution, resource, inspectionExecutor,workspaceId), executor);
                            });
                        }
                        return Stream.empty();
                    })
                    .collect(Collectors.toList());

            // 等待所有主机执行完成并收集结果
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计主机维度和规则维度的结果
            boolean ruleSuccess = true;
            for (CompletableFuture<InspectionResult> future : futures) {
                try {
                    InspectionResult result = future.get();
                    if (result.isScriptExecutionSuccess()) {
                        hostSuccessCount.incrementAndGet();
                    } else {
                        hostFailCount.incrementAndGet();
                        ruleSuccess = false;
                    }
                } catch (Exception e) {
                    log.error("Error getting inspection result", e);
                    hostFailCount.incrementAndGet();
                    ruleSuccess = false;
                }
            }

            // 规则维度统计：只有当所有主机都成功时，规则才算成功
            // 成功代表符合该规则
            if (ruleSuccess) {
                successCount.incrementAndGet();
            } else {
                failCount.incrementAndGet();
            }

            log.info("Rule execution completed: {}, ruleSuccess: {}, hostSuccessCount: {}, hostFailCount: {}",
                    ruleExecution.getRuleId(), ruleSuccess, hostSuccessCount.get(), hostFailCount.get());

        } catch (Exception e) {
            log.error("Error executing rule: {} for task: {}", ruleExecution.getRuleId(), ruleExecution.getTaskId(), e);
            failCount.incrementAndGet();
        }


    }

    public void executeSystemMetric(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 获取检查执行器
        InspectionExecutor inspectionExecutor = executorDomainService.selectOptimalExecutor(ruleExecution.getExecutionContext());

        // 执行巡检
        InspectionResult result = inspectionExecutor.execute(ruleExecution);
    }

    private InspectionResult expHandler(RuleExecution ruleExecution,Resource resource,String errorMessage,String workspaceId){
        TenantUtils.setWorkspaceIdAndSysInfo(workspaceId);
        InspectionResult result = InspectionResult.builder()
                .success(false)
                .ruleMatchingSuccess(false)
                .executionDuration(0L)
                .ruleMatchingResult(null)
                .scriptExecutionSuccess(false)
                .pluginName(ruleExecution.getPluginName())
                .details(errorMessage)
                .build();
        // 按单一主机记录执行结果，包含资源信息
        recordTheResultsOfTheExecution(ruleExecution, resource, result);
        return result;
    }

    private void recordTheResultsOfTheExecution(RuleExecution ruleExecution, Resource resource, InspectionResult result) {
        monitoringService.recordRuleExecutionResult(ruleExecution.getTaskId(), ruleExecution.getExecutionId(), ruleExecution.getRuleName(), ruleExecution.getPluginId(), result, resource, resource.getResourceName());
    }

    private InspectionResult executeForResource(RuleExecution ruleExecution, Resource resource, InspectionExecutor inspectionExecutor,String workspaceId) {
        TenantUtils.setWorkspaceIdAndSysInfo(workspaceId);
        try {
            long start = System.currentTimeMillis();
            // 执行巡检
            InspectionResult result = inspectionExecutor.execute(ruleExecution);
            result.setExecutionDuration(System.currentTimeMillis() - start);
            // 按单一主机记录执行结果，包含资源信息
            recordTheResultsOfTheExecution(ruleExecution, resource, result);
            return result;

        } catch (Exception e) {
            log.error("Error executing plugin: {} for resource: {}", ruleExecution.getPluginId(), resource.getResourceName(), e);

            // 记录失败结果
            InspectionResult failResult = InspectionResult.builder()
                    .success(false)
                    .message("Execution error")
                    .details(e.getMessage())
                    .pluginName(ruleExecution.getPluginName())
                    .build();

            monitoringService.recordRuleExecutionResult(ruleExecution.getTaskId(), ruleExecution.getExecutionId(), ruleExecution.getRuleName(), ruleExecution.getPluginId(), failResult,
                    resource, resource.getResourceName());

            return failResult;
        }
    }

    private List<TargetHost> convertToTargetHostList(VmResource vmResource) {
        List<SubHostInfoBO> subHostInfoBOs = sysConfigHostService.queryHostList(Collections.singletonList(vmResource.getResourceId()), TenantUtils.getWorkspaceId());
        if (CollectionUtils.isEmpty(subHostInfoBOs)) {
            return new ArrayList<>();
        }
        if(vmResource.getStrategy().equals(ResourceExecutionStrategyEnum.RANDOM)){
            SubHostInfoBO targetHost = subHostInfoBOs.get(RandomUtil.getRandomNumber(subHostInfoBOs.size()));
            subHostInfoBOs.clear();
            subHostInfoBOs.add(targetHost);
        }
        return subHostInfoBOs.stream().map(subHostInfoBO -> TargetHost.builder()
                .authType(StringUtils.isBlank(subHostInfoBO.getHostPassword()) ? TargetHost.AuthType.PRIVATE_KEY : TargetHost.AuthType.PASSWORD)
                .hostId(subHostInfoBO.getHostId().toString())
                .hostName(subHostInfoBO.getHostDesc())
                .host(subHostInfoBO.getHostAddress())
                .port(subHostInfoBO.getHostPort())
                .username(subHostInfoBO.getHostUsername())
                .password(subHostInfoBO.getHostPassword())
                .build())
                .collect(Collectors.toList());
    }


    /**
     * 构建K8s连接配置
     */
    private List<K8sConnectionConfig> buildK8sConnectionConfig(String  workspaceId,ContainerResource containerResource) {
        if (containerResource == null) {
            log.error("无法构建K8s连接配置，容器资源信息为空");
            return new ArrayList<>();
        }
        List<K8sConnectionConfig> config = queryPodDynamically(workspaceId, containerResource);
        if (config != null) {
            return config;
        }

        log.error("无法构建K8s连接配置，所有方式都失败");
        return new ArrayList<>();
    }

    /**
     * 动态查询Pod
     */
    private List<K8sConnectionConfig> queryPodDynamically(String workspaceId,ContainerResource resource) {
        try {
            // 查询Pod列表
            String appName = resource.getResourceName();
            String cluster = resource.getCluster();
            String namespace = resource.getNamespace();
            Map<String, List<String>> pods = hacpKubesphereService.queryPods(workspaceId, cluster, namespace, appName);
            if (pods == null || pods.isEmpty()) {
                log.warn("未查询到Pod列表，cluster: {}, namespace: {}", cluster, namespace);
                return null;
            }

            // 如果指定了appName，优先查找对应的Pod
            if (org.springframework.util.StringUtils.hasText(appName) && pods.containsKey(appName)) {
                List<String> appPods = pods.get(appName);
                if (appPods.isEmpty()) {
                    log.warn("未查询到Pod列表，cluster: {}, namespace: {}, appName: {}", cluster, namespace, appName);
                    return null;
                }
                if(resource.getStrategy().equals(ResourceExecutionStrategyEnum.RANDOM)){
                    int randomIndex = RandomUtil.getRandomNumber(appPods.size());
                    K8sConnectionConfig build = K8sConnectionConfig.builder()
                            .cluster(cluster)
                            .namespace(namespace)
                            .podName(appPods.get(randomIndex))
                            .workspaceId(workspaceId)
                            .workingDirectory("/tmp")
                            .build();
                    return Collections.singletonList(build);
                }
                List<K8sConnectionConfig> result = appPods.stream().map(podName ->
                        K8sConnectionConfig.builder()
                                .cluster(cluster)
                                .namespace(namespace)
                                .podName(podName)
                                .workspaceId(workspaceId)
                                .workingDirectory("/tmp")
                                .build()
                ).collect(Collectors.toList());
                String podName = appPods.get(0); // 取第一个Pod
                log.info("动态查询到Pod: {}/{}/{}", cluster, namespace, podName);

                return result;
            }

        } catch (Exception e) {
            log.warn("动态查询Pod失败", e);
        }

        return null;
    }
}
