package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportGenerationService;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.GenerateState;
import com.cmpay.hacp.inspection.domain.model.enums.LevelEnum;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatusEnum;
import com.cmpay.hacp.inspection.domain.task.model.TaskExecution;
import com.cmpay.hacp.inspection.domain.task.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.report.repository.DailyInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.report.repository.DailyInspectionReportRepositoryImpl;
import com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.report.repository.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleExecutionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按日报告生成服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportGenerationServiceImpl implements DailyInspectionReportGenerationService {

    private final DailyInspectionReportRepositoryImpl dailyInspectionReportRepositoryImpl;
    private final DailyInspectionReportDetailRepository dailyInspectionReportDetailRepository;
    private final TaskExecutionRepository taskExecutionRepository;
    private final RuleExecutionRepository ruleExecutionRepository;

    private static final DateTimeFormatter DATE_LOCAL_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");
    private static final DateTimeFormatter DATE_SHORT_LOCAL_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String MS_UNIT = "ms";
    private static final String PERCENT_SIGN_UNIT = "%";
    private static final String PIECE_UNIT = "个";
    private static final String ID_PREFIX = "DR";

    @Override
    @Transactional
    public void generateDailyReport(LocalDate reportDate, Integer triggerType) {
        try {
            // 1. 检查是否已存在该日期的报告
            DailyInspectionReportDO existingReport = getDailyReportByDate(reportDate);
            if (null != existingReport && GenerateState.COMPLETED.equals(existingReport.getReportStatus())) {
                log.info("Daily report already exists for date: {}, reportId: {}", reportDate, existingReport.getReportId());
                return;
            }

            // 2. 获取该日期的所有任务执行记录
            List<TaskExecution> taskExecutions = getTaskExecutionsByDate(reportDate);
            if (taskExecutions.isEmpty()) {
                log.warn("No task executions found for date: {}", reportDate);
                createEmptyDailyReport(reportDate);
                return;
            }

            // 3. 获取所有相关的规则执行记录
            List<String> taskExecutionIds = taskExecutions.stream()
                    .map(TaskExecution::getExecutionId)
                    .collect(Collectors.toList());

            List<RuleExecutionDO> ruleExecutions = getRuleExecutionsByTaskIds(taskExecutionIds);

            // 4. 创建或更新按日报告
            DailyInspectionReportDO dailyReport = existingReport != null ? existingReport : createNewDailyReport(reportDate);

            // 5. 设置生成状态
            dailyReport.setReportStatus(GenerateState.GENERATING);

            // 6. 计算汇总统计数据
            calculateSummaryStatistics(dailyReport, taskExecutions, ruleExecutions);

            // 7. 构建详细内容
            DailyInspectionReportDetailDO detail = buildDailyReportContent(reportDate, dailyReport, ruleExecutions);
            // 8. 设置完成状态
            dailyReport.setReportStatus(GenerateState.COMPLETED);

            // 9. 保存报告
            dailyInspectionReportRepositoryImpl.saveOrUpdate(dailyReport);

            detail.setReportId(dailyReport.getReportId());
            dailyInspectionReportDetailRepository.saveOrUpdate(detail);

            log.info("Daily report generated successfully for date: {}, reportId: {}",
                    reportDate, dailyReport.getReportId());


        } catch (Exception e) {
            log.error("Failed to generate daily report for date: {}", reportDate, e);

            // 更新失败状态
            DailyInspectionReportDO failedReport = getDailyReportByDate(reportDate);
            if (failedReport != null) {
                failedReport.setReportStatus(GenerateState.FAILED);
                dailyInspectionReportRepositoryImpl.saveOrUpdate(failedReport);
            }

            throw new RuntimeException("Failed to generate daily report for date: " + reportDate, e);
        }
    }

    /**
     * 获取指定日期的按日报告
     */
    private DailyInspectionReportDO getDailyReportByDate(LocalDate reportDate) {
        return dailyInspectionReportRepositoryImpl.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                        .eq(DailyInspectionReportDO::getReportDate, reportDate)
        );
    }

    /**
     * 获取指定日期的所有任务执行记录
     */
    private List<TaskExecution> getTaskExecutionsByDate(LocalDate reportDate) {
        LocalDateTime startOfDay = reportDate.atStartOfDay();
        LocalDateTime endOfDay = reportDate.plusDays(1).atStartOfDay();

        return taskExecutionRepository.listByExecutionTimeRange(startOfDay, endOfDay);
    }

    /**
     * 根据任务执行ID列表获取规则执行记录
     */
    private List<RuleExecutionDO> getRuleExecutionsByTaskIds(List<String> taskExecutionIds) {
        if (taskExecutionIds.isEmpty()) {
            return new ArrayList<>();
        }

        return ruleExecutionRepository.list(
                Wrappers.lambdaQuery(RuleExecutionDO.class)
                        .in(RuleExecutionDO::getTaskExecutionId, taskExecutionIds)
                        .orderBy(true, true, RuleExecutionDO::getExecutionTime)
        );
    }

    /**
     * 创建空的按日报告（当没有任务执行记录时）
     */
    private void createEmptyDailyReport(LocalDate reportDate) {
        DailyInspectionReportDO dailyReport = createNewDailyReport(reportDate);
        dailyReport.setReportStatus(GenerateState.COMPLETED);

        // 设置所有统计字段为0
        dailyReport.setRuleCount(0);
        dailyReport.setExecutionCount(0);
        dailyReport.setWarningCount(0);
        dailyReport.setFailedCount(0);
        dailyReport.setPassRate(0.0);
        dailyReport.setPassRateChange(0.0);
        dailyReport.setAverageResponseTime(0L);
        dailyReport.setResponseTimeChange(0L);
        dailyReport.setTotalExceptionCount(0);

        dailyInspectionReportRepositoryImpl.save(dailyReport);
    }

    /**
     * 创建新的按日报告对象
     */
    private DailyInspectionReportDO createNewDailyReport(LocalDate reportDate) {
        DailyInspectionReportDO dailyReport = new DailyInspectionReportDO();
        dailyReport.setReportDate(reportDate);
        dailyReport.setReportId(generateDailyReportId(reportDate));
        dailyReport.setReportStatus(GenerateState.GENERATING);
        return dailyReport;
    }

    /**
     * 生成按日报告ID
     */
    private String generateDailyReportId(LocalDate reportDate) {
        return ID_PREFIX + reportDate.format(DATE_SHORT_LOCAL_FORMATTER);
    }

    /**
     * 计算汇总统计数据
     */
    private void calculateSummaryStatistics(DailyInspectionReportDO dailyReport, List<TaskExecution> taskExecutions, List<RuleExecutionDO> ruleExecutions) {
        // 基础统计
        dailyReport.setExecutionCount(ruleExecutions.size());

        // 统计规则数量（去重）
        Set<String> uniqueRules = ruleExecutions.stream()
                .map(RuleExecutionDO::getRuleName)
                .collect(Collectors.toSet());
        dailyReport.setRuleCount(uniqueRules.size());

        Set<String> uniqueTasks = taskExecutions.stream()
                .map(TaskExecution::getTaskName)
                .collect(Collectors.toSet());
        dailyReport.setTaskCount(uniqueTasks.size());
        // 统计规则执行结果
        int totalRuleExecutions = ruleExecutions.size();
        // 成功->匹配 ->告警    成功->不匹配->成功     不成功->失败
        long successRuleExecutions = ruleExecutions.stream()
                .filter(rule -> ExecutionResult.PASS.equals(rule.getExecutionStatus()) && !rule.isMatchStatus())
                .count();
        long warningRuleExecutions = ruleExecutions.stream()
                .filter(rule -> ExecutionResult.PASS.equals(rule.getExecutionStatus()) && rule.isMatchStatus())
                .count();
        long failedRuleExecutions = ruleExecutions.stream()
                .filter(rule -> ExecutionResult.FAIL.equals(rule.getExecutionStatus()))
                .count();

        // 计算通过率
        double passRate = totalRuleExecutions > 0 ? (successRuleExecutions * 100.0) / totalRuleExecutions : 0.0;
        dailyReport.setPassRate(new BigDecimal(passRate).setScale(2, RoundingMode.HALF_UP).doubleValue());

        // 统计异常数量（失败的规则执行视为异常）
        dailyReport.setFailedCount((int) failedRuleExecutions);
        dailyReport.setWarningCount((int) warningRuleExecutions);
        dailyReport.setTotalExceptionCount((int) failedRuleExecutions+(int)warningRuleExecutions);

        // 计算平均响应时间
        OptionalDouble avgResponseTime = ruleExecutions.stream()
                .filter(rule -> rule.getExecutionDuration() != null)
                .mapToLong(RuleExecutionDO::getExecutionDuration)
                .average();
        dailyReport.setAverageResponseTime(avgResponseTime.isPresent() ? (long) avgResponseTime.getAsDouble() : 0L);

        // 计算昨日对比数据（通过率变化和响应时间变化）
        calculateTrendChanges(dailyReport);
    }

    /**
     * 计算趋势变化数据
     */
    private void calculateTrendChanges(DailyInspectionReportDO dailyReport) {
        LocalDate yesterdayDate = dailyReport.getReportDate().minusDays(1);
        DailyInspectionReportDO yesterdayReport = getDailyReportByDate(yesterdayDate);

        if (yesterdayReport != null) {
            // 计算通过率变化
            double passRateChange = dailyReport.getPassRate() - yesterdayReport.getPassRate();
            dailyReport.setPassRateChange(passRateChange);

            // 计算响应时间变化
            long responseTimeChange = dailyReport.getAverageResponseTime() - yesterdayReport.getAverageResponseTime();
            dailyReport.setResponseTimeChange(responseTimeChange);
        } else {
            dailyReport.setPassRateChange(0.0);
            dailyReport.setResponseTimeChange(0L);
        }
    }

    /**
     * 构建按日报告详细内容
     */
    private DailyInspectionReportDetailDO buildDailyReportContent(LocalDate reportDate,DailyInspectionReportDO dailyReport, List<RuleExecutionDO> ruleExecutions) {
        return DailyInspectionReportDetailDO.builder()
                .categoryStatistics(buildCategoryStatistics(ruleExecutions))
                .passRateTrends(buildRecentPassRateTrends(reportDate,dailyReport))
                .exceptionDetails(buildExceptionDetails(ruleExecutions))
                .trendAnalysis(buildTrendAnalysis(reportDate,dailyReport))
                .build();
    }

    /**
     * 构建巡检分类统计
     */
    private List<DailyInspectionReportDetailDO.CategoryStatistics> buildCategoryStatistics(List<RuleExecutionDO> ruleExecutions) {
        // 按插件名称分类统计（可以根据实际业务调整分类逻辑）
        Map<String, List<RuleExecutionDO>> categoryGroups = ruleExecutions.stream()
                .collect(Collectors.groupingBy(rule ->
                        rule.getPluginName() != null ? rule.getPluginName() : CommonConstant.OTHER_CN));// FIXME 规则类型

        List<DailyInspectionReportDetailDO.CategoryStatistics> categoryStatistics = new ArrayList<>();
        int totalChecks = ruleExecutions.size();

        for (Map.Entry<String, List<RuleExecutionDO>> entry : categoryGroups.entrySet()) {
            String categoryName = entry.getKey();
            List<RuleExecutionDO> categoryRules = entry.getValue();
            int checkCount = categoryRules.size();
            double percentage = new BigDecimal(checkCount * 100.0 / totalChecks)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue();
            categoryStatistics.add(DailyInspectionReportDetailDO.CategoryStatistics.builder()
                    .categoryName(categoryName)
                    .checkCount(checkCount)
                    .percentage(percentage)
                    .build());
        }

        return categoryStatistics;
    }

    /**
     * 构建近7日通过率趋势
     */
    private List<DailyInspectionReportDetailDO.DailyPassRateTrend> buildRecentPassRateTrends(LocalDate reportDate,DailyInspectionReportDO dailyReport) {
        List<DailyInspectionReportDetailDO.DailyPassRateTrend> trends = new ArrayList<>();

        // 获取近7日的数据
        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);

            DailyInspectionReportDO historicalReport = dailyInspectionReportRepositoryImpl.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getPassRate)
                            .eq(DailyInspectionReportDO::getReportDate, date));
            if(i ==0){
                historicalReport = dailyReport;
            }

            double passRate = 0.0;
            String level = LevelEnum.BAD.getLabel();

            if (historicalReport != null && historicalReport.getPassRate() != null) {
                passRate = historicalReport.getPassRate();
            }

            // 根据通过率确定级别
            if (passRate >= 95) {
                level = LevelEnum.EXCELLENT.getLabel();
            } else if (passRate >= 90) {
                level = LevelEnum.GOOD.getLabel();
            } else if (passRate >= 80) {
                level = LevelEnum.MEDIUM.getLabel();
            }

            trends.add(DailyInspectionReportDetailDO.DailyPassRateTrend.builder()
                    .date(date)
                    .passRate(passRate)
                    .level(level)
                    .build());
        }

        return trends;
    }

    /**
     * 构建异常详情
     */
    private DailyInspectionReportDetailDO.ExceptionDetails buildExceptionDetails(List<RuleExecutionDO> ruleExecutions) {
        // 统计异常数量
        List<RuleExecutionDO> exceptionRules = ruleExecutions.stream()
                .filter(rule -> !ReportStatusEnum.PASSED.equals(rule.getReportStatus()))
                .collect(Collectors.toList());

        int totalCount = exceptionRules.size();
        int errorCount = (int) exceptionRules.stream()
                .filter(rule -> ReportStatusEnum.FAILED.equals(rule.getReportStatus()))
                .count();
        int warningCount = totalCount - errorCount;

        // 构建异常项列表
        List<DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem> exceptions = exceptionRules.stream()
                .map(rule -> DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem.builder()
                        .ruleExecutionId(rule.getId())
                        .status(rule.getReportStatus())
                        .title(rule.getRuleName())
                        .description(rule.getExecutionResult())
                        .resourceType(rule.getResourceType())
                        .resourceName(rule.getResourceName())
                        .suggest(rule.getSuggest())
                        .conditionMsg(rule.getConditionMsg())
                        .occurTime(rule.getExecutionTime())
                        .details(rule.getDetails())
                        .build())
                .collect(Collectors.toList());

        return DailyInspectionReportDetailDO.ExceptionDetails.builder()
                .totalCount(totalCount)
                .errorCount(errorCount)
                .warningCount(warningCount)
                .exceptions(exceptions)
                .build();
    }

    /**
     * 构建趋势分析
     */
    private DailyInspectionReportDetailDO.TrendAnalysis buildTrendAnalysis(LocalDate reportDate,DailyInspectionReportDO dailyReport) {
        // 构建通过率趋势
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> passRateTrend = buildPassRateTrendPoints(reportDate,dailyReport);

        // 构建响应时间趋势
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> responseTimeTrend = buildResponseTimeTrendPoints(reportDate,dailyReport);

        // 构建异常数量趋势
        DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend exceptionTrend = buildExceptionTrend(reportDate,dailyReport);

        return DailyInspectionReportDetailDO.TrendAnalysis.builder()
                .passRateTrend(passRateTrend)
                .responseTimeTrend(responseTimeTrend)
                .exceptionTrend(exceptionTrend)
                .build();
    }

    /**
     * 构建通过率趋势点
     */
    private List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> buildPassRateTrendPoints(LocalDate reportDate, DailyInspectionReportDO dailyReport) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> trendPoints = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepositoryImpl.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getPassRate)
                            .eq(DailyInspectionReportDO::getReportDate, date));
            if(i ==0){
                historicalReport = dailyReport;
            }
            double passRate = 0.0;
            if (historicalReport != null && historicalReport.getPassRate() != null) {
                passRate = historicalReport.getPassRate();
            }

            trendPoints.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date)
                    .dateDisplay(date.format(DATE_LOCAL_FORMATTER))
                    .value(passRate)
                    .unit(PERCENT_SIGN_UNIT)
                    .build());
        }
        return trendPoints;
    }

    /**
     * 构建响应时间趋势点
     */
    private List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> buildResponseTimeTrendPoints(LocalDate reportDate, DailyInspectionReportDO dailyReport) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> trendPoints = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepositoryImpl.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getAverageResponseTime)
                            .eq(DailyInspectionReportDO::getReportDate, date));
            if(i ==0){
                historicalReport = dailyReport;
            }
            double responseTime = 0.0;
            if (historicalReport != null && historicalReport.getAverageResponseTime() != null) {
                responseTime = historicalReport.getAverageResponseTime().doubleValue();
            }

            trendPoints.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date)
                    .dateDisplay(date.format(DATE_LOCAL_FORMATTER))
                    .value(responseTime)
                    .unit(MS_UNIT)
                    .build());
        }
        return trendPoints;
    }

    /**
     * 构建异常趋势
     */
    private DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend buildExceptionTrend(LocalDate reportDate, DailyInspectionReportDO dailyReport) {
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> errorTrend = new ArrayList<>();
        List<DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint> warningTrend = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = reportDate.minusDays(i);
            DailyInspectionReportDO historicalReport = dailyInspectionReportRepositoryImpl.getOne(
                    Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                            .select(DailyInspectionReportDO::getFailedCount)
                            .select(DailyInspectionReportDO::getWarningCount)
                            .eq(DailyInspectionReportDO::getReportDate, date));
            if(i ==0){
                historicalReport = dailyReport;
            }
            double errorCount = 0.0;
            double warningCount = 0.0;

            if (historicalReport != null) {
                if (historicalReport.getFailedCount() != null) {
                    errorCount = historicalReport.getFailedCount().doubleValue();
                }
                if (historicalReport.getWarningCount() != null) {
                    warningCount = historicalReport.getWarningCount().doubleValue();
                }
            }

            String dateDisplay = date.format(DATE_LOCAL_FORMATTER);

            errorTrend.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date).dateDisplay(dateDisplay).value(errorCount).unit(PIECE_UNIT).build());
            warningTrend.add(DailyInspectionReportDetailDO.TrendAnalysis.TrendPoint.builder()
                    .date(date).dateDisplay(dateDisplay).value(warningCount).unit(PIECE_UNIT).build());
        }
        return DailyInspectionReportDetailDO.TrendAnalysis.ExceptionTrend.builder()
                .errorTrend(errorTrend)
                .warningTrend(warningTrend)
                .build();
    }
}
