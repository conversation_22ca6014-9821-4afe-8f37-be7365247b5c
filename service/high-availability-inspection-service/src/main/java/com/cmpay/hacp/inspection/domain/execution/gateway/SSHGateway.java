package com.cmpay.hacp.inspection.domain.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.SshConnectionConfig;

public interface SSHGateway {
    ExecutionResult executeScript(SshConnectionConfig connectionConfig,
                                        ScriptExecutionRequest request);

    ExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                         String command);
}
