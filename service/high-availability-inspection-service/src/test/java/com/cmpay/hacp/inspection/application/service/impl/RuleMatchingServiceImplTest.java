package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.ExecutionResultParserService;
import com.cmpay.hacp.inspection.domain.condition.model.RuleCondition;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import com.cmpay.hacp.inspection.infrastructure.condition.converter.RuleConditionConverter;
import com.cmpay.hacp.inspection.infrastructure.condition.repository.RuleConditionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 规则匹配服务测试
 */
@ExtendWith(MockitoExtension.class)
class RuleMatchingServiceImplTest {

    @Mock
    private ExecutionResultParserService executionResultParserService;

    @Mock
    private RuleConditionRepository ruleConditionRepository;
    @Mock
    private RuleConditionConverter ruleConditionConverter;

    @InjectMocks
    private RuleMatchingServiceImpl ruleMatchingService;

    private RuleCondition ruleCondition;
    private PluginOutputField fieldDefinition;

    @BeforeEach
    void setUp() {
        // 设置规则条件：CPU使用率大于80%
        ruleCondition = new RuleCondition();
        // ruleCondition.setRuleId("RULE-000001");
        // ruleCondition.setPluginId("PLUGIN-000001");
        ruleCondition.setPluginOutputFiledName("name");
        ruleCondition.setComparisonOperator(RuleComparisonOperator.GREATER_THAN);
        ruleCondition.setComparisonValue("80");
        // ruleCondition.setSuggest("请优化CPU使用率");

        // 设置字段定义
        fieldDefinition = new PluginOutputField();
        fieldDefinition.setFieldName("cpu.usage");
        fieldDefinition.setFieldType(PluginOutputFieldType.NUMERIC);
        fieldDefinition.setDescription("CPU使用率");
    }

    @Test
    void testEvaluateFieldCondition_GreaterThan_Success() {
        // 测试大于条件 - 成功场景
        Object fieldValue = new BigDecimal("85");

        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);

        assertTrue(result, "85 > 80 应该返回true");
    }

    @Test
    void testEvaluateFieldCondition_GreaterThan_Failure() {
        // 测试大于条件 - 失败场景
        Object fieldValue = new BigDecimal("75");

        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);

        assertFalse(result, "75 > 80 应该返回false");
    }

    @Test
    void testEvaluateFieldCondition_LessThan() {
        // 测试小于条件
        ruleCondition.setComparisonOperator(RuleComparisonOperator.LESS_THAN);
        ruleCondition.setComparisonValue("50");

        Object fieldValue = new BigDecimal("30");

        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);

        assertTrue(result, "30 < 50 应该返回true");
    }

    @Test
    void testEvaluateFieldCondition_Equal() {
        // 测试等于条件
        ruleCondition.setComparisonOperator(RuleComparisonOperator.EQUAL);
        ruleCondition.setComparisonValue("100");

        Object fieldValue = new BigDecimal("100");

        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);

        assertTrue(result, "100 == 100 应该返回true");
    }

    @Test
    void testMatchParsedValues_Success() {
        // 测试解析值匹配 - 成功场景
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("85"));

        RuleMatchingResult.RuleMatchingFieldResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertTrue(result.isSuccess());
        assertEquals("cpu.usage", result.getFieldName());
        assertEquals(new BigDecimal("85"), result.getActualValue());
        assertEquals(new BigDecimal("80"), result.getExpectedValue());
        assertNotNull(result.getMessage());
    }

    @Test
    void testMatchParsedValues_Failure() {
        // 测试解析值匹配 - 失败场景
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("75"));

        RuleMatchingResult.RuleMatchingFieldResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertFalse(result.isSuccess());
        assertEquals("cpu.usage", result.getFieldName());
        assertEquals(new BigDecimal("75"), result.getActualValue());
        assertEquals(new BigDecimal("80"), result.getExpectedValue());
    }

    @Test
    void testMatchParsedValues_FieldNotFound() {
        // 测试字段未找到场景
        Map<String, Object> parsedValues = new HashMap<>();
        // 不包含 cpu.usage 字段

        RuleMatchingResult.RuleMatchingFieldResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("Field value not found"));
    }

    @Test
    void testMatchRule_Success() {
        // 测试完整规则匹配流程 - 成功场景
        String pluginOutput = "{\"cpu\":{\"usage\":85.5}}";

        // Mock repository calls
        when(ruleConditionRepository.getOne(any())).thenReturn(ruleConditionConverter.toRuleConditionDO(ruleCondition));

        // Mock parser service
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("85.5"));
        when(executionResultParserService.parsePluginOutput(anyString(), any())).thenReturn(parsedValues);

        RuleMatchingResult result = ruleMatchingService.matchRule("RULE-000001", "PLUGIN-000001", pluginOutput);

        assertTrue(result.isSuccess());
        assertEquals("RULE-000001", result.getRuleId());
        assertEquals("PLUGIN-000001", result.getPluginId());
    }

    @Test
    void testMatchRule_RuleNotFound() {
        // 测试规则未找到场景
        when(ruleConditionRepository.getOne(any())).thenReturn(null);

        RuleMatchingResult result = ruleMatchingService.matchRule("RULE-000001", "PLUGIN-000001", "{}");

        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("Rule condition not found"));
    }

    @Test
    void testEvaluateFieldCondition_NotEqual() {
        // 测试不等于条件
        ruleCondition.setComparisonOperator(RuleComparisonOperator.NOT_EQUAL);
        ruleCondition.setComparisonValue("100");

        assertTrue(ruleMatchingService.evaluateFieldCondition("200", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("100", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_StringContains() {
        // 测试字符串包含
        ruleCondition.setComparisonOperator(RuleComparisonOperator.STR_CONTAINS);
        ruleCondition.setComparisonValue("error");

        assertTrue(ruleMatchingService.evaluateFieldCondition("This is an error message", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("This is a success message", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_StringNotContains() {
        // 测试字符串不包含
        ruleCondition.setComparisonOperator(RuleComparisonOperator.STR_NOT_CONTAINS);
        ruleCondition.setComparisonValue("error");

        assertTrue(ruleMatchingService.evaluateFieldCondition("This is a success message", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("This is an error message", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_StringStartsWith() {
        // 测试字符串开头匹配
        ruleCondition.setComparisonOperator(RuleComparisonOperator.STR_STARTS_WITH);
        ruleCondition.setComparisonValue("ERROR");

        assertTrue(ruleMatchingService.evaluateFieldCondition("ERROR: Connection failed", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("INFO: Connection successful", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_StringEndsWith() {
        // 测试字符串结尾匹配
        ruleCondition.setComparisonOperator(RuleComparisonOperator.STR_ENDS_WITH);
        ruleCondition.setComparisonValue("failed");

        assertTrue(ruleMatchingService.evaluateFieldCondition("Connection failed", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("Connection successful", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_StringMatches() {
        // 测试正则表达式匹配
        ruleCondition.setComparisonOperator(RuleComparisonOperator.STR_MATCHES);
        ruleCondition.setComparisonValue("\\d{4}-\\d{2}-\\d{2}"); // 日期格式

        assertTrue(ruleMatchingService.evaluateFieldCondition("2024-01-15", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("2024/01/15", ruleCondition));

        // 测试无效正则表达式
        ruleCondition.setComparisonValue("[invalid");
        assertFalse(ruleMatchingService.evaluateFieldCondition("test", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_IsNull() {
        // 测试为空判断
        ruleCondition.setComparisonOperator(RuleComparisonOperator.IS_NULL);

        assertTrue(ruleMatchingService.evaluateFieldCondition(null, ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("not null", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_IsNotNull() {
        // 测试不为空判断
        ruleCondition.setComparisonOperator(RuleComparisonOperator.IS_NOT_NULL);

        assertTrue(ruleMatchingService.evaluateFieldCondition("not null", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition(null, ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_Exists() {
        // 测试存在判断
        ruleCondition.setComparisonOperator(RuleComparisonOperator.EXISTS);

        assertTrue(ruleMatchingService.evaluateFieldCondition("exists", ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition(null, ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_NotExists() {
        // 测试不存在判断
        ruleCondition.setComparisonOperator(RuleComparisonOperator.NOT_EXISTS);

        assertTrue(ruleMatchingService.evaluateFieldCondition(null, ruleCondition));
        assertFalse(ruleMatchingService.evaluateFieldCondition("exists", ruleCondition));
    }

    @Test
    void testEvaluateFieldCondition_EdgeCases() {
        // 测试边界情况

        // 操作符为null
        ruleCondition.setComparisonOperator(null);
        assertFalse(ruleMatchingService.evaluateFieldCondition("test", ruleCondition));

        // 期望值为null（对于需要期望值的操作）
        ruleCondition.setComparisonOperator(RuleComparisonOperator.EQUAL);
        ruleCondition.setComparisonValue(null);
        assertFalse(ruleMatchingService.evaluateFieldCondition("test", ruleCondition));

        // 数值转换失败
        ruleCondition.setComparisonOperator(RuleComparisonOperator.GREATER_THAN);
        ruleCondition.setComparisonValue("not-a-number");
        assertFalse(ruleMatchingService.evaluateFieldCondition("also-not-a-number", ruleCondition));
    }
}
