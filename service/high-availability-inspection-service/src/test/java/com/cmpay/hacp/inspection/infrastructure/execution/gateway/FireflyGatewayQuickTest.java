package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.infrastructure.execution.config.FireflyProperties;
import com.cmpay.hacp.inspection.domain.model.firefly.*;
import com.cmpay.hacp.inspection.infrastructure.execution.cache.FireflyTokenCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FireflyGateway快速测试
 * 用于快速验证真实Firefly接口的连通性
 * <p>
 * 运行方式：
 * gradlew test --tests FireflyGatewayQuickTest -Direfly.test.enabled=true
 */
@EnabledIfSystemProperty(named = "firefly.test.enabled", matches = "true")
class FireflyGatewayQuickTest {

    private static final Logger log = LoggerFactory.getLogger(FireflyGatewayQuickTest.class);

    private FireflyGatewayImpl fireflyGateway;
    private FireflyTokenCache mockTokenCache;

    @BeforeEach
    void setUp() throws Exception {
        log.info("=== FireflyGateway快速测试开始 ===");
        
        // 创建mock的token缓存
        mockTokenCache = mock(FireflyTokenCache.class);
        
        // 创建dev环境配置
        FireflyProperties properties = createDevConfig();
        
        // 创建gateway实例
        fireflyGateway = new FireflyGatewayImpl(mockTokenCache, properties);
        fireflyGateway.afterPropertiesSet();
        
        log.info("测试环境初始化完成");
        log.info("Firefly服务器: {}", properties.getBaseUrl());
        log.info("用户ID: {}", properties.getAuth().getUserId());
    }

    private FireflyProperties createDevConfig() {
        FireflyProperties props = new FireflyProperties();
        props.setBaseUrl("http://10.176.156.3:1980");
        props.setIndicatorUrl("/openability/v1/firefly/busOperation/getIndicatorDataList");
        
        FireflyProperties.Auth auth = new FireflyProperties.Auth();
        auth.setUserId("business_indicators-15002");
        auth.setUserKey("YnxnGt39ZGAFfkUsG50W");
        auth.setTokenUrl("/openability/v1/firefly/getToken");
        props.setAuth(auth);
        
        FireflyProperties.Http http = new FireflyProperties.Http();
        http.setConnectTimeout(15000);  // 增加超时时间
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setEnableSslCheck(false);
        props.setHttp(http);
        
        return props;
    }

    @Test
    void quickTest_GetAuthToken() {
        log.info("--- 测试1: 获取认证令牌 ---");
        
        // Given
        when(mockTokenCache.getValidToken(anyString())).thenReturn(null);
        
        try {
            // When
            FireflyToken token = fireflyGateway.getAuthToken();
            
            // Then
            assertNotNull(token, "Token不应为null");
            assertNotNull(token.getAccessToken(), "AccessToken不应为null");
            assertNotNull(token.getTokenType(), "TokenType不应为null");
            
            log.info("✅ 认证令牌获取成功!");
            log.info("   Token类型: {}", token.getTokenType());
            log.info("   Token长度: {}", token.getAccessToken().length());
            log.info("   过期时间: {}", token.getExpiresAt());
            log.info("   是否有效: {}", !token.isExpired());
            
        } catch (Exception e) {
            log.error("❌ 认证令牌获取失败: {}", e.getMessage(), e);
            fail("认证令牌获取失败: " + e.getMessage());
        }
    }

    @Test
    void quickTest_GetIndicatorData() {
        log.info("--- 测试2: 查询指标数据 ---");
        
        try {
            // 首先获取token
            when(mockTokenCache.getValidToken(anyString())).thenReturn(null);
            FireflyToken token = fireflyGateway.getAuthToken();
            assertNotNull(token, "需要先获取有效token");
            
            // 重置mock，返回获取到的token
            reset(mockTokenCache);
            when(mockTokenCache.getValidToken(anyString())).thenReturn(token);
            
            // 创建查询请求
            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(LocalDateTime.now().minusHours(2))
                    .endTime(LocalDateTime.now().minusHours(1))
                    .indicatorType("host")
                    .indicatorName("cpu_usage_rate")
                    .pageNum(1)
                    .pageSize(5)
                    .build();
            
            log.info("查询参数:");
            log.info("   时间范围: {} 到 {}", request.getStartTime(), request.getEndTime());
            log.info("   指标类型: {}", request.getIndicatorType());
            log.info("   指标名称: {}", request.getIndicatorName());
            log.info("   分页: 第{}页, 每页{}条", request.getPageNum(), request.getPageSize());
            
            // When
            IndicatorQueryResponse response = fireflyGateway.getIndicatorDataList(request);
            
            // Then
            assertNotNull(response, "响应不应为null");
            
            log.info("✅ 指标数据查询完成!");
            log.info("   返回码: {}", response.getMsgCd());
            log.info("   返回信息: {}", response.getMsgInfo());
            log.info("   是否成功: {}", response.isSuccess());
            
            if (response.getDataList() != null) {
                log.info("   数据条数: {}", response.getDataList().size());
                log.info("   总记录数: {}", response.getTotalCount());
                log.info("   当前页码: {}", response.getPageNum());
                log.info("   页大小: {}", response.getPageSize());
                
                // 显示前3条数据
                response.getDataList().stream()
                        .limit(3)
                        .forEach(data -> {
                            log.info("   数据项: 指标={}, 值={}, 时间={}, 区域={}", 
                                    data.getIndicatorName(), 
                                    data.getValue(), 
                                    data.getTimestamp(), 
                                    data.getZone());
                        });
            } else {
                log.info("   返回数据为空");
            }
            
        } catch (Exception e) {
            log.error("❌ 指标数据查询失败: {}", e.getMessage(), e);
            fail("指标数据查询失败: " + e.getMessage());
        }
    }

    @Test
    void quickTest_MultipleIndicators() {
        log.info("--- 测试3: 多种指标查询 ---");
        
        try {
            // 获取token
            when(mockTokenCache.getValidToken(anyString())).thenReturn(null);
            FireflyToken token = fireflyGateway.getAuthToken();
            reset(mockTokenCache);
            when(mockTokenCache.getValidToken(anyString())).thenReturn(token);
            
            // 测试多种指标
            String[][] indicators = {
                {"host", "cpu_usage_rate", "主机CPU使用率"},
                {"host", "memory_usage_rate", "主机内存使用率"},
                {"mid", "jvm_memory_usage", "JVM内存使用"},
                {"container", "container_cpu_usage", "容器CPU使用"}
            };
            
            for (String[] indicator : indicators) {
                String type = indicator[0];
                String name = indicator[1];
                String desc = indicator[2];
                
                log.info("查询 {}: type={}, name={}", desc, type, name);
                
                IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                        .startTime(LocalDateTime.now().minusHours(1))
                        .endTime(LocalDateTime.now())
                        .indicatorType(type)
                        .indicatorName(name)
                        .pageNum(1)
                        .pageSize(3)
                        .build();
                
                IndicatorQueryResponse response = fireflyGateway.getIndicatorDataList(request);
                
                log.info("   {} 结果: 返回码={}, 数据条数={}", 
                        desc, 
                        response.getMsgCd(),
                        response.getDataList() != null ? response.getDataList().size() : 0);
                
                // 避免请求过于频繁
                Thread.sleep(200);
            }
            
            log.info("✅ 多种指标查询测试完成!");
            
        } catch (Exception e) {
            log.error("❌ 多种指标查询测试失败: {}", e.getMessage(), e);
            fail("多种指标查询测试失败: " + e.getMessage());
        }
    }

    @Test
    void quickTest_ParameterValidation() {
        log.info("--- 测试4: 参数验证 ---");
        
        try {
            // 测试无效请求
            IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                    .build(); // 缺少必要参数
            
            IndicatorQueryResponse response = fireflyGateway.getIndicatorDataList(invalidRequest);
            
            assertNotNull(response, "响应不应为null");
            assertEquals("FFM80003", response.getMsgCd(), "应该返回参数验证失败的错误码");
            assertFalse(response.isSuccess(), "无效请求应该失败");
            
            log.info("✅ 参数验证测试通过!");
            log.info("   返回码: {}", response.getMsgCd());
            log.info("   返回信息: {}", response.getMsgInfo());
            
        } catch (Exception e) {
            log.error("❌ 参数验证测试失败: {}", e.getMessage(), e);
            fail("参数验证测试失败: " + e.getMessage());
        }
    }

    @Test
    void quickTest_TokenCaching() {
        log.info("--- 测试5: Token缓存机制 ---");
        
        try {
            // 第一次调用 - 从服务器获取
            when(mockTokenCache.getValidToken(anyString())).thenReturn(null);
            FireflyToken firstToken = fireflyGateway.getAuthToken();
            assertNotNull(firstToken, "第一次获取的token不应为null");
            
            log.info("第一次获取token成功: {}...", 
                    firstToken.getAccessToken().substring(0, Math.min(10, firstToken.getAccessToken().length())));
            
            // 第二次调用 - 从缓存获取
            when(mockTokenCache.getValidToken(anyString())).thenReturn(firstToken);
            FireflyToken secondToken = fireflyGateway.getAuthToken();
            assertNotNull(secondToken, "第二次获取的token不应为null");
            assertEquals(firstToken.getAccessToken(), secondToken.getAccessToken(), "两次获取的token应该相同");
            
            log.info("✅ Token缓存机制测试通过!");
            log.info("   缓存token验证成功");
            
            // 验证缓存调用
            verify(mockTokenCache, atLeast(2)).getValidToken(anyString());
            verify(mockTokenCache, atLeastOnce()).cacheToken(anyString(), any(FireflyToken.class));
            
        } catch (Exception e) {
            log.error("❌ Token缓存机制测试失败: {}", e.getMessage(), e);
            fail("Token缓存机制测试失败: " + e.getMessage());
        }
    }

    void tearDown() throws Exception {
        if (fireflyGateway != null) {
            fireflyGateway.destroy();
            log.info("=== FireflyGateway快速测试结束 ===");
        }
    }
}
