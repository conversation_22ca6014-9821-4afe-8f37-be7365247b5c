package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.infrastructure.execution.config.FireflyProperties;
import com.cmpay.hacp.inspection.domain.model.firefly.*;
import com.cmpay.hacp.inspection.infrastructure.execution.cache.FireflyTokenCache;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * FireflyGateway Spring集成测试
 * 使用Spring Boot测试框架和真实的Firefly HTTP接口
 */
@SpringBootTest(classes = {
    FireflyGatewayImpl.class,
    FireflyProperties.class
})
@ActiveProfiles("integration-test")
@TestPropertySource(locations = "classpath:application-integration-test.yml")
class FireflyGatewaySpringIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(FireflyGatewaySpringIntegrationTest.class);

    @Autowired
    private FireflyGatewayImpl fireflyGateway;

    @Autowired
    private FireflyProperties fireflyProperties;

    @MockBean
    private FireflyTokenCache fireflyTokenCache;

    @Test
    void testSpringConfiguration() {
        // 验证Spring配置是否正确加载
        assertNotNull(fireflyGateway, "FireflyGateway应该被正确注入");
        assertNotNull(fireflyProperties, "FireflyProperties应该被正确注入");
        
        log.info("Spring配置验证通过:");
        log.info("Base URL: {}", fireflyProperties.getBaseUrl());
        log.info("User ID: {}", fireflyProperties.getAuth().getUserId());
        log.info("Indicator URL: {}", fireflyProperties.getIndicatorUrl());
        
        assertEquals("http://10.176.156.3:1980", fireflyProperties.getBaseUrl());
        assertEquals("business_indicators-15002", fireflyProperties.getAuth().getUserId());
        assertEquals("YnxnGt39ZGAFfkUsG50W", fireflyProperties.getAuth().getUserKey());
    }

    @Test
    void testGetAuthToken_WithSpringContext() {
        // Given
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(null);

        // When
        log.info("开始Spring上下文中的认证令牌测试...");
        FireflyToken result = fireflyGateway.getAuthToken();

        // Then
        assertNotNull(result, "获取的token不应为null");
        assertNotNull(result.getAccessToken(), "accessToken不应为null");
        assertNotNull(result.getTokenType(), "tokenType不应为null");
        assertNotNull(result.getExpiresAt(), "expiresAt不应为null");
        
        log.info("Spring上下文认证令牌测试成功:");
        log.info("Token Type: {}", result.getTokenType());
        log.info("Token Length: {}", result.getAccessToken().length());
        log.info("Expires At: {}", result.getExpiresAt());
        
        // 验证token有效性
        assertFalse(result.isExpired(), "新获取的token不应该已过期");
        assertTrue(result.getExpiresAt().isAfter(LocalDateTime.now()), "过期时间应该在未来");
        
        // 验证缓存交互
        verify(fireflyTokenCache).getValidToken(fireflyProperties.getAuth().getUserId());
        verify(fireflyTokenCache).cacheToken(eq(fireflyProperties.getAuth().getUserId()), any(FireflyToken.class));
    }

    @Test
    void testGetIndicatorDataList_WithSpringContext() {
        // Given
        // 首先获取真实的token
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(null);
        FireflyToken token = fireflyGateway.getAuthToken();
        assertNotNull(token, "需要先获取有效的token");
        
        // 重置mock，让后续调用返回真实token
        reset(fireflyTokenCache);
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(token);
        
        // 创建查询请求
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(2))
                .endTime(LocalDateTime.now().minusHours(1))
                .indicatorType("host")
                .indicatorName("cpu_usage_rate")
                .pageNum(1)
                .pageSize(5)
                .zones(Arrays.asList("zone1", "zone2"))
                .specialNameOne("test-special-1")
                .specialNameTwo("test-special-2")
                .build();

        // When
        log.info("开始Spring上下文中的指标数据查询测试...");
        log.info("查询时间范围: {} 到 {}", request.getStartTime(), request.getEndTime());
        log.info("查询参数: type={}, name={}, zones={}", 
                request.getIndicatorType(), request.getIndicatorName(), request.getZones());
        
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result, "查询结果不应为null");
        assertNotNull(result.getMsgCd(), "返回码不应为null");
        assertNotNull(result.getMsgInfo(), "返回信息不应为null");
        
        log.info("Spring上下文指标数据查询结果:");
        log.info("返回码: {}", result.getMsgCd());
        log.info("返回信息: {}", result.getMsgInfo());
        log.info("是否成功: {}", result.isSuccess());
        
        if (result.getDataList() != null) {
            log.info("返回数据条数: {}", result.getDataList().size());
            log.info("总记录数: {}", result.getTotalCount());
            
            // 打印前几条数据的详细信息
            result.getDataList().stream()
                    .limit(3)
                    .forEach(data -> {
                        log.info("数据项: 指标={}, 值={}, 时间={}, 区域={}", 
                                data.getIndicatorName(), data.getValue(), 
                                data.getTimestamp(), data.getZone());
                    });
        }
        
        // 验证基本结构
        assertTrue(result.getMsgCd() != null && !result.getMsgCd().isEmpty(), "返回码不应为空");
        
        // 如果查询成功，验证分页信息
        if (result.isSuccess()) {
            assertEquals(Integer.valueOf(1), result.getPageNum(), "页码应为1");
            assertEquals(Integer.valueOf(5), result.getPageSize(), "页大小应为5");
        }
    }

    @Test
    void testMultipleIndicatorQueries() {
        // Given
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(null);
        FireflyToken token = fireflyGateway.getAuthToken();
        assertNotNull(token, "需要先获取有效的token");
        
        reset(fireflyTokenCache);
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(token);
        
        // 定义多个查询场景
        Object[][] testCases = {
            {"host", "cpu_usage_rate", "CPU使用率"},
            {"host", "memory_usage_rate", "内存使用率"},
            {"host", "disk_usage_rate", "磁盘使用率"},
            {"mid", "jvm_memory_usage", "JVM内存使用"},
            {"container", "container_cpu_usage", "容器CPU使用"}
        };

        // When & Then
        log.info("开始多指标查询测试...");
        
        for (Object[] testCase : testCases) {
            String indicatorType = (String) testCase[0];
            String indicatorName = (String) testCase[1];
            String description = (String) testCase[2];
            
            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(LocalDateTime.now().minusHours(1))
                    .endTime(LocalDateTime.now())
                    .indicatorType(indicatorType)
                    .indicatorName(indicatorName)
                    .pageNum(1)
                    .pageSize(3)
                    .build();
            
            log.info("查询 {} ({}): type={}, name={}", description, indicatorType, indicatorType, indicatorName);
            
            IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(request);
            
            assertNotNull(result, description + " 查询结果不应为null");
            assertNotNull(result.getMsgCd(), description + " 返回码不应为null");
            
            log.info("{} 查询结果: 返回码={}, 信息={}, 数据条数={}", 
                    description, result.getMsgCd(), result.getMsgInfo(),
                    result.getDataList() != null ? result.getDataList().size() : 0);
            
            // 短暂延迟，避免请求过于频繁
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    @Test
    void testTokenExpiration_Simulation() {
        // Given
        // 模拟token过期的场景
        FireflyToken expiredToken = FireflyToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().minusHours(1)) // 已过期
                .createdAt(LocalDateTime.now().minusHours(2))
                .build();
        
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(expiredToken);
        
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage_rate")
                .pageNum(1)
                .pageSize(5)
                .build();

        // When
        log.info("开始token过期模拟测试...");
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result, "即使token过期，也应该返回结果");
        log.info("Token过期测试结果: 返回码={}, 信息={}", result.getMsgCd(), result.getMsgInfo());
        
        // 如果返回401错误，应该会清除缓存
        if ("HAI50004".equals(result.getMsgCd()) || result.getMsgInfo().contains("过期")) {
            verify(fireflyTokenCache).evictToken(fireflyProperties.getAuth().getUserId());
            log.info("Token过期处理正确，已清除缓存");
        }
    }

    @Test
    void testConcurrentRequests() throws InterruptedException {
        // Given
        when(fireflyTokenCache.getValidToken(anyString())).thenReturn(null);
        
        int threadCount = 3;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];
        
        // When
        log.info("开始并发请求测试，线程数: {}", threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    FireflyToken token = fireflyGateway.getAuthToken();
                    results[index] = (token != null && token.getAccessToken() != null);
                    log.info("线程 {} 获取token结果: {}", index, results[index]);
                } catch (Exception e) {
                    log.error("线程 {} 执行异常", index, e);
                    results[index] = false;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000); // 最多等待5秒
        }
        
        // Then
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 应该成功获取token");
        }
        
        log.info("并发请求测试完成，所有线程都成功获取了token");
        
        // 验证缓存操作
        verify(fireflyTokenCache, atLeast(threadCount)).getValidToken(fireflyProperties.getAuth().getUserId());
    }
}
