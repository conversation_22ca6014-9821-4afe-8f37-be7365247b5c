package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.SSHGateway;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.SshConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SSH执行器单元测试
 */
@ExtendWith(MockitoExtension.class)
class SSHExecutorTest {

    @Mock
    private SSHGateway sshGateway;

    @Mock
    private PluginScriptRepository pluginScriptRepository;

    @Mock
    private RulePluginParamRepository rulePluginParamRepository;

    @Mock
    private RuleMatchingService ruleMatchingService;

    @InjectMocks
    private SSHExecutor sshExecutor;

    private RuleExecution ruleExecution;
    private PluginScript pluginScript;
    private ExecutionResult successResult;
    private ExecutionResult failureResult;
    private RuleMatchingResult matchingResult;
    private List<RulePluginParam> rulePluginParams;

    @BeforeEach
    void setUp() {
        // 设置规则执行信息
        ruleExecution = new RuleExecution();
        ruleExecution.setRuleId("RULE-CPU-CHECK");
        ruleExecution.setPluginId("PLUGIN-SYSTEM-INFO");
        ruleExecution.setPluginType(PluginType.SHELL_SCRIPT);

        TargetHost targetHost = TargetHost.builder()
                .host("*************")
                .port(22)
                .username("root")
                .password("password123")
                .authType(TargetHost.AuthType.PASSWORD)
                .build();

        ruleExecution.setResourceType(ResourceType.VIRTUAL_MACHINE);
        ruleExecution.setTargetHost(targetHost);

        // 设置插件脚本
        pluginScript = new PluginScript();
        pluginScript.setPluginId("PLUGIN-SYSTEM-INFO");
        pluginScript.setScriptContent("#!/bin/bash\necho '{\"cpu_usage\": ${cpu_threshold}}'");

        // 设置规则插件参数
        RulePluginParam param1 = new RulePluginParam();
        param1.setPluginParamName("cpu_threshold");
        param1.setPluginParamValue("80.0");
        rulePluginParams = Arrays.asList(param1);

        // 设置成功的脚本执行结果
        successResult = ExecutionResult.builder()
                .success(true)
                .exitCode(0)
                .stdout("{\"cpu_usage\": 75.5}")
                .stderr("")
                .executionTime(1500L)
                .build();

        // 设置失败的脚本执行结果
        failureResult = ExecutionResult.builder()
                .success(false)
                .exitCode(1)
                .stdout("")
                .stderr("Connection timeout")
                .executionTime(30000L)
                .build();

        // 设置规则匹配结果
        matchingResult = RuleMatchingResult.builder()
                .success(true)
                .ruleId("RULE-CPU-CHECK")
                .pluginId("PLUGIN-SYSTEM-INFO")
                .suggest("继续监控")
                .build();
    }

    @Test
    void testExecute_Success() {
        // 准备测试数据
        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenReturn(matchingResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.isScriptExecutionSuccess());
        assertTrue(result.isRuleMatchingSuccess());
        assertEquals("巡检通过", result.getMessage());
        assertNotNull(result.getDetails());
        assertTrue(result.getDetails().contains("脚本执行结果"));
        assertTrue(result.getDetails().contains("规则匹配结果"));

        // 验证调用
        verify(pluginScriptRepository).getByPluginId("PLUGIN-SYSTEM-INFO");
        verify(rulePluginParamRepository).findByRuleId("RULE-CPU-CHECK");
        verify(sshGateway).executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class));
        verify(ruleMatchingService).matchRule("RULE-CPU-CHECK", "PLUGIN-SYSTEM-INFO", "{\"cpu_usage\": 75.5}");
    }

    @Test
    void testExecute_PluginScriptNotFound() {
        // 准备测试数据 - 插件脚本不存在
        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(null);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertFalse(result.isScriptExecutionSuccess());
        assertFalse(result.isRuleMatchingSuccess());
        assertEquals("Plugin script not found", result.getMessage());
        assertTrue(result.getDetails().contains("Plugin script not found for pluginId: PLUGIN-SYSTEM-INFO"));

        // 验证调用
        verify(pluginScriptRepository).getByPluginId("PLUGIN-SYSTEM-INFO");
        verify(rulePluginParamRepository, never()).findByRuleId(anyString());
        verify(sshGateway, never()).executeScript(any(), any());
        verify(ruleMatchingService, never()).matchRule(anyString(), anyString(), anyString());
    }

    @Test
    void testExecute_ScriptExecutionFailure() {
        // 准备测试数据 - 脚本执行失败
        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(failureResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertFalse(result.isScriptExecutionSuccess());
        assertFalse(result.isRuleMatchingSuccess());
        assertEquals("脚本执行失败", result.getMessage());
        assertTrue(result.getDetails().contains("执行状态: 失败"));
        assertTrue(result.getDetails().contains("退出码: 1"));

        // 验证调用
        verify(pluginScriptRepository).getByPluginId("PLUGIN-SYSTEM-INFO");
        verify(rulePluginParamRepository).findByRuleId("RULE-CPU-CHECK");
        verify(sshGateway).executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class));
        verify(ruleMatchingService, never()).matchRule(anyString(), anyString(), anyString());
    }

    @Test
    void testExecute_RuleMatchingFailure() {
        // 准备测试数据 - 规则匹配失败
        RuleMatchingResult failedMatchingResult = RuleMatchingResult.builder()
                .success(false)
                .ruleId("RULE-CPU-CHECK")
                .pluginId("PLUGIN-SYSTEM-INFO")
                .errorMessage("阈值超限")
                .build();

        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenReturn(failedMatchingResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.isScriptExecutionSuccess());
        assertFalse(result.isRuleMatchingSuccess());
        assertEquals("规则匹配失败: CPU使用率过高", result.getMessage());
        assertTrue(result.getDetails().contains("匹配状态: 失败"));
        assertTrue(result.getDetails().contains("错误信息: 阈值超限"));
    }

    @Test
    void testExecute_RuleMatchingException() {
        // 准备测试数据 - 规则匹配抛出异常
        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenThrow(new RuntimeException("Rule matching service error"));

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.isScriptExecutionSuccess());
        assertFalse(result.isRuleMatchingSuccess());
        assertEquals("规则匹配失败", result.getMessage());
        assertNotNull(result.getRuleMatchingResult());
        assertFalse(result.getRuleMatchingResult().isSuccess());
        assertTrue(result.getRuleMatchingResult().getErrorMessage().contains("Rule matching error"));
    }

    @Test
    void testExecute_WithParameterSubstitution() {
        // 测试参数替换功能
        pluginScript.setScriptContent("#!/bin/bash\necho '{\"cpu_usage\": ${cpu_threshold}, \"memory_usage\": ${memory_threshold}}'");

        RulePluginParam param2 = new RulePluginParam();
        param2.setPluginParamName("memory_threshold");
        param2.setPluginParamValue("70.0");
        rulePluginParams = Arrays.asList(rulePluginParams.get(0), param2);

        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenReturn(matchingResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证脚本内容中的参数被正确替换
        verify(sshGateway).executeScript(any(SshConnectionConfig.class), argThat(request ->
                request.getScriptContent().contains("80.0") &&
                        request.getScriptContent().contains("70.0")
        ));
    }

    @Test
    void testExecute_PythonScript() {
        // 测试Python脚本执行
        ruleExecution.setPluginType(PluginType.PYTHON_SCRIPT);
        pluginScript.setScriptContent("#!/usr/bin/env python3\nimport json\nprint(json.dumps({'cpu_usage': 85.5}))");

        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(Collections.emptyList());
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenReturn(matchingResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证脚本类型为Python
        verify(sshGateway).executeScript(any(SshConnectionConfig.class), argThat(request ->
                request.getScriptType() == ScriptExecutionRequest.ScriptType.PYTHON
        ));
    }

    @Test
    void testExecute_EmptyStdout() {
        // 测试标准输出为空的情况
        ExecutionResult emptyOutputResult = ExecutionResult.builder()
                .success(true)
                .exitCode(0)
                .stdout("")
                .stderr("")
                .executionTime(1000L)
                .build();

        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(emptyOutputResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.isScriptExecutionSuccess());
        assertFalse(result.isRuleMatchingSuccess());

        // 验证不会调用规则匹配服务
        verify(ruleMatchingService, never()).matchRule(anyString(), anyString(), anyString());
    }

    @Test
    void testSupports_ShellScript() {
        // 测试支持Shell脚本
        ExecutionContext context = new ExecutionContext();
        context.setPluginType(PluginType.SHELL_SCRIPT);
        context.setResourceType(ResourceType.VIRTUAL_MACHINE);

        boolean supports = sshExecutor.supports(context);

        assertTrue(supports);
    }

    @Test
    void testSupports_PythonScript() {
        // 测试支持Python脚本
        ExecutionContext context = new ExecutionContext();
        context.setPluginType(PluginType.PYTHON_SCRIPT);
        context.setResourceType(ResourceType.VIRTUAL_MACHINE);

        boolean supports = sshExecutor.supports(context);

        assertTrue(supports);
    }

    @Test
    void testSupports_UnsupportedPluginType() {
        // 测试不支持的插件类型
        ExecutionContext context = new ExecutionContext();
        context.setPluginType(PluginType.K8S_RESOURCE_HEALTH_CHECK);
        context.setResourceType(ResourceType.VIRTUAL_MACHINE);

        boolean supports = sshExecutor.supports(context);

        assertFalse(supports);
    }

    @Test
    void testSupports_UnsupportedResourceType() {
        // 测试不支持的资源类型
        ExecutionContext context = new ExecutionContext();
        context.setPluginType(PluginType.SHELL_SCRIPT);
        context.setResourceType(ResourceType.ENVIRONMENT_INDEPENDENT);

        boolean supports = sshExecutor.supports(context);

        assertFalse(supports);
    }

    @Test
    void testBuildResultDetails_WithAllInfo() {
        // 测试构建完整的结果详情
        when(pluginScriptRepository.getByPluginId("PLUGIN-SYSTEM-INFO")).thenReturn(pluginScript);
        when(rulePluginParamRepository.findByRuleId("RULE-CPU-CHECK")).thenReturn(rulePluginParams);
        when(sshGateway.executeScript(any(SshConnectionConfig.class), any(ScriptExecutionRequest.class)))
                .thenReturn(successResult);
        when(ruleMatchingService.matchRule(eq("RULE-CPU-CHECK"), eq("PLUGIN-SYSTEM-INFO"), anyString()))
                .thenReturn(matchingResult);

        // 执行测试
        InspectionResult result = sshExecutor.execute(ruleExecution);

        // 验证详情包含所有必要信息
        String details = result.getDetails();
        assertTrue(details.contains("=== 脚本执行结果 ==="));
        assertTrue(details.contains("执行状态: 成功"));
        assertTrue(details.contains("退出码: 0"));
        assertTrue(details.contains("执行时间: 1500ms"));
        assertTrue(details.contains("标准输出:"));
        assertTrue(details.contains("=== 规则匹配结果 ==="));
        assertTrue(details.contains("匹配状态: 通过"));
        assertTrue(details.contains("匹配信息: CPU使用率正常"));
        assertTrue(details.contains("治理建议: 继续监控"));
    }
}