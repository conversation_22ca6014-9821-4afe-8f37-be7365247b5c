package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.infrastructure.execution.config.FireflyProperties;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.model.firefly.FireflyToken;
import com.cmpay.hacp.inspection.infrastructure.execution.cache.FireflyTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * FireflyGatewayImpl单元测试
 * 使用MockWebServer模拟HTTP服务器
 */
@ExtendWith(MockitoExtension.class)
class FireflyGatewayImplTest {

    @Mock
    private FireflyTokenCache fireflyTokenCache;

    private FireflyGatewayImpl fireflyGateway;
    private MockWebServer mockWebServer;
    private FireflyProperties properties;

    @BeforeEach
    void setUp() throws Exception {
        // 启动MockWebServer
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        // 创建真实的FireflyProperties对象
        properties = new FireflyProperties();
        properties.setBaseUrl(mockWebServer.url("/").toString().replaceAll("/$", ""));
        properties.setIndicatorUrl("/api/indicators");

        FireflyProperties.Auth auth = new FireflyProperties.Auth();
        auth.setUserId("testUser");
        auth.setUserKey("testKey");
        auth.setTokenUrl("/api/token");
        properties.setAuth(auth);

        FireflyProperties.Http http = new FireflyProperties.Http();
        http.setConnectTimeout(10000);
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setMaxIdleConnections(10);
        http.setKeepAliveDuration(5);
        http.setEnableSslCheck(false);
        properties.setHttp(http);

        // 创建FireflyGatewayImpl实例
        fireflyGateway = new FireflyGatewayImpl(fireflyTokenCache, properties);

        // 初始化组件
        fireflyGateway.afterPropertiesSet();
    }

    @AfterEach
    void tearDown() throws IOException {
        if (mockWebServer != null) {
            mockWebServer.shutdown();
        }
        if (fireflyGateway != null) {
            fireflyGateway.destroy();
        }
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        FireflyToken cachedToken = FireflyToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        FireflyToken result = fireflyGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(fireflyTokenCache).getValidToken("testUser");
        // 验证没有发起HTTP请求
        assertEquals(0, mockWebServer.getRequestCount());
    }

    @Test
    void testGetAuthToken_FetchNew_Success() throws Exception {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        // Mock HTTP response
        String tokenResponseJson = "{\n" +
                "    \"token\": \"new-access-token\",\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 3600,\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(tokenResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        FireflyToken result = fireflyGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("new-access-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(fireflyTokenCache, times(2)).getValidToken("testUser");
        verify(fireflyTokenCache).cacheToken(eq("testUser"), any(FireflyToken.class));

        // 验证请求
        RecordedRequest request = mockWebServer.takeRequest();
        assertEquals("POST", request.getMethod());
        assertTrue(request.getPath().contains("/api/token"));
    }

    @Test
    void testGetAuthToken_HttpRequestFailed() throws Exception {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.FIREFLY_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
        verify(fireflyTokenCache, times(2)).getValidToken("testUser");
        verifyNoMoreInteractions(fireflyTokenCache);
    }

    @Test
    void testGetAuthToken_AuthenticationFailed() throws Exception {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80001\",\n" +
                "    \"msgInfo\": \"Authentication failed\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.FIREFLY_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_UserStatusError() throws Exception {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80002\",\n" +
                "    \"msgInfo\": \"User status error\"\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.FIREFLY_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_InvalidJsonResponse() throws Exception {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json")
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_ConcurrentAccess() throws InterruptedException {
        // Given
        when(fireflyTokenCache.getValidToken("testUser"))
                .thenReturn(null)  // 第一次调用返回null
                .thenReturn(FireflyToken.builder()  // 后续调用返回缓存的token
                        .accessToken("concurrent-token")
                        .tokenType("Bearer")
                        .expiresAt(LocalDateTime.now().plusHours(1))
                        .build());

        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // When
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    FireflyToken token = fireflyGateway.getAuthToken();
                    assertNotNull(token);
                } finally {
                    latch.countDown();
                }
            });
        }

        // Then
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        executor.shutdown();

        // 验证只有一个线程真正获取了新token，其他线程从缓存获取
        verify(fireflyTokenCache, atLeast(threadCount)).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_Success() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        FireflyToken token = FireflyToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [],\n" +
                "    \"total\": 0,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());

        // 验证请求
        RecordedRequest recordedRequest = mockWebServer.takeRequest();
        assertEquals("POST", recordedRequest.getMethod());
        assertTrue(Objects.requireNonNull(recordedRequest.getPath()).contains("/api/indicators"));
        assertTrue(Objects.requireNonNull(recordedRequest.getHeader("Authorization")).contains("Bearer valid-token"));
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
        // 验证没有发起HTTP请求
        assertEquals(0, mockWebServer.getRequestCount());
    }

    @Test
    void testGetIndicatorDataList_TokenExpired() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        FireflyToken token = FireflyToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(401)
                .setBody("Unauthorized"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.FIREFLY_TOKEN_EXPIRED.getMsgCd(), exception.getMsgCd());
        verify(fireflyTokenCache).evictToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_QueryError() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        FireflyToken token = FireflyToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.FIREFLY_QUERY_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetIndicatorDataList_WithCompleteRequest() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .zones(Arrays.asList("zone1", "zone2"))
                .specialNameOne("special1")
                .specialNameTwo("special2")
                .build();

        FireflyToken token = FireflyToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"indicatorName\": \"cpu_usage\",\n" +
                "            \"timestamp\": \"2024-01-01T10:00:00\",\n" +
                "            \"value\": \"75.5\",\n" +
                "            \"zone\": \"zone1\",\n" +
                "            \"one\": \"special1\",\n" +
                "            \"two\": \"special2\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"total\": 1,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());
        assertNotNull(result.getDataList());
        assertEquals(1, result.getDataList().size());
        assertEquals(Long.valueOf(1), result.getTotalCount());
        assertEquals(Integer.valueOf(1), result.getPageNum());
        assertEquals(Integer.valueOf(100), result.getPageSize());
    }

    @Test
    void testGetIndicatorDataList_InvalidJsonResponse() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        FireflyToken token = FireflyToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(token);

        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json response")
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fireflyGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.FIREFLY_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    /**
     * 辅助方法：创建有效的FireflyToken
     */
    private FireflyToken createValidToken() {
        return FireflyToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 辅助方法：创建过期的FireflyToken
     */
    private FireflyToken createExpiredToken() {
        return FireflyToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().minusHours(1))
                .createdAt(LocalDateTime.now().minusHours(2))
                .build();
    }

    /**
     * 辅助方法：创建基本的IndicatorQueryRequest
     */
    private IndicatorQueryRequest createBasicRequest() {
        return IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();
    }
}
