package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.infrastructure.execution.config.FireflyProperties;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.model.firefly.FireflyToken;
import com.cmpay.hacp.inspection.domain.model.firefly.FireflyTokenResponse;
import com.cmpay.hacp.inspection.infrastructure.execution.cache.FireflyTokenCache;
import lombok.var;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FireflyGatewayImpl简化单元测试
 * 主要测试缓存逻辑和参数验证
 */
@ExtendWith(MockitoExtension.class)
class FireflyGatewaySimpleTest {

    @Mock
    private FireflyTokenCache fireflyTokenCache;

    @Mock
    private FireflyProperties properties;

    @Mock
    private FireflyProperties.Auth authProperties;

    @Mock
    private FireflyProperties.Http httpProperties;

    private FireflyGatewayImpl fireflyGateway;

    @BeforeEach
    void setUp() {
        // 设置默认的properties mock行为
        when(properties.getAuth()).thenReturn(authProperties);
        when(properties.getHttp()).thenReturn(httpProperties);
        when(authProperties.getUserId()).thenReturn("testUser");
        when(authProperties.getUserKey()).thenReturn("testKey");
        when(authProperties.getTokenUrl()).thenReturn("/api/token");
        when(properties.getBaseUrl()).thenReturn("http://localhost:9090");
        when(properties.getIndicatorUrl()).thenReturn("/api/indicators");

        // 设置HTTP配置默认值
        when(httpProperties.getConnectTimeout()).thenReturn(10000);
        when(httpProperties.getReadTimeout()).thenReturn(30000);
        when(httpProperties.getWriteTimeout()).thenReturn(30000);
        when(httpProperties.getMaxIdleConnections()).thenReturn(10);
        when(httpProperties.getKeepAliveDuration()).thenReturn(5);
        when(httpProperties.isEnableSslCheck()).thenReturn(false);

        fireflyGateway = new FireflyGatewayImpl(fireflyTokenCache, properties);
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        FireflyToken cachedToken = FireflyToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        FireflyToken result = fireflyGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(fireflyTokenCache).getValidToken("testUser");
    }

    @Test
    void testGetAuthToken_CacheReturnsNull() {
        // Given
        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        // When & Then
        // 由于没有初始化HTTP客户端，这里会抛出异常，这是预期的
        assertThrows(Exception.class, () -> fireflyGateway.getAuthToken());

        verify(fireflyTokenCache).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        IndicatorQueryResponse result = fireflyGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
    }

    @Test
    void testGetIndicatorDataList_ValidRequest_NoToken() {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        when(fireflyTokenCache.getValidToken("testUser")).thenReturn(null);

        // When & Then
        // 由于没有初始化HTTP客户端，这里会抛出异常，这是预期的
        assertThrows(Exception.class, () -> fireflyGateway.getIndicatorDataList(request));
    }

    @Test
    void testFireflyToken_Methods() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        FireflyToken token = FireflyToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(now.plusHours(1))
                .createdAt(now)
                .build();

        // When & Then
        assertFalse(token.isExpired());
        assertFalse(token.isExpiringSoon());
        assertEquals("Bearer test-token", token.getAuthorizationHeader());

        // Test expired token
        FireflyToken expiredToken = FireflyToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(now.minusHours(1))
                .createdAt(now.minusHours(2))
                .build();

        assertTrue(expiredToken.isExpired());
    }

    @Test
    void testFireflyToken_ExpiringSoon() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        FireflyToken token = FireflyToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(now.plusMinutes(3)) // 3分钟后过期，小于5分钟阈值
                .createdAt(now)
                .build();

        // When & Then
        assertTrue(token.isExpiringSoon());
        assertFalse(token.isExpired());
    }

    @Test
    void testFireflyToken_DefaultTokenType() {
        // Given
        FireflyToken token = FireflyToken.builder()
                .accessToken("test-token")
                .tokenType(null) // 没有设置tokenType
                .build();

        // When & Then
        assertEquals("Bearer test-token", token.getAuthorizationHeader());
    }

    @Test
    void testIndicatorQueryRequest_SpecialFields() {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .specialNameOne("value1")
                .specialNameTwo("value2")
                .specialNameThree("value3")
                .build();

        // When
        var specialFields = request.getSpecialFields();

        // Then
        assertEquals(3, specialFields.size());
        assertEquals("value1", specialFields.get("specialNameOne"));
        assertEquals("value2", specialFields.get("specialNameTwo"));
        assertEquals("value3", specialFields.get("specialNameThree"));
    }

    @Test
    void testFireflyTokenResponse_Methods() {
        // Given
        FireflyTokenResponse response = new FireflyTokenResponse();

        // Test success
        response.setMsgCd("FFM00000");
        assertTrue(response.isSuccess());
        assertFalse(response.isAuthError());
        assertFalse(response.isUserStatusError());

        // Test auth error
        response.setMsgCd("FFM80001");
        assertFalse(response.isSuccess());
        assertTrue(response.isAuthError());
        assertFalse(response.isUserStatusError());

        // Test user status error
        response.setMsgCd("FFM80002");
        assertFalse(response.isSuccess());
        assertFalse(response.isAuthError());
        assertTrue(response.isUserStatusError());
    }

    @Test
    void testIndicatorQueryResponse_Methods() {
        // Given
        IndicatorQueryResponse response = IndicatorQueryResponse.builder()
                .msgCd("FFM00000")
                .msgInfo("Success")
                .build();

        // When & Then
        assertTrue(response.isSuccess());

        // Test failure
        response.setMsgCd("FFM80003");
        assertFalse(response.isSuccess());
    }

    @Test
    void testErrorCodeEnum_FireflyErrors() {
        // Test all Firefly related error codes
        assertEquals("HAI50001", ErrorCodeEnum.FIREFLY_CONNECTION_ERROR.getMsgCd());
        assertEquals("Firefly连接失败", ErrorCodeEnum.FIREFLY_CONNECTION_ERROR.getMsgInfo());

        assertEquals("HAI50002", ErrorCodeEnum.FIREFLY_AUTH_ERROR.getMsgCd());
        assertEquals("Firefly认证失败", ErrorCodeEnum.FIREFLY_AUTH_ERROR.getMsgInfo());

        assertEquals("HAI50003", ErrorCodeEnum.FIREFLY_QUERY_ERROR.getMsgCd());
        assertEquals("Firefly查询失败", ErrorCodeEnum.FIREFLY_QUERY_ERROR.getMsgInfo());

        assertEquals("HAI50004", ErrorCodeEnum.FIREFLY_TOKEN_EXPIRED.getMsgCd());
        assertEquals("Firefly认证令牌已过期", ErrorCodeEnum.FIREFLY_TOKEN_EXPIRED.getMsgInfo());

        assertEquals("HAI50005", ErrorCodeEnum.FIREFLY_INVALID_RESPONSE.getMsgCd());
        assertEquals("Firefly响应格式无效", ErrorCodeEnum.FIREFLY_INVALID_RESPONSE.getMsgInfo());
    }

    /**
     * 辅助方法：创建有效的FireflyToken
     */
    private FireflyToken createValidToken() {
        return FireflyToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 辅助方法：创建基本的IndicatorQueryRequest
     */
    private IndicatorQueryRequest createBasicRequest() {
        return IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();
    }
}
