package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.InspectionTaskExecutionService;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.domain.model.enums.CommonStatusEnum;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.task.model.Task;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRepository;
import com.cmpay.lemon.common.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SSH虚拟机巡检任务服务测试
 * 主要测试从executeTaskNow开始的SSH虚拟机巡检流程
 */
@ExtendWith(MockitoExtension.class)
class InspectionTaskServiceImplSshTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private InspectionTaskExecutionService taskExecutionService;

    @InjectMocks
    private InspectionTaskServiceImpl inspectionTaskService;

    private Task validTask;
    private String validTaskId;

    @BeforeEach
    void setUp() {
        validTaskId = "TASK-SSH-VM-001";
        
        validTask = new Task();
        validTask.setTaskId(validTaskId);
        validTask.setName("SSH虚拟机巡检任务");
        validTask.setDescription("通过SSH连接虚拟机进行系统巡检");
        validTask.setStatus(CommonStatusEnum.ENABLE);
    }

    @Test
    void testExecuteTaskNow_Success() {
        // 准备测试数据 - 任务存在的情况
        when(taskRepository.getByTaskId(validTaskId)).thenReturn(validTask);

        // 执行测试
        assertDoesNotThrow(() -> {
            inspectionTaskService.executeTaskNow(validTaskId);
        });

        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(validTaskId);
        verify(taskExecutionService, times(1)).executeTask(validTaskId, TriggerMode.MANUAL);
    }

    @Test
    void testExecuteTaskNow_TaskNotFound() {
        // 准备测试数据 - 任务不存在的情况
        String nonExistentTaskId = "TASK-NOT-EXIST";
        when(taskRepository.getByTaskId(nonExistentTaskId)).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inspectionTaskService.executeTaskNow(nonExistentTaskId);
        });

        // 验证异常信息
        assertEquals(ErrorCodeEnum.TASK_NAME_NOT_EXIST.getMsgCd(), exception.getMsgCd());
        
        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(nonExistentTaskId);
        verify(taskExecutionService, never()).executeTask(anyString(), any(TriggerMode.class));
    }

    @Test
    void testExecuteTaskNow_NullTaskId() {
        // 测试空任务ID的情况
        String nullTaskId = null;
        when(taskRepository.getByTaskId(nullTaskId)).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inspectionTaskService.executeTaskNow(nullTaskId);
        });

        // 验证异常信息
        assertEquals(ErrorCodeEnum.TASK_NAME_NOT_EXIST.getMsgCd(), exception.getMsgCd());
        
        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(nullTaskId);
        verify(taskExecutionService, never()).executeTask(anyString(), any(TriggerMode.class));
    }

    @Test
    void testExecuteTaskNow_EmptyTaskId() {
        // 测试空字符串任务ID的情况
        String emptyTaskId = "";
        when(taskRepository.getByTaskId(emptyTaskId)).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            inspectionTaskService.executeTaskNow(emptyTaskId);
        });

        // 验证异常信息
        assertEquals(ErrorCodeEnum.TASK_NAME_NOT_EXIST.getMsgCd(), exception.getMsgCd());
        
        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(emptyTaskId);
        verify(taskExecutionService, never()).executeTask(anyString(), any(TriggerMode.class));
    }

    @Test
    void testExecuteTaskNow_ExecutionServiceThrowsException() {
        // 准备测试数据 - 任务存在但执行服务抛出异常
        when(taskRepository.getByTaskId(validTaskId)).thenReturn(validTask);
        
        RuntimeException executionException = new RuntimeException("SSH连接失败");
        doThrow(executionException).when(taskExecutionService).executeTask(validTaskId, TriggerMode.MANUAL);

        // 执行测试并验证异常传播
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            inspectionTaskService.executeTaskNow(validTaskId);
        });

        // 验证异常信息
        assertEquals("SSH连接失败", exception.getMessage());
        
        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(validTaskId);
        verify(taskExecutionService, times(1)).executeTask(validTaskId, TriggerMode.MANUAL);
    }

    @Test
    void testExecuteTaskNow_CorrectTriggerModePassedToExecutionService() {
        // 准备测试数据
        when(taskRepository.getByTaskId(validTaskId)).thenReturn(validTask);

        // 执行测试
        inspectionTaskService.executeTaskNow(validTaskId);

        // 验证传递给执行服务的触发模式是MANUAL
        verify(taskExecutionService, times(1)).executeTask(eq(validTaskId), eq(TriggerMode.MANUAL));
    }

    @Test
    void testExecuteTaskNow_MultipleCallsWithSameTaskId() {
        // 测试相同任务ID的多次调用
        when(taskRepository.getByTaskId(validTaskId)).thenReturn(validTask);

        // 执行多次调用
        inspectionTaskService.executeTaskNow(validTaskId);
        inspectionTaskService.executeTaskNow(validTaskId);
        inspectionTaskService.executeTaskNow(validTaskId);

        // 验证每次都正确调用了服务
        verify(taskRepository, times(3)).getByTaskId(validTaskId);
        verify(taskExecutionService, times(3)).executeTask(validTaskId, TriggerMode.MANUAL);
    }

    @Test
    void testExecuteTaskNow_DifferentTaskIds() {
        // 测试不同任务ID的调用
        String taskId1 = "TASK-SSH-VM-001";
        String taskId2 = "TASK-SSH-VM-002";
        
        Task task1 = new Task();
        task1.setTaskId(taskId1);
        task1.setName("SSH虚拟机巡检任务1");
        
        Task task2 = new Task();
        task2.setTaskId(taskId2);
        task2.setName("SSH虚拟机巡检任务2");

        when(taskRepository.getByTaskId(taskId1)).thenReturn(task1);
        when(taskRepository.getByTaskId(taskId2)).thenReturn(task2);

        // 执行测试
        inspectionTaskService.executeTaskNow(taskId1);
        inspectionTaskService.executeTaskNow(taskId2);

        // 验证每个任务都被正确处理
        verify(taskRepository, times(1)).getByTaskId(taskId1);
        verify(taskRepository, times(1)).getByTaskId(taskId2);
        verify(taskExecutionService, times(1)).executeTask(taskId1, TriggerMode.MANUAL);
        verify(taskExecutionService, times(1)).executeTask(taskId2, TriggerMode.MANUAL);
    }

    @Test
    void testExecuteTaskNow_TaskExistsButInactiveStatus() {
        // 测试任务存在但状态为非活跃状态
        Task inactiveTask = new Task();
        inactiveTask.setTaskId(validTaskId);
        inactiveTask.setName("非活跃SSH虚拟机巡检任务");
        inactiveTask.setDescription("已停用的SSH巡检任务");
        inactiveTask.setStatus(CommonStatusEnum.DISABLE);

        when(taskRepository.getByTaskId(validTaskId)).thenReturn(inactiveTask);

        // 执行测试 - 即使任务状态为INACTIVE，executeTaskNow方法也应该能执行
        // 因为这是手动触发，不检查任务状态
        assertDoesNotThrow(() -> {
            inspectionTaskService.executeTaskNow(validTaskId);
        });

        // 验证调用
        verify(taskRepository, times(1)).getByTaskId(validTaskId);
        verify(taskExecutionService, times(1)).executeTask(validTaskId, TriggerMode.MANUAL);
    }
}