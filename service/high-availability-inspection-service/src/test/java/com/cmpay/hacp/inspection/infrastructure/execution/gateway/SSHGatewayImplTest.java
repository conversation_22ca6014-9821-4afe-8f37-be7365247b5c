package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.infrastructure.execution.config.SshProperties;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.SshConnectionConfig;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.channel.ChannelExec;
import org.apache.sshd.client.future.AuthFuture;
import org.apache.sshd.client.future.ConnectFuture;
import org.apache.sshd.client.future.OpenFuture;
import org.apache.sshd.client.session.ClientSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SSH网关实现类测试
 * 测试SSH连接、认证、脚本执行等核心功能
 */
@ExtendWith(MockitoExtension.class)
class SSHGatewayImplTest {

    @Mock
    private SshClient mockSshClient;

    @Mock
    private ClientSession mockSession;

    @Mock
    private ChannelExec mockChannelExec;

    @Mock
    private ConnectFuture mockConnectFuture;


    @Mock
    private AuthFuture mockAuthFuture;

    @Mock
    private OpenFuture mockOpenFuture;

    private SSHGatewayImpl sshGateway;
    private SshProperties sshProperties;
    private SshConnectionConfig passwordConfig;
    private SshConnectionConfig privateKeyConfig;
    private ScriptExecutionRequest shellRequest;
    private ScriptExecutionRequest pythonRequest;

    @BeforeEach
    void setUp() {
        // 设置SSH属性
        sshProperties = new SshProperties();
        sshProperties.setConnectionTimeout(5000);
        sshProperties.setExecutionTimeout(30000);
        sshProperties.setMaxConnections(10);

        // 创建SSH网关实例
        sshGateway = new SSHGatewayImpl(sshProperties);
        
        // 使用反射设置mock的sshClient
        ReflectionTestUtils.setField(sshGateway, "sshClient", mockSshClient);

        // 设置密码认证配置
        passwordConfig = SshConnectionConfig.builder()
                .host("127.0.0.1")
                .port(2222)
                .username("vagrant")
                .password("vagrant")
                .authType(SshConnectionConfig.AuthType.PASSWORD)
                .build();

        // 设置私钥认证配置
        privateKeyConfig = SshConnectionConfig.builder()
                .host("127.0.0.1")
                .port(2222)
                .username("vagrant")
                .privateKeyContent("-----BEGIN RSA PRIVATE KEY-----\n" +
                        "MIIEpQIBAAKCAQEA2GCUIoVln8ahRcTevt///QgMZSOV9KX4sHpHOdmmBzLqgOzO\n" +
                        "ddkmUejvnh7qEJw7ct+AjXF9p0X9j2o/LPAdYIV+6Un4GyFn7+XCqtG/ZwUkgMC9\n" +
                        "LiLqXzrruv2DD3F1aqaSDbxA9an4fyHgsSsmTLT3nuO1QBXokINnzjebzpcOaj+y\n" +
                        "85Mw7dUi9Yx1FExJLw/0F3ezNyYraiY+dZyMsNU24pB9x2yZSvi42ePt1IgyBKFm\n" +
                        "hFz/CgjYJVJaP03H8WGFXz+cVJ8cvrR4sMk2SXv9n5Y9Z7WJvJANg0L5PGh0OJ40\n" +
                        "Lf4XlHe6mxaVk8cDEL1rDrscE540E7dJ7rV6ewIDAQABAoIBACB+kICGq/ZMrBeA\n" +
                        "MztTBGyJcuMVbwWojyphkqS7bNWT7eeiqHbGP5z5cFK8rcYE0GC18+ZJ7ubZ+fQ5\n" +
                        "rncfMCo4ohCCzUG1yJgcVxm0Ghxsh0jWqRJup+fZP2F7hN0i2tFDZpetd8vs1Kay\n" +
                        "/b1dj4jhfHZeF/Ah2iUptsMfUsS5X+LgG5MhxUsufORoDZTCruTFcCNxaMsdz1D1\n" +
                        "pItcU6lNdvpjKIdG7yKqcrKy8oS3vA91UWCNharc9NmzbmTzLC7Oc8ZoBeaRv6im\n" +
                        "rjN4CCqXF/093FzTULFpUW7BnpBshEOnhf1FxZUwex9K9510c3w6Pgwv+yTFM+53\n" +
                        "i84PhqECgYEA+KGV/AV/fO5j4CV6sTeNdBlNzKkqDA8/WeaFITbvoFbcrMNfKAPc\n" +
                        "DY/DLYscaT1/Irxg8AVzudgsoIo3nCCmncFiBa0h9XS3CB8hTb8pVIO6U/pRyjVY\n" +
                        "rMtZeOadULy6ZgF348oTRVyZaGcliCpHuy2jcHOsGt6OZc/YRWDMLMMCgYEA3spG\n" +
                        "md6XaV+i/fbnu089JpDIHOZHF4/GFdnbVvaM5zX+kRi9Csn6MRFDqJ5HMzyCuAKp\n" +
                        "oSg4ITzSFJP4WooxkNPMcz185WO9QrRSo2w79Q5zTXQ270BQIVULBCUgFWEqu/bb\n" +
                        "Js3XFSU+MH+hl10CnJz7pJosah2x40pjMs/kf+kCgYEA5Dp30ks9tvGhzry4vnqP\n" +
                        "J3ae2vKh/UX10lFv/YhPOPDjStdgvvukAPLpqCHdZ6Gi1cD0fv+SNpOAi7G00GWM\n" +
                        "bJ16nFlMJeQIq7Tfvl1u0/zDipjqgWKszpkvvXJ8EAPeWwqYAgqZ2/7fd13PrwmY\n" +
                        "pPKE6BHWdye6sSy0iyMK6T8CgYEAvzrGgPqD4mm5oN4WqDtqlLi/W7b5HKtG7qiR\n" +
                        "0cJMzYUWHfsrAKC/hPB5r7k0CjGNTl7StUatbzIibJFaInTt+I8jtWL42njTEfdv\n" +
                        "22Bi0oR1yezP3mrInOO/Ue5/7Hn70V0hXNxgGoFeMQjMbfzi6gUBJpdDOk6Ey0Cl\n" +
                        "hw7U5kkCgYEA0rt6Qn6tB1y0X8Qy2a0/oUoGKDzVEAD3/Pmb91peyrzxpdmGNJgk\n" +
                        "/KajOvvLelsNdd2aKsPfKU0BXlPH95hbGg25Vof7nSNxdOSeWIgaaFCmCF2r5M7x\n" +
                        "vUrHtzu2yf9DJ1cfFDJjV/OXZV5ZDAEXJgdu2duQ7RxHh89zcdKuHvM=\n" +
                        "-----END RSA PRIVATE KEY-----")
                .authType(SshConnectionConfig.AuthType.PRIVATE_KEY)
                .build();

        // 设置Shell脚本执行请求
        shellRequest = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\necho 'Hello World'\necho '{\"cpu_usage\": 85.5}' | jq .")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(30)
                .build();

        // 设置Python脚本执行请求
        Map<String, String> env = new HashMap<>();
        env.put("PYTHONPATH", "/usr/local/lib/python3.8/site-packages");
        pythonRequest = ScriptExecutionRequest.builder()
                .scriptContent("#!/usr/bin/env python3\nimport json\nprint(json.dumps({'cpu_usage': 85.5}))")
                .scriptType(ScriptExecutionRequest.ScriptType.PYTHON)
                .environment(env)
                .workingDirectory("/tmp")
                .timeoutSeconds(60)
                .build();
    }

    @Test
    void testAfterPropertiesSet() {
        // 使用静态mock来测试初始化
        try (MockedStatic<SshClient> mockedSshClient = mockStatic(SshClient.class)) {
            mockedSshClient.when(SshClient::setUpDefaultClient).thenReturn(mockSshClient);
            
            // 创建新实例进行测试
            SSHGatewayImpl gateway = new SSHGatewayImpl(sshProperties);
            
            // 执行初始化
            gateway.afterPropertiesSet();
            
            // 验证调用
            mockedSshClient.verify(SshClient::setUpDefaultClient);
            verify(mockSshClient, times(1)).start();
        }
    }

    @Test
    void testDestroy() {
        // 设置mock行为
        when(mockSshClient.isClosed()).thenReturn(false);
        
        // 执行销毁
        sshGateway.destroy();
        
        // 验证调用
        verify(mockSshClient, times(1)).isClosed();
        verify(mockSshClient, times(1)).stop();
    }

    @Test
    void testDestroy_AlreadyClosed() {
        // 设置mock行为 - 客户端已关闭
        when(mockSshClient.isClosed()).thenReturn(true);
        
        // 执行销毁
        sshGateway.destroy();
        
        // 验证调用
        verify(mockSshClient, times(1)).isClosed();
        verify(mockSshClient, never()).stop();
    }

    @Test
    void testExecuteScript_Success_PasswordAuth() throws Exception {
        // 设置mock行为链
        setupSuccessfulConnection();
        setupSuccessfulPasswordAuth();
        setupSuccessfulScriptExecution();

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getExitCode());
        assertEquals("Hello World\n{\"cpu_usage\": 85.5}", result.getStdout());
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() >= 0);

        // 验证关键方法调用
        verify(mockSshClient, times(1)).connect(eq("vagrant"), eq("127.0.0.1"), eq(2222));
        verify(mockSession, times(1)).addPasswordIdentity("vagrant");
        verify(mockSession, times(1)).auth();
        verify(mockSession, times(4)).createExecChannel(anyString()); // 创建脚本、chmod、执行脚本
    }

    @Test
    void testExecuteScript_Success_PrivateKeyAuth() throws Exception {
        // 设置mock行为链
        setupSuccessfulConnection();
        setupSuccessfulPrivateKeyAuth();
        setupSuccessfulScriptExecution();

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(privateKeyConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getExitCode());
        assertEquals("Hello World\n{\"cpu_usage\": 85.5}", result.getStdout());
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() >= 0);

        // 验证关键方法调用
        verify(mockSshClient, times(1)).connect(eq("vagrant"), eq("127.0.0.1"), eq(2222));
        verify(mockSession, times(1)).addPublicKeyIdentity(any());
        verify(mockSession, times(1)).auth();
    }

    @Test
    void testExecuteScript_SshClientNotInitialized() {
        // 设置SSH客户端为null
        ReflectionTestUtils.setField(sshGateway, "sshClient", null);

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("SSH client is not initialized or has been shut down", result.getErrorMessage());
    }

    @Test
    void testExecuteScript_SshClientClosed() {
        // 设置SSH客户端为已关闭状态
        when(mockSshClient.isClosed()).thenReturn(true);

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("SSH client is not initialized or has been shut down", result.getErrorMessage());
    }

    @Test
    void testExecuteScript_ConnectionFailed() throws Exception {
        // 设置连接失败
        when(mockSshClient.isClosed()).thenReturn(false);
        when(mockSshClient.connect(anyString(), anyString(), anyInt())).thenReturn(mockConnectFuture);
        when(mockConnectFuture.verify(anyLong())).thenThrow(new IOException("Connection failed"));

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("Connection failed"));
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteScript_AuthenticationFailed() throws Exception {
        // 设置连接成功但认证失败
        setupSuccessfulConnection();
        when(mockSession.auth()).thenReturn(mockAuthFuture);
        when(mockAuthFuture.verify(anyLong())).thenReturn(mockAuthFuture);
        when(mockAuthFuture.isSuccess()).thenReturn(false);

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("SSH authentication failed", result.getErrorMessage());
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteScript_ScriptExecutionFailed() throws Exception {
        // 设置连接和认证成功，但脚本执行失败
        setupSuccessfulConnection();
        setupSuccessfulPasswordAuth();
        
        // 设置脚本执行失败
        when(mockSession.createExecChannel(anyString())).thenReturn(mockChannelExec);
        when(mockChannelExec.open()).thenReturn(mockOpenFuture);
        when(mockOpenFuture.verify(anyLong())).thenReturn(mockOpenFuture);
        when(mockChannelExec.getExitStatus()).thenReturn(1); // 非零退出码
        
        // 设置输出流
        setupOutputStreams("", "Script execution failed");

        // 执行测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, shellRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(1, result.getExitCode());
        assertEquals("", result.getStdout());
        assertEquals("Script execution failed", result.getStderr());
        assertTrue(result.getExecutionTime() >= 0);
    }

    @Test
    void testExecuteScript_WithPythonScript() throws Exception {
        // 设置成功的连接和认证
        setupSuccessfulConnection();
        setupSuccessfulPasswordAuth();
        setupSuccessfulScriptExecution();

        // 执行Python脚本测试
        ExecutionResult result = sshGateway.executeScript(passwordConfig, pythonRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getExitCode());
        assertTrue(result.getExecutionTime() >= 0);

        // 验证Python脚本执行命令包含环境变量和工作目录
        verify(mockSession, times(4)).createExecChannel(anyString());
    }

    // 辅助方法 - 设置成功的连接
    private void setupSuccessfulConnection() throws Exception {
        when(mockSshClient.isClosed()).thenReturn(false);
        when(mockSshClient.connect(anyString(), anyString(), anyInt())).thenReturn(mockConnectFuture);
        when(mockConnectFuture.verify(anyLong())).thenReturn(mockConnectFuture);
        when(mockConnectFuture.getSession()).thenReturn(mockSession);
    }

    // 辅助方法 - 设置成功的密码认证
    private void setupSuccessfulPasswordAuth() throws Exception {
        when(mockSession.auth()).thenReturn(mockAuthFuture);
        when(mockAuthFuture.verify(anyLong())).thenReturn(mockAuthFuture);
        when(mockAuthFuture.isSuccess()).thenReturn(true);
    }

    // 辅助方法 - 设置成功的私钥认证
    private void setupSuccessfulPrivateKeyAuth() throws Exception {
        when(mockSession.auth()).thenReturn(mockAuthFuture);
        when(mockAuthFuture.verify(anyLong())).thenReturn(mockAuthFuture);
        when(mockAuthFuture.isSuccess()).thenReturn(true);
    }

    // 辅助方法 - 设置成功的脚本执行
    private void setupSuccessfulScriptExecution() throws Exception {
        when(mockSession.createExecChannel(anyString())).thenReturn(mockChannelExec);
        when(mockChannelExec.open()).thenReturn(mockOpenFuture);
        when(mockOpenFuture.verify(anyLong())).thenReturn(mockOpenFuture);
        when(mockChannelExec.getExitStatus()).thenReturn(0);
        
        setupOutputStreams("Hello World\n{\"cpu_usage\": 85.5}", "");
    }

    // 辅助方法 - 设置输出流
    private void setupOutputStreams(String stdout, String stderr) throws Exception {
        // 模拟stdout输出
        doAnswer(invocation -> {
            ByteArrayOutputStream outputStream = invocation.getArgument(0);
            outputStream.write(stdout.getBytes());
            return null;
        }).when(mockChannelExec).setOut(any(ByteArrayOutputStream.class));

        // 模拟stderr输出
        doAnswer(invocation -> {
            ByteArrayOutputStream outputStream = invocation.getArgument(0);
            outputStream.write(stderr.getBytes());
            return null;
        }).when(mockChannelExec).setErr(any(ByteArrayOutputStream.class));
    }
}