package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.domain.model.enums.PluginOutputFieldType;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginOutputField;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 脚本结果解析服务测试
 */
@ExtendWith(MockitoExtension.class)
class ExecutionResultParserServiceImplTest {

    @InjectMocks
    private ExecutionResultParserServiceImpl executionResultParserService;

    private List<PluginOutputField> fieldDefinitions;

    @BeforeEach
    void setUp() {
        // 设置字段定义
        PluginOutputField cpuUsage = new PluginOutputField();
        cpuUsage.setFieldName("cpu.usage");
        cpuUsage.setFieldType(PluginOutputFieldType.NUMERIC);

        PluginOutputField memoryUsage = new PluginOutputField();
        memoryUsage.setFieldName("memory.usage");
        memoryUsage.setFieldType(PluginOutputFieldType.NUMERIC);

        PluginOutputField serviceStatus = new PluginOutputField();
        serviceStatus.setFieldName("service.status");
        serviceStatus.setFieldType(PluginOutputFieldType.BOOLEAN);

        PluginOutputField serviceName = new PluginOutputField();
        serviceName.setFieldName("service.name");
        serviceName.setFieldType(PluginOutputFieldType.STRING);

        fieldDefinitions = Arrays.asList(cpuUsage, memoryUsage, serviceStatus, serviceName);
    }

    @Test
    void testParseJsonOutput() {
        // 测试JSON格式输出解析
        String jsonOutput = "{\n" +
                "  \"cpu\": {\n" +
                "    \"usage\": 85.5\n" +
                "  },\n" +
                "  \"memory\": {\n" +
                "    \"usage\": 1024\n" +
                "  },\n" +
                "  \"service\": {\n" +
                "    \"status\": true,\n" +
                "    \"name\": \"nginx\"\n" +
                "  }\n" +
                "}";

        Map<String, Object> result = executionResultParserService.parsePluginOutput(jsonOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("85.5"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("1024"), result.get("memory.usage"));
        assertEquals(true, result.get("service.status"));
        assertEquals("nginx", result.get("service.name"));
    }

    @Test
    void testParseKeyValueOutput() {
        // 测试键值对格式输出解析
        String keyValueOutput = "cpu.usage=75.2\n" +
                "memory.usage=2048\n" +
                "service.status=true\n" +
                "service.name=apache\n";

        Map<String, Object> result = executionResultParserService.parsePluginOutput(keyValueOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("75.2"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("2048"), result.get("memory.usage"));
        assertEquals(true, result.get("service.status"));
        assertEquals("apache", result.get("service.name"));
    }

    @Test
    void testParseKeyValueOutputWithColon() {
        // 测试冒号分隔的键值对格式
        String keyValueOutput = "cpu.usage: 90.0\n" +
                "memory.usage: 512\n" +
                "service.status: false\n" +
                "service.name: mysql\n";

        Map<String, Object> result = executionResultParserService.parsePluginOutput(keyValueOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("90.0"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("512"), result.get("memory.usage"));
        assertEquals(false, result.get("service.status"));
        assertEquals("mysql", result.get("service.name"));
    }

    @Test
    void testConvertFieldValue_Numeric() {
        // 测试数值类型转换
        Object result1 = executionResultParserService.convertFieldValue("85.5", PluginOutputFieldType.NUMERIC);
        assertEquals(new BigDecimal("85.5"), result1);

        Object result2 = executionResultParserService.convertFieldValue("100%", PluginOutputFieldType.NUMERIC);
        assertEquals(new BigDecimal("100"), result2);

        Object result3 = executionResultParserService.convertFieldValue("1024MB", PluginOutputFieldType.NUMERIC);
        assertEquals(new BigDecimal("1024"), result3);
    }

    @Test
    void testConvertFieldValue_Boolean() {
        // 测试布尔类型转换
        assertTrue((Boolean) executionResultParserService.convertFieldValue("true", PluginOutputFieldType.BOOLEAN));
        assertTrue((Boolean) executionResultParserService.convertFieldValue("1", PluginOutputFieldType.BOOLEAN));
        assertTrue((Boolean) executionResultParserService.convertFieldValue("yes", PluginOutputFieldType.BOOLEAN));
        assertTrue((Boolean) executionResultParserService.convertFieldValue("enabled", PluginOutputFieldType.BOOLEAN));

        assertFalse((Boolean) executionResultParserService.convertFieldValue("false", PluginOutputFieldType.BOOLEAN));
        assertFalse((Boolean) executionResultParserService.convertFieldValue("0", PluginOutputFieldType.BOOLEAN));
        assertFalse((Boolean) executionResultParserService.convertFieldValue("no", PluginOutputFieldType.BOOLEAN));
        assertFalse((Boolean) executionResultParserService.convertFieldValue("disabled", PluginOutputFieldType.BOOLEAN));
    }

    @Test
    void testConvertFieldValue_String() {
        // 测试字符串类型转换
        Object result = executionResultParserService.convertFieldValue("test value", PluginOutputFieldType.STRING);
        assertEquals("test value", result);
    }

    @Test
    void testValidateFieldType() {
        // 测试字段类型验证
        assertTrue(executionResultParserService.validateFieldType("85.5", PluginOutputFieldType.NUMERIC));
        assertTrue(executionResultParserService.validateFieldType("true", PluginOutputFieldType.BOOLEAN));
        assertTrue(executionResultParserService.validateFieldType("test", PluginOutputFieldType.STRING));

        assertFalse(executionResultParserService.validateFieldType("invalid", PluginOutputFieldType.NUMERIC));
        assertFalse(executionResultParserService.validateFieldType("maybe", PluginOutputFieldType.BOOLEAN));
    }

    @Test
    void testParseEmptyOutput() {
        // 测试空输出
        Map<String, Object> result = executionResultParserService.parsePluginOutput("", fieldDefinitions);
        assertTrue(result.isEmpty());

        result = executionResultParserService.parsePluginOutput(null, fieldDefinitions);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseOutputWithEmptyFieldDefinitions() {
        // 测试空字段定义
        String output = "cpu.usage=85.5";
        Map<String, Object> result = executionResultParserService.parsePluginOutput(output, null);
        assertTrue(result.isEmpty());

        result = executionResultParserService.parsePluginOutput(output, Arrays.asList());
        assertTrue(result.isEmpty());
    }

    @Test
    void testParsePartialMatch() {
        // 测试部分匹配场景
        String output = "cpu.usage=85.5\n" +
                "unknown.field=123\n" +
                "service.name=nginx\n";

        Map<String, Object> result = executionResultParserService.parsePluginOutput(output, fieldDefinitions);

        assertEquals(2, result.size()); // 只有匹配的字段
        assertEquals(new BigDecimal("85.5"), result.get("cpu.usage"));
        assertEquals("nginx", result.get("service.name"));
        assertNull(result.get("unknown.field")); // 未定义的字段不会被包含
    }

    @Test
    void testParsePluginOutput_WithJSONPath() {
        // 测试JSONPath取值路径功能
        String jsonOutput = "{\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"metrics\": {\n" +
                "                \"cpu\": {\"usage\": 85.3},\n" +
                "                \"memory\": {\"usage\": 90.1}\n" +
                "            },\n" +
                "            \"status\": \"running\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"timestamp\": \"2024-01-01T10:00:00Z\"\n" +
                "}";


        // 创建使用JSONPath的字段定义
        PluginOutputField cpuUsageWithPath = new PluginOutputField();
        cpuUsageWithPath.setFieldName("cpu_usage");
        cpuUsageWithPath.setFieldPath("$.data[0].metrics.cpu.usage");
        cpuUsageWithPath.setFieldType(PluginOutputFieldType.NUMERIC);

        PluginOutputField memoryUsageWithPath = new PluginOutputField();
        memoryUsageWithPath.setFieldName("memory_usage");
        memoryUsageWithPath.setFieldPath("$.data[0].metrics.memory.usage");
        memoryUsageWithPath.setFieldType(PluginOutputFieldType.NUMERIC);

        PluginOutputField statusWithPath = new PluginOutputField();
        statusWithPath.setFieldName("pod_status");
        statusWithPath.setFieldPath("$.data[0].status");
        statusWithPath.setFieldType(PluginOutputFieldType.STRING);

        List<PluginOutputField> jsonPathDefinitions = Arrays.asList(
                cpuUsageWithPath, memoryUsageWithPath, statusWithPath
        );

        Map<String, Object> result = executionResultParserService.parsePluginOutput(jsonOutput, jsonPathDefinitions);

        assertEquals(3, result.size());
        assertEquals(85.3, result.get("cpu_usage"));
        assertEquals(90.1, result.get("memory_usage"));
        assertEquals("running", result.get("pod_status"));
    }

    @Test
    void testParsePluginOutput_MixedFieldNameAndJSONPath() {
        // 测试混合使用传统fieldName和JSONPath
        String jsonOutput = "{\n" +
                "    \"simple\": {\n" +
                "        \"value\": 42\n" +
                "    },\n" +
                "    \"complex\": {\n" +
                "        \"nested\": {\n" +
                "            \"deep\": {\n" +
                "                \"value\": 99\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";


        // 传统fieldName方式
        PluginOutputField simpleField = new PluginOutputField();
        simpleField.setFieldName("simple.value");
        simpleField.setFieldType(PluginOutputFieldType.NUMERIC);
        // valuePath为null，使用传统方式

        // JSONPath方式
        PluginOutputField complexField = new PluginOutputField();
        complexField.setFieldName("deep_value");
        complexField.setFieldPath("$.complex.nested.deep.value");
        complexField.setFieldType(PluginOutputFieldType.NUMERIC);

        List<PluginOutputField> mixedDefinitions = Arrays.asList(simpleField, complexField);

        Map<String, Object> result = executionResultParserService.parsePluginOutput(jsonOutput, mixedDefinitions);

        assertEquals(2, result.size());
        assertEquals(42, result.get("simple.value"));
        assertEquals(99, result.get("deep_value"));
    }

    @Test
    void testParsePluginOutput_InvalidJSONPath() {
        // 测试无效JSONPath的处理
        String jsonOutput = "{\n" +
                "    \"data\": {\n" +
                "        \"value\": 123\n" +
                "    }\n" +
                "}";


        PluginOutputField invalidPathField = new PluginOutputField();
        invalidPathField.setFieldName("invalid_field");
        invalidPathField.setFieldPath("$.nonexistent.path");
        invalidPathField.setFieldType(PluginOutputFieldType.NUMERIC);

        List<PluginOutputField> invalidDefinitions = Collections.singletonList(invalidPathField);

        Map<String, Object> result = executionResultParserService.parsePluginOutput(jsonOutput, invalidDefinitions);

        // 无效路径应该不返回结果
        assertEquals(0, result.size());
        assertFalse(result.containsKey("invalid_field"));
    }
}
