dependencies {
    api project(':common:high-availability-common')
    api project(':service:high-availability-tenant-service')
    api project(':service:high-availability-message-service')
    api project(':interface:high-availability-base-interface')
    api project(':interface:high-availability-emergency-interface')

    api('com.cmpay:lemon-framework-starter-context')
    api('com.cmpay:lemon-framework-starter-idgenerator')
    api('com.cmpay:lemon-framework-starter-datasource')
    api('com.cmpay:lemon-framework-starter-mybatis')
    api('com.cmpay:lemon-framework-starter-cache-jcache')
    api('com.cmpay:file-client-starter')
    api('com.cmpay:lemon-common')
    api('com.cmpay:alerting-starter')
    // 工作流相关
    api('org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.17.0')
    api('org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest:7.17.0')
    api('org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp:7.17.0')
//    api('com.jcraft:jsch:0.1.55')
//    api('org.camunda.community.rest:camunda-platform-7-rest-client-spring-boot-starter:7.17.0')
    api 'com.github.mwiede:jsch:0.2.20'
    api('org.mvel:mvel2:2.5.2.Final')
    api('org.apache.commons:commons-text:1.8')
    api('com.alibaba.fastjson2:fastjson2:2.0.49')

    api 'org.apache.poi:poi'
    api 'org.apache.poi:poi-ooxml'

    api("javax.mail:mail:1.4.7")
}
