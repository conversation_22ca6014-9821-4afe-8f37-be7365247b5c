<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.extend.container.dao.IEmergencyContainerDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        <id column="container_id" property="containerId" jdbcType="INTEGER" />
        <result column="client_id" property="clientId" jdbcType="VARCHAR" />
        <result column="client_secret" property="clientSecret" jdbcType="VARCHAR" />
        <result column="grant_type" property="grantType" jdbcType="VARCHAR" />
        <result column="username" property="username" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="VARCHAR" />
        <result column="root_url" property="rootUrl" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="cloud_workspace_id" property="cloudWorkspaceId" jdbcType="VARCHAR" />
        <result column="cloud_clusters" property="cloudClusters" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        container_id, client_id, client_secret, grant_type, username, password, root_url, 
        workspace_id, cloud_workspace_id, cloud_clusters, operator_id, operator_name, create_time, 
        update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from emergency_container
        where container_id = #{containerId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from emergency_container
        where container_id = #{containerId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        insert into emergency_container
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="containerId != null" >
                container_id,
            </if>
            <if test="clientId != null" >
                client_id,
            </if>
            <if test="clientSecret != null" >
                client_secret,
            </if>
            <if test="grantType != null" >
                grant_type,
            </if>
            <if test="username != null" >
                username,
            </if>
            <if test="password != null" >
                password,
            </if>
            <if test="rootUrl != null" >
                root_url,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="cloudWorkspaceId != null" >
                cloud_workspace_id,
            </if>
            <if test="cloudClusters != null" >
                cloud_clusters,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="containerId != null" >
                #{containerId,jdbcType=INTEGER},
            </if>
            <if test="clientId != null" >
                #{clientId,jdbcType=VARCHAR},
            </if>
            <if test="clientSecret != null" >
                #{clientSecret,jdbcType=VARCHAR},
            </if>
            <if test="grantType != null" >
                #{grantType,jdbcType=VARCHAR},
            </if>
            <if test="username != null" >
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null" >
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="rootUrl != null" >
                #{rootUrl,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="cloudWorkspaceId != null" >
                #{cloudWorkspaceId,jdbcType=VARCHAR},
            </if>
            <if test="cloudClusters != null" >
                #{cloudClusters,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        update emergency_container
        <set >
            <if test="clientId != null" >
                client_id = #{clientId,jdbcType=VARCHAR},
            </if>
            <if test="clientSecret != null" >
                client_secret = #{clientSecret,jdbcType=VARCHAR},
            </if>
            <if test="grantType != null" >
                grant_type = #{grantType,jdbcType=VARCHAR},
            </if>
            <if test="username != null" >
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null" >
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="rootUrl != null" >
                root_url = #{rootUrl,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="cloudWorkspaceId != null" >
                cloud_workspace_id = #{cloudWorkspaceId,jdbcType=VARCHAR},
            </if>
            <if test="cloudClusters != null" >
                cloud_clusters = #{cloudClusters,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where container_id = #{containerId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_container
        <where >
            <if test="containerId != null" >
                and container_id = #{containerId,jdbcType=INTEGER}
            </if>
            <if test="clientId != null" >
                and client_id = #{clientId,jdbcType=VARCHAR}
            </if>
            <if test="clientSecret != null" >
                and client_secret = #{clientSecret,jdbcType=VARCHAR}
            </if>
            <if test="grantType != null" >
                and grant_type = #{grantType,jdbcType=VARCHAR}
            </if>
            <if test="username != null" >
                and username = #{username,jdbcType=VARCHAR}
            </if>
            <if test="password != null" >
                and password = #{password,jdbcType=VARCHAR}
            </if>
            <if test="rootUrl != null" >
                and root_url = #{rootUrl,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="cloudWorkspaceId != null" >
                and cloud_workspace_id = #{cloudWorkspaceId,jdbcType=VARCHAR}
            </if>
            <if test="cloudClusters != null" >
                and cloud_clusters = #{cloudClusters,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>