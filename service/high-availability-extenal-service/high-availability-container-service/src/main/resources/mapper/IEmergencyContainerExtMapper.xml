<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.extend.container.dao.IEmergencyContainerExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        <id column="container_id" property="containerId" jdbcType="INTEGER" />
        <result column="client_id" property="clientId" jdbcType="VARCHAR" />
        <result column="client_secret" property="clientSecret" jdbcType="VARCHAR" />
        <result column="grant_type" property="grantType" jdbcType="VARCHAR" />
        <result column="username" property="username" jdbcType="VARCHAR" />
        <result column="password" property="password" jdbcType="VARCHAR" />
        <result column="root_url" property="rootUrl" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="cloud_workspace_id" property="cloudWorkspaceId" jdbcType="VARCHAR" />
        <result column="cloud_clusters" property="cloudClusters" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        container_id, client_id, client_secret, grant_type, username, root_url,
        workspace_id, cloud_workspace_id, cloud_clusters, operator_id, operator_name, create_time, update_time
    </sql>

    <sql id="Password_Column_list">
        password
    </sql>

    <select id="getDetailInfo" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Password_Column_list" />
        from emergency_container
        where
        workspace_id = #{workspaceId,jdbcType=VARCHAR}
        <if test="containerId != null">
            and container_id = #{containerId,jdbcType=INTEGER}
        </if>
        <if test="clientSecret != null">
            and client_secret = #{clientSecret,jdbcType=VARCHAR}
        </if>
        <if test="clientId != null">
            and client_id = #{clientId,jdbcType=VARCHAR}
        </if>
        <if test="username != null">
            and username = #{username,jdbcType=VARCHAR}
        </if>
    </select>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        delete from emergency_container
        where container_id = #{containerId,jdbcType=INTEGER}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.extend.container.entity.EmergencyContainerDO" >
        select
        <include refid="Base_Column_List" />
        from emergency_container
        <where >
            <if test="containerId != null" >
                and container_id = #{containerId,jdbcType=INTEGER}
            </if>
            <if test="clientId != null" >
                and client_id = #{clientId,jdbcType=INTEGER}
            </if>
            <if test="clientSecret != null" >
                and client_secret = #{clientSecret,jdbcType=VARCHAR}
            </if>
            <if test="grantType != null" >
                and grant_type = #{grantType,jdbcType=VARCHAR}
            </if>
            <if test="username != null" >
                and username = #{username,jdbcType=VARCHAR}
            </if>
            <if test="password != null" >
                and password = #{password,jdbcType=VARCHAR}
            </if>
            <if test="rootUrl != null" >
                and root_url = #{rootUrl,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>