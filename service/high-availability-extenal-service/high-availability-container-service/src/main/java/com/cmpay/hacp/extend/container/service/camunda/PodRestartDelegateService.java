package com.cmpay.hacp.extend.container.service.camunda;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.extend.container.bo.KubernetesApp;
import com.cmpay.hacp.extend.container.bo.PodRestartParamBO;
import com.cmpay.hacp.extend.container.k8s.dto.ResponseStatusRspDTO;
import com.cmpay.hacp.extend.container.service.adapter.HacpKubesphereServiceAdapter;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.service.EmergencyProcessService;
import com.cmpay.hacp.emergency.service.HacpCaseService;
import com.cmpay.hacp.emergency.service.HacpNodeRecodeService;
import com.cmpay.hacp.emergency.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 容器重启
 * @date 2024/8/29 14:46
 */
@Service("doRestartPod")
@Slf4j
public class PodRestartDelegateService extends ServiceTaskTemplateDelegate {

    private final HacpKubesphereServiceAdapter ksClient;


    public PodRestartDelegateService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService,
            HacpKubesphereServiceAdapter ksClient) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
        this.ksClient = ksClient;
    }

    @Override
    protected void processTask(){
        DelegateExecution delegateExecution = execution.get();
        ProcessExecuteLogEntity processExecuteLogEntity = logEntity.get();
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();
        String tenantId = delegateExecution.getTenantId();
        //获取活动任务id
        if (JudgeUtils.isNull(hacpEmergencyTaskBO)) {
            BusinessException.throwBusinessException(MsgEnum.TASK_NOT_EXIST);
        }
        // 获取调度ID 可能会是一个JSON格式，则需要进行类型转换
        String taskParam = hacpEmergencyTaskBO.getTaskParam();
        PodRestartParamBO paramBO = JsonUtil.strToObject(taskParam, PodRestartParamBO.class);
        assert paramBO != null;
        KubernetesApp kubernetesApp = paramBO.getKubernetesApp();
        //kubernetesApps.forEach(x->{
        processExecuteLogEntity.appendBuffer("执行实例：").appendBuffer(kubernetesApp.getAppName());
        if (JudgeUtils.isNotEmpty(kubernetesApp.getPodNames())) {
            processExecuteLogEntity.appendBuffer("开始重启实例下指定pod");
            logEntity.set(processExecuteLogEntity);
            kubernetesApp.getPodNames().forEach(y -> {
                ResponseStatusRspDTO response = ksClient.deletePod(tenantId, paramBO.getCluster(), paramBO.getNamespace(), y);
                if (response.getStatus() < 200 || response.getStatus() >= 300) {
                    ProcessExecuteLogEntity processExecuteLogEntity1 = logEntity.get();
                    processExecuteLogEntity.appendBuffer("重启POD失败：" + y + ", HTTP状态码：" + response.getStatus());
                    processExecuteLogEntity.appendBuffer("重启POD失败：" + y + ", 响应报文：" + response);
                    logEntity.set(processExecuteLogEntity1);
                    traceEnabled = false;
                    BusinessException.throwBusinessException(MsgEnum.POD_DELETE_FAIL);
                }
            });
            processExecuteLogEntity.appendBuffer("重启POD：" + kubernetesApp.getPodNames() + "完成");
        } else {
            //重启所选中的实例下面所有的pod
            processExecuteLogEntity.appendBuffer("重启所选中的实例下面所有的pod");
            logEntity.set(processExecuteLogEntity);
            Map<String, List<String>> map = ksClient.queryPods(tenantId, paramBO.getCluster(), paramBO.getNamespace(),null);
            List<String> pods = map.get(kubernetesApp.getAppName());
            if (JudgeUtils.isNotEmpty(pods)) {
                pods.forEach(y -> {
                    ProcessExecuteLogEntity processExecuteLogEntity1 = logEntity.get();
                    processExecuteLogEntity1.appendBuffer("容器pod：" + y + "开始重启.....");
                    ResponseStatusRspDTO response = ksClient.deletePod(tenantId, paramBO.getCluster(), paramBO.getNamespace(), y);
                    if (response.getStatus() < 200 || response.getStatus() >= 300) {
                        processExecuteLogEntity1.appendBuffer("重启POD失败：" + y + ", HTTP状态码：" + response.getStatus());
                        processExecuteLogEntity1.appendBuffer("重启POD失败：" + y + ", 响应报文：" + response);
                        traceEnabled = false;
                        BusinessException.throwBusinessException(MsgEnum.POD_DELETE_FAIL);
                    } else {
                        processExecuteLogEntity1.appendBuffer("容器pod：" + y + "重启完成.....");
                    }
                    logEntity.set(processExecuteLogEntity1);
                });
            }
        }
    }

}
