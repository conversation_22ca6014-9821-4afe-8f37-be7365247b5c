/*
 * @ClassName EmergencyContainerDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-28 13:33:11
 */
package com.cmpay.hacp.extend.container.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class EmergencyContainerDO extends BaseDO {
    /**
     * @Fields containerId id
     */
    private Integer containerId;
    /**
     * @Fields clientId 客户端id
     */
    private String clientId;
    /**
     * @Fields clientSecret 密钥
     */
    private String clientSecret;
    /**
     * @Fields grantType 授权类型
     */
    private String grantType;
    /**
     * @Fields username 账号
     */
    private String username;
    /**
     * @Fields password 密码
     */
    private String password;
    /**
     * @Fields rootUrl 端点根url
     */
    private String rootUrl;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields cloudWorkspaceId 云平台项目空间ID
     */
    private String cloudWorkspaceId;
    /**
     * @Fields cloudClusters 操作员
     */
    private String cloudClusters;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getContainerId() {
        return containerId;
    }

    public void setContainerId(Integer containerId) {
        this.containerId = containerId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRootUrl() {
        return rootUrl;
    }

    public void setRootUrl(String rootUrl) {
        this.rootUrl = rootUrl;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getCloudWorkspaceId() {
        return cloudWorkspaceId;
    }

    public void setCloudWorkspaceId(String cloudWorkspaceId) {
        this.cloudWorkspaceId = cloudWorkspaceId;
    }

    public String getCloudClusters() {
        return cloudClusters;
    }

    public void setCloudClusters(String cloudClusters) {
        this.cloudClusters = cloudClusters;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}