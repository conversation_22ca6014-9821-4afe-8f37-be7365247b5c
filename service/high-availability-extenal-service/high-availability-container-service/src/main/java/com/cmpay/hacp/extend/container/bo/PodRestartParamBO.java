package com.cmpay.hacp.extend.container.bo;

import com.cmpay.hacp.bo.task.TaskParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/8/29 10:28
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class PodRestartParamBO extends TaskParam {

    private String workspace;
    /**
     * 节点
     */
    private String cluster;
    /**
     * 命名空间
     */
    private String namespace;
    /**
     * 是否自动配置
     */
    private boolean autoConfig;

    private KubernetesApp kubernetesApp;

    private boolean dynamicConfig;
}
