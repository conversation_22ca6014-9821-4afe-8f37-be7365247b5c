package com.cmpay.hacp.extend.container.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.dao.IEmergencyContainerExtDao;
import com.cmpay.hacp.extend.container.entity.EmergencyContainerDO;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:23
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HacpContainerServiceImpl implements HacpContainerService {

    private final IEmergencyContainerExtDao emergencyContainerDao;

    private final SystemCipherService systemCipherService;

    @Override
    public void add(ContainerConfigBO bo) {
        if (JudgeUtils.isBlank(bo.getPassword()) ||CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())) {
            BusinessException.throwBusinessException(MsgEnum.INCOMPLETE_PARAM);
        }
        if(JudgeUtils.isNotNull(bo.getPassword())){
            bo.setPassword(systemCipherService.otherModuleEncryptAndDecryptData(bo.getUuid(), bo.getPassword()));
        }
        EmergencyContainerDO insert = BeanConvertUtil.convert(bo, EmergencyContainerDO.class);
        emergencyContainerDao.insert(insert);
    }

    @Override
    public void update(ContainerConfigBO bo) {
        String password = bo.getPassword();
        String uuid = bo.getUuid();
        bo.setPassword(null);
        ContainerConfigBO detailInfo = this.getDetailInfo(bo);
        if(JudgeUtils.isNull(detailInfo)){
            log.error("info is null");
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_USER_INFO_IS_NULL);
        }

        if(JudgeUtils.isNull(detailInfo) && CommonConstant.ENCRYPTED_DISPLAY.equals(password)){
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PASSWORD_IS_ERROR);
        }

        if (!CommonConstant.ENCRYPTED_DISPLAY.equals(password)) {
            bo.setPassword(password);
        }
        if(JudgeUtils.isNotNull(bo.getPassword())){
            bo.setPassword(systemCipherService.otherModuleEncryptAndDecryptData(uuid, password));
        }
        bo.setContainerId(detailInfo.getContainerId());
        bo.setUpdateTime(LocalDateTime.now());
        EmergencyContainerDO entity = BeanConvertUtil.convert(bo, EmergencyContainerDO.class);
        emergencyContainerDao.update(entity);
    }

    @Override
    public void delete(ContainerConfigBO bo) {
        EmergencyContainerDO query = BeanConvertUtil.convert(bo, EmergencyContainerDO.class);
        emergencyContainerDao.deleteExt(query);
    }

    @Override
    public ContainerConfigBO getDetailInfo(ContainerConfigBO bo) {
        EmergencyContainerDO query = BeanConvertUtil.convert(bo, EmergencyContainerDO.class);
        EmergencyContainerDO detailInfo = emergencyContainerDao.getDetailInfo(query);
        if(JudgeUtils.isNull(detailInfo)){
            return null;
        }
        ContainerConfigBO convert = BeanConvertUtil.convert(detailInfo, ContainerConfigBO.class);
        convert.setCloudClusterList(JSON.parseArray(detailInfo.getCloudClusters(), String.class));
        return convert;
    }

    @Override
    public ContainerConfigBO getDecryptDetailInfo(ContainerConfigBO bo) {
        ContainerConfigBO detailInfo = this.getDetailInfo(bo);
        if(JudgeUtils.isNull(detailInfo)){
            return null;
        }
        detailInfo.setPassword(systemCipherService.otherModuleDecryptData(detailInfo.getPassword()));
        return detailInfo;
    }

    @Override
    public PageInfo<ContainerConfigBO> getPage(int pageNum, int pageSize, ContainerConfigBO bo) {
        EmergencyContainerDO query = BeanConvertUtil.convert(bo, EmergencyContainerDO.class);
        return  PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> emergencyContainerDao.findExt(query).stream().map(x->{
                    ContainerConfigBO containerConfigBO = BeanUtils.copyPropertiesReturnDest(new ContainerConfigBO(), x);
                    containerConfigBO.setCloudClusterList(JSON.parseArray(x.getCloudClusters(), String.class));
                    return containerConfigBO;
                }).collect(Collectors.toList()));
    }

}
