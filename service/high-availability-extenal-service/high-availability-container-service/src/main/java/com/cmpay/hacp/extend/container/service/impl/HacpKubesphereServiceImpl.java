package com.cmpay.hacp.extend.container.service.impl;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.bo.KubesphereMetaData;
import com.cmpay.hacp.extend.container.k8s.KubesphereClient;
import com.cmpay.hacp.extend.container.k8s.dto.KubesphereRspDTO;
import com.cmpay.hacp.extend.container.k8s.dto.KubesphereTokenReqDTO;
import com.cmpay.hacp.extend.container.k8s.dto.KubesphereTokenRspDTO;
import com.cmpay.hacp.extend.container.service.HacpContainerService;
import com.cmpay.hacp.extend.container.service.adapter.HacpKubesphereServiceAdapter;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Primary
public class HacpKubesphereServiceImpl extends HacpKubesphereServiceAdapter {

    private final SystemCipherService systemCipherService;

    public HacpKubesphereServiceImpl(HacpContainerService containerService,
            SystemCipherService systemCipherService,
            SystemCacheService systemCacheService,
            KubesphereClient kubesphereClient) {
        super(containerService, systemCacheService, kubesphereClient);
        this.systemCipherService = systemCipherService;
    }


    public List<KubesphereMetaData> queryWorkspaces(ContainerConfigBO bo, String workspaceId) {
        KubesphereTokenReqDTO tokenBody = new KubesphereTokenReqDTO();
        if (JudgeUtils.isNotNull(bo.getContainerId()) && CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())) {
            tokenBody = buildTokenBody(bo, workspaceId);
        } else {
            if (JudgeUtils.isNotBlank(bo.getPassword())) {
                bo.setPassword(systemCipherService.subDecryptData(bo.getUuid(), bo.getPassword()));
            }
            tokenBody.setClient_id(bo.getClientId());
            tokenBody.setClient_secret(bo.getClientSecret());
            tokenBody.setGrant_type(bo.getGrantType());
            tokenBody.setUsername(bo.getUsername());
            tokenBody.setPassword(bo.getPassword());
            tokenBody.setWorkspaceId(bo.getCloudWorkspaceId());
        }

        //先获取token
        KubesphereTokenRspDTO token = getToken(tokenBody);
        if (token != null) {
            KubesphereRspDTO workspaces = kubesphereClient.getWorkspaces_v1beta1(token.getAccessToken());
            if (JudgeUtils.isNull(workspaces)) {
                return null;
            }
            return parseMetaDataWorkSpace(workspaces);
        }
        return null;
    }
}
