package com.cmpay.hacp.extend.cmft.bo;

import lombok.Data;

import java.util.List;

@Data
public class CmftDispatchStrategyBO {

    private String id;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 名称
     */
    private String name;
    /**
     * 修饰符
     */
    private String mod;
    /**
     * URL的标签ID列表
     */
    private List<String> urlTagIdList;
    /**
     * client的标签ID列表
     */
    private List<String> clientTagIdList;
    /**
     * 模式
     */
    private String mode;
    /**
     * 类型
     */
    private int type;
    /**
     * 状态，1可用，0禁用
     */
    private int status;
    /**
     * 是否内容被修改
     */
    private int modifyStatus;
    /**
     * 修改内容
     */
    private String modifyContent;
    /**
     * 创建人
     */
    private String createBy;
    private String updateBy;
    private String gmtCreate;
    private String gmtUpdate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 服务器路径数量
     */
    private int urlCount;
    /**
     * 是否发布，0未发布，1已发布
     */
    private Integer releaseStatus;

    private String key;
}
