package com.cmpay.hacp.extend.cmft.client;

import com.cmft.api.utils.dto.CmftApiEncryptDTO;
import com.cmpay.hacp.extend.cmft.client.dto.BaseApiDTO;
import com.cmpay.hacp.extend.cmft.client.dto.CmftApiDecryptDTO;
import com.cmpay.hacp.extend.cmft.constant.HttpHeadConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 后续可能切换成接口调用，不使用rest调用容器接口
 *
 * <AUTHOR>
 * @create 2024/10/12 14:11
 * @since 1.0.0
 */
@FeignClient(url = "${hacp.emergence.cmft.dispatch.url: }", name = "cmftDispatch", contextId = "cmftDispatchClient")
public interface CmftDispatchClient {

    /**
     * 查询项目策略
     * @return
     */
    @PostMapping(value ="/queryStrategyList")
    BaseApiDTO<CmftApiDecryptDTO> queryStrategyList(@RequestHeader(HttpHeadConstant.PROJECT_ID) String projectId, @RequestBody CmftApiEncryptDTO reqDTO);

    /**
     * 修改策略状态
     * @return
     */
    @PostMapping(value ="/modifyStrategyStatus")
    BaseApiDTO<CmftApiDecryptDTO> modifyStrategyStatus(@RequestHeader(HttpHeadConstant.PROJECT_ID) String projectId, @RequestBody CmftApiEncryptDTO reqDTO);

    /**
     * 发布策略
     * @return
     */
    @PostMapping(value ="/releaseStrategy")
    BaseApiDTO<CmftApiDecryptDTO> releaseStrategy(@RequestHeader(HttpHeadConstant.PROJECT_ID) String projectId, @RequestBody CmftApiEncryptDTO reqDTO);
}
