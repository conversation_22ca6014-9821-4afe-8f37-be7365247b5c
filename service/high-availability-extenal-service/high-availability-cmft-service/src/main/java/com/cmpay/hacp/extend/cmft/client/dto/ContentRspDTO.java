package com.cmpay.hacp.extend.cmft.client.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;

import java.util.List;
@Data
@ToString
public class ContentRspDTO {
    private int total;
    @JsonIgnore
    private List<Item> list;
    private int pageNum;
    private int pageSize;
    private int size;
    private int startRow;
    private int endRow;
    private int pages;
    private int prePage;
    private int nextPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private boolean hasPreviousPage;
    private boolean hasNextPage;
    private int navigatePages;
    @JsonIgnore
    private List<Integer> navigatepageNums;
    private int navigateFirstPage;
    private int navigateLastPage;
    @Data
    @ToString
    public static class Item {
        private String id;
        /**
         * 项目id
         */
        private String projectId;
        /**
         * 名称
         */
        private String name;
        /**
         * 修饰符
         */
        private String mod;
        /**
         * URL的标签ID列表
         */
        private List<String> urlTagIdList;
        /**
         * client的标签ID列表
         */
        private List<String> clientTagIdList;
        /**
         * 模式
         */
        private String mode;
        /**
         * 类型
         */
        private int type;
        /**
         * 状态，1可用，0禁用
         */
        private int status;
        /**
         * 是否内容被修改
         */
        private int modifyStatus;
        /**
         * 修改内容
         */
        private String modifyContent;
        /**
         * 创建人
         */
        private String createBy;
        private String updateBy;
        private String gmtCreate;
        private String gmtUpdate;
        /**
         * 备注
         */
        private String remark;
        /**
         * 服务器路径数量
         */
        private int urlCount;

        /**
         * 是否发布，0未发布，1已发布
         */
        private Integer releaseStatus;
    }
}
