package com.cmpay.hacp.extend.cmft.service;

import com.cmpay.hacp.extend.cmft.bo.CmftDispatchConfigBO;
import com.cmpay.hacp.extend.cmft.bo.CmftDispatchStrategyBO;
import com.cmpay.hacp.extend.cmft.client.dto.QueryStrategyListReqDTO;
import com.cmpay.lemon.framework.page.PageInfo;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:23
 * @since 1.0.0
 */

public interface CmftDispatchService {

    PageInfo<CmftDispatchStrategyBO> queryStrategyPage(QueryStrategyListReqDTO reqDTO,String workspaceId, CmftDispatchConfigBO dispatchConfigBO);

    void modifyStrategyStatus(CmftDispatchConfigBO config, String id, Integer status);

    void releaseStrategy(CmftDispatchConfigBO config, String... ids);
}
