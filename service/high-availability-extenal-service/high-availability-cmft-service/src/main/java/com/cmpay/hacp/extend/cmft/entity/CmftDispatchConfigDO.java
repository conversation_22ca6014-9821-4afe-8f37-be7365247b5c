/*
 * @ClassName CmftDispatchConfigDO
 * @Description 
 * @version 1.0
 * @Date 2024-11-11 10:09:45
 */
package com.cmpay.hacp.extend.cmft.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class CmftDispatchConfigDO extends BaseDO {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields workspaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields externalPublicKey 公钥
     */
    private String externalPublicKey;
    /**
     * @Fields externalPrivateKey 私钥
     */
    private String externalPrivateKey;
    /**
     * @Fields cmftPublicKey 双活公钥
     */
    private String cmftPublicKey;
    /**
     * @Fields projectId 在双活的项目id
     */
    private String projectId;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private Boolean status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getExternalPublicKey() {
        return externalPublicKey;
    }

    public void setExternalPublicKey(String externalPublicKey) {
        this.externalPublicKey = externalPublicKey;
    }

    public String getExternalPrivateKey() {
        return externalPrivateKey;
    }

    public void setExternalPrivateKey(String externalPrivateKey) {
        this.externalPrivateKey = externalPrivateKey;
    }

    public String getCmftPublicKey() {
        return cmftPublicKey;
    }

    public void setCmftPublicKey(String cmftPublicKey) {
        this.cmftPublicKey = cmftPublicKey;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}