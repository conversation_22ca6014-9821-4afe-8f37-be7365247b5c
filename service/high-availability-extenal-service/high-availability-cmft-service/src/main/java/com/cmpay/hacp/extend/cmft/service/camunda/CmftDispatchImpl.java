package com.cmpay.hacp.extend.cmft.service.camunda;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.extend.cmft.bo.CmftDispatchParamBO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Slf4j
@Component(CmftDispatchImpl.BEAN_NAME)
public class CmftDispatchImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "cmftDispatchImpl";

    public CmftDispatchImpl() {
        super(Collections.singletonList(new TaskStrategyType("7", "同城双活流量调度", BEAN_NAME)));
    }

    @Override
    public boolean checkTaskParam(String json) {
        CmftDispatchParamBO taskParam = toTaskParam(json);
        if(taskParam.getDynamicConfig()){
            return true;
        }
        if(JudgeUtils.isNull(taskParam.getCmftStatus())){{
            log.error("status is null");
            return false;
        }}
        if(JudgeUtils.isEmpty(taskParam.getList())){
            log.error("list is null");
            return false;
        }
        return true;
    }

    @Override
    public CmftDispatchParamBO toTaskParam(String json) {
        return JsonUtil.strToObject(json, CmftDispatchParamBO.class );
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandleAfter(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        //检查任务执行参数是否正确
        if (!checkTaskParam(taskInfo.getTaskParam())) {
            log.error("task param is error:{}",taskInfo.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask) node).setCamundaDelegateExpression("${doCmftDispatch}");
            ((ServiceTask) node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }

    @Override
    public boolean isNotDynamicParam(String taskParamJson) {
        CmftDispatchParamBO taskParam = toTaskParam(taskParamJson);
        return !taskParam.getDynamicConfig();
    }

}
