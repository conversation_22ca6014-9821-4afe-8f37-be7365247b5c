<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.extend.cmft.dao.ICmftDispatchConfigExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="external_public_key" property="externalPublicKey" jdbcType="VARCHAR" />
        <result column="external_private_key" property="externalPrivateKey" jdbcType="VARCHAR" />
        <result column="cmft_public_key" property="cmftPublicKey" jdbcType="VARCHAR" />
        <result column="project_id" property="projectId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="BIT" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, workspace_id, external_public_key, external_private_key, cmft_public_key, project_id,
        operator_id, operator_name, create_time, update_time, status
    </sql>

</mapper>