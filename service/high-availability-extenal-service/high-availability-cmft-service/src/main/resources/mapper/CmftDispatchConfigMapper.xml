<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.extend.cmft.dao.ICmftDispatchConfigDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="external_public_key" property="externalPublicKey" jdbcType="VARCHAR" />
        <result column="external_private_key" property="externalPrivateKey" jdbcType="VARCHAR" />
        <result column="cmft_public_key" property="cmftPublicKey" jdbcType="VARCHAR" />
        <result column="project_id" property="projectId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="BIT" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, workspace_id, external_public_key, external_private_key, cmft_public_key, project_id, 
        operator_id, operator_name, create_time, update_time, status
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from cmft_dispatch_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from cmft_dispatch_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO" >
        insert into cmft_dispatch_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="externalPublicKey != null" >
                external_public_key,
            </if>
            <if test="externalPrivateKey != null" >
                external_private_key,
            </if>
            <if test="cmftPublicKey != null" >
                cmft_public_key,
            </if>
            <if test="projectId != null" >
                project_id,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="status != null" >
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="externalPublicKey != null" >
                #{externalPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="externalPrivateKey != null" >
                #{externalPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="cmftPublicKey != null" >
                #{cmftPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="projectId != null" >
                #{projectId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO" >
        update cmft_dispatch_config
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="externalPublicKey != null" >
                external_public_key = #{externalPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="externalPrivateKey != null" >
                external_private_key = #{externalPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="cmftPublicKey != null" >
                cmft_public_key = #{cmftPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="projectId != null" >
                project_id = #{projectId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.extend.cmft.entity.CmftDispatchConfigDO" >
        select 
        <include refid="Base_Column_List" />
        from cmft_dispatch_config
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="externalPublicKey != null" >
                and external_public_key = #{externalPublicKey,jdbcType=VARCHAR}
            </if>
            <if test="externalPrivateKey != null" >
                and external_private_key = #{externalPrivateKey,jdbcType=VARCHAR}
            </if>
            <if test="cmftPublicKey != null" >
                and cmft_public_key = #{cmftPublicKey,jdbcType=VARCHAR}
            </if>
            <if test="projectId != null" >
                and project_id = #{projectId,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=BIT}
            </if>
        </where>
    </select>
</mapper>