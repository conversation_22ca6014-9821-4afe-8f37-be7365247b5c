dependencies {
    api project(':common:high-availability-common')
    api project(':service:high-availability-tenant-service')
    api project(':service:high-availability-message-service')

    api project(':interface:high-availability-base-interface')
    api project(':interface:high-availability-extenal-interface:high-availability-sso-interface')

    api("com.cmpay:lemon-framework-starter-security")
    api("org.apache.httpcomponents:httpclient")
    api("org.springframework.cloud:spring-cloud-openfeign-core")
    api("com.fasterxml.jackson.core:jackson-databind")
    api('io.swagger:swagger-annotations')

}
