package com.cmpay.hacp.extend.sso.ticket.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


@Data
@ConfigurationProperties(prefix = "upms.user-sync-config")
public class LemonBatchUserProperties {
    /**
     * 是否启用定时任务，默认false
     */
    private boolean enabled;
    /**
     * 定时任务时间cron表达式
     */
    private String cron;
    /**
     * 同步用户的URL地址
     */
    private String userForSyncUrl = "https://55.147.49.12:9999/zyjk/queryAccountForSync";
    /**
     * 查询间隔：距离当天之前的天数
     */
    private String interval = "1";
    /**
     * 用户所属应用appId
     */
    private String userAppId = "buicommonmanager";
    /**
     * AES秘钥Key
     */
    private String syncPwdAesKey = "linkageqlinkageq";
    /**
     * 默认部门Id
     */
    private String defaultDeptId = "2";
    /**
     * 默认数据权限类型：1：本部门以及下级部门
     */
    private String defaultDutyId = "1";
    /**
     * 连接超时时间
     */
    private int connectTimeout = 6000;

    /**
     * 读取超时时间
     */
    private int readTimeout = 6000;

}
