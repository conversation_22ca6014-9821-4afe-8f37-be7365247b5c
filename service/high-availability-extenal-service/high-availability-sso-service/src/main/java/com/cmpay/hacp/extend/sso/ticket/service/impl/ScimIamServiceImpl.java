package com.cmpay.hacp.extend.sso.ticket.service.impl;

import com.cmpay.hacp.bo.system.DeptBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.extend.sso.ticket.bo.*;
import com.cmpay.hacp.extend.sso.ticket.enums.MessageCodeEnum;
import com.cmpay.hacp.extend.sso.ticket.properties.BatchScimIamProperties;
import com.cmpay.hacp.extend.sso.ticket.service.ScimIamService;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.system.service.SystemAccessTokenService;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemDepartmentService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.utils.RandomUtil;
import com.cmpay.hacp.utils.crypto.MessageDigestEncryptorUtil;
import com.cmpay.hacp.utils.crypto.PasswordUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 新4A账户账户体系
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ScimIamServiceImpl implements ScimIamService {

    /**
     *
     */
    public final static String APPLICATION_JSON = "application/json";
    /**
     *
     */
    public final static String ACCEPT = "Accept";
    /**
     *
     */
    private final static String SYNC_USER_LOCK = "LEMON_WEB_ADMIN:SYNC_USER_LOCK";
    /**
     * 通过AppId、AppSecret与Timestamp计算获得
     */
    private final static String X_APP_TOKEN = "X-App-Token";
    /**
     * 应用唯一标识
     */
    private final static String X_APP_ID = "X-App-Id";
    /**
     * 发送请求的时间戳
     */
    private final static String X_TIMESTAMP = "X-Timestamp";
    /**
     *
     */
    private final static String CONCAT = "&";
    /**
     *
     */
    private final static String EQ = "=";
    /**
     * 开始下标
     */
    private final static String START_INDEX = "startIndex";
    /**
     * 查多少条
     */
    private final static String COUNT = "count";
    /**
     * 过滤条件
     */
    private final static String FILTER = "filter";
    /**
     * 数据类型
     */
    private final static String SEARCH_TYPE = "searchType";
    @Resource
    private SystemUserService systemUserService;

    @Resource
    private SystemDepartmentService systemDepartmentService;

    @Resource
    private SystemAccessTokenService systemAccessTokenService;

    @Autowired
    private SystemCacheService systemCacheService;


    @Resource(name = "scimIamRestTemplate")
    private RestTemplate restTemplate;


    @Resource
    private BatchScimIamProperties batchScimIamProperties;

    /**
     * 获取apptoken
     *
     * @param currentTimeMillis
     * @param appId
     * @param appSecret
     * @return
     */
    private String getAppToken(String appId, String appSecret, String currentTimeMillis) {
        return MessageDigestEncryptorUtil.getSha256Digest(appId.concat(appSecret).concat(currentTimeMillis));
    }

    private HttpHeaders getScimIamHeaders() {
        return getScimIamHeaders(batchScimIamProperties.getAppId(), batchScimIamProperties.getAppSecret());
    }


    private HttpHeaders getScimIamHeaders(String appId, String appSecret) {
        HttpHeaders headers = new HttpHeaders();
        String currentTimeMillis = String.valueOf(System.currentTimeMillis());
        headers.add(X_APP_TOKEN, getAppToken(appId, appSecret, currentTimeMillis));
        headers.add(X_APP_ID, appId);
        headers.add(X_TIMESTAMP, currentTimeMillis);
        headers.add(ACCEPT, APPLICATION_JSON);
        return headers;
    }

    private String addUrlParams(int startIndex, int count, String filter, String searchType, String scimIamGetApi) {
        scimIamGetApi = scimIamGetApi.concat("?").concat(START_INDEX).concat(EQ).concat(String.valueOf(startIndex));
        scimIamGetApi = scimIamGetApi.concat(CONCAT).concat(COUNT).concat(EQ).concat(String.valueOf(count));
        if (JudgeUtils.isNotBlank(filter)) {
            scimIamGetApi = scimIamGetApi.concat(CONCAT).concat(FILTER).concat(EQ).concat(filter);
        }
        if (JudgeUtils.isNotBlank(searchType)) {
            scimIamGetApi = scimIamGetApi.concat(CONCAT).concat(SEARCH_TYPE).concat(EQ).concat(searchType);
        }
        return scimIamGetApi;
    }


    @Override
    public void sync() {
        try {
            String currentDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
            if (systemCacheService.setIfAbsent(SYNC_USER_LOCK, currentDateTimeStr, 1, TimeUnit.HOURS)) {
                if (batchScimIamProperties.isEnabledSyncUserOrganization()) {
                    log.warn("enabledSyncUserOrganization,before please backup sys_user table !!!");
                }
                this.syncAllScimIamUsers();
                if (batchScimIamProperties.isEnabledSyncUserOrganization()
                        || batchScimIamProperties.isEnabledOrganization()) {
                    this.syncAllScimIamOrganizations();
                }
            }
        } catch (Exception e) {
            log.info("新4A用户同步出现异常,异常信息：{}", e.getMessage());
        } finally {
            log.info("delete sync lock");
            systemCacheService.delete(SYNC_USER_LOCK);
        }
    }

    @Override
    public void syncAllScimIamUsers() {
        List<ScimIamUser> allScimIamUsers = this.getAllScimIamUsers();
        this.syncScimIamUsers(allScimIamUsers);

    }

    @Override
    public void syncAllScimIamUsers(int startIndex, int count, String filter, String searchType) {
        List<ScimIamUser> allScimIamUsers = this.getAllScimIamUsers(startIndex, count, filter, searchType);
        this.syncScimIamUsers(allScimIamUsers);
    }


    @Override
    public List<ScimIamUser> getAllScimIamUsers() {
        return this.getAllScimIamUsers(batchScimIamProperties.getStartIndex(), batchScimIamProperties.getCount(), batchScimIamProperties.getFilter(), batchScimIamProperties.getSearchType());
    }


    @Override
    public List<ScimIamUser> getAllScimIamUsers(int startIndex, int count, String filter, String searchType) {
        //api中的用户
        List<ScimIamUser> allScimIamUsers = new ArrayList<>();
        try {
            //获取总数
            int totalResults = getScimIamUsersTotal(startIndex, count, filter, searchType);
            int batch = totalResults % count == 0 ? totalResults / count : (totalResults / count) + 1;
            for (int i = 1; i <= batch; i++) {
                ScimIamUsers scimIamUsers = this.scimIamGetUsersApi(startIndex, count, filter, searchType);
                startIndex = (i * count) + 1;
                if (JudgeUtils.isNotEmpty(scimIamUsers.getResources())) {
                    allScimIamUsers.addAll(scimIamUsers.getResources());
                }
            }
        } catch (Exception e) {
            log.warn("getAllScimIamUsers Exception: {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.FAIL);
        }
        return allScimIamUsers;
    }

    @Override
    public ScimIamUsers scimIamGetUsersApi(int startIndex, int count, String filter, String searchType) throws Exception {
        BatchScimIamProperties.Server server = batchScimIamProperties.getServer();
        String scimIamGetUserApi = server.getAddress().concat(server.getUserApi());
        scimIamGetUserApi = addUrlParams(startIndex, count, filter, searchType, scimIamGetUserApi);
        log.info("scimIamGetUsersApi: {}", scimIamGetUserApi);
        ResponseEntity<ScimIamUsers> exchange = restTemplate.exchange(scimIamGetUserApi, HttpMethod.GET, new HttpEntity<>(null, getScimIamHeaders()), ScimIamUsers.class);
        return exchange.getBody();
    }

    @Override
    public void syncAllScimIamOrganizations() {
        List<ScimIamOrganization> allScimIamOrganizations = this.getAllScimIamOrganizations();
        this.syncScimIamOrganizations(allScimIamOrganizations);
    }

    @Override
    public void syncAllScimIamOrganizations(int startIndex, int count, String filter, String searchType) {
        List<ScimIamOrganization> allScimIamOrganizations = this.getAllScimIamOrganizations(startIndex, count, filter, searchType);
        this.syncScimIamOrganizations(allScimIamOrganizations);
    }

    @Override
    public List<ScimIamOrganization> getAllScimIamOrganizations() {
        return this.getAllScimIamOrganizations(batchScimIamProperties.getStartIndex(), batchScimIamProperties.getCount(), batchScimIamProperties.getFilter(), batchScimIamProperties.getSearchType());
    }

    @Override
    public List<ScimIamOrganization> getAllScimIamOrganizations(int startIndex, int count, String filter, String searchType) {
        //api中的用户
        List<ScimIamOrganization> allScimIamOrganizations = new ArrayList<>();
        try {
            //获取总数
            int totalResults = getScimIamOrganizationsTotal(startIndex, count, filter, searchType);
            int batch = totalResults % count == 0 ? totalResults / count : (totalResults / count) + 1;
            for (int i = 1; i <= batch; i++) {
                ScimIamOrganizations scimIamOrganizations = this.scimIamGetOrganizationsApi(startIndex, count, filter, searchType);
                startIndex = (i * count) + 1;
                if (JudgeUtils.isNotEmpty(scimIamOrganizations.getResources())) {
                    allScimIamOrganizations.addAll(scimIamOrganizations.getResources());
                }
            }
        } catch (Exception e) {
            log.warn("getAllScimIamOrganizations Exception: {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.FAIL);
        }
        return allScimIamOrganizations;
    }

    @Override
    public ScimIamOrganizations scimIamGetOrganizationsApi(int startIndex, int count, String filter, String searchType) throws Exception {
        BatchScimIamProperties.Server server = batchScimIamProperties.getServer();
        String scimIamGetOrganizationApi = server.getAddress().concat(server.getOrganizationApi());
        scimIamGetOrganizationApi = addUrlParams(startIndex, count, filter, searchType, scimIamGetOrganizationApi);
        ResponseEntity<ScimIamOrganizations> exchange = restTemplate.exchange(scimIamGetOrganizationApi, HttpMethod.GET, new HttpEntity<>(null, getScimIamHeaders()), ScimIamOrganizations.class);
        return exchange.getBody();
    }

    private int getScimIamUsersTotal(int startIndex, int count, String filter, String searchType) {
        int totalResults = 0;
        try {
            ScimIamUsers scimIamUsers = this.scimIamGetUsersApi(startIndex, count, filter, searchType);
            totalResults = scimIamUsers.getTotalResults();
        } catch (Exception e) {
            log.error("getScimIamUsersTotal Exception: {}", e.getMessage());
        }
        return totalResults;
    }

    public void syncScimIamUsers(List<ScimIamUser> allScimIamUsers) {
        //有可能是暂时异常了，不能禁用所有用户
        if (JudgeUtils.isEmpty(allScimIamUsers)) {
            return;
        }
        //全量数据库中存在的用户
        List<UserBO> users = systemUserService.getAllUserMobiles();
        List<String> allUserMobiles = users.stream().map(UserBO::getMobile).collect(Collectors.toList());
        Map<String, String> userMap = new HashMap<>();
        users.stream().forEach(userVO -> {
            userMap.put(userVO.getMobile(), userVO.getStatus());
        });
        //全量新4A系统中的用户
        List<String> allScimIamMobiles = new ArrayList<>();
        int current = 0;
        int count = allScimIamUsers.size();
        for (ScimIamUser scimIamUser : allScimIamUsers) {
            current++;
            log.info("syncScimIamUser count {},current {}", count, current);
            List<ScimIamUserPhone> scimIamUserPhones = scimIamUser.getPhoneNumbers();
            if (JudgeUtils.isEmpty(scimIamUserPhones)) {
                continue;
            }
            try {
                //同步API中的用户，并返回手机号码
                this.syncScimIamUser(scimIamUser, allUserMobiles, allScimIamMobiles, userMap);
            } catch (Exception exception) {
                log.warn("syncScimIamUser Exception: {},ScimIamUser:{}", exception.getMessage(), scimIamUser.toString());
            }
        }
        syncAllUserMobiles(allScimIamMobiles, allUserMobiles, userMap);
    }

    public void syncAllUserMobiles(List<String> allScimIamMobiles, List<String> allUserMobiles, Map<String, String> userMap) {
        if (JudgeUtils.isEmpty(allScimIamMobiles) || JudgeUtils.isEmpty(allUserMobiles)) {
            return;
        }
        int size = allUserMobiles.size();
        int now = 0;
        for (String mobile : allUserMobiles) {
            now++;
            log.info("syncScimIamUsers delete or disable user count {},current {}", size, now);
            try {
                //只有开始禁用模式时，才对已经禁用的账户不进行禁用操作，删除模式时，每次都去删除比对。
                if (!batchScimIamProperties.isSyncDelete()) {
                    //已经禁用的不重复禁用
                    if (JudgeUtils.equalsIgnoreCase(StatusConstans.DISABLE.getValue(), userMap.get(mobile))) {
                        continue;
                    }
                }
                //数据库中的不存在api中，置为禁用状态
                if (!allScimIamMobiles.contains(mobile)) {
                    if (batchScimIamProperties.isSyncDelete()) {
                        log.warn("syncScimIamUsers delete mobile: {}", mobile);
                        systemUserService.deleteUserByMobile(mobile);
                    } else {
                        log.warn("syncScimIamUsers disable mobile: {}", mobile);
                        UserBO user = new UserBO();
                        user.setStatus(StatusConstans.DISABLE.getValue());
                        user.setMobile(mobile);
                        user.setCreateUserId("task");
                        systemUserService.updateUserByMobile(user);
                    }
                }
            } catch (Exception exception) {
                log.warn("syncScimIamUsers delete Exception: {}, count {},current {},mobile: {}", exception.getMessage(), size, now, mobile);
            }

        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void syncScimIamUser(ScimIamUser scimIamUser, List<String> allUserMobiles, List<String> allScimIamMobiles, Map<String, String> userMap) {
        //4A状态禁用，直接返回
        String mobile = null;
        List<ScimIamUserPhone> scimIamUserPhones = scimIamUser.getPhoneNumbers();
        if (JudgeUtils.isEmpty(scimIamUserPhones)) {
            log.warn("syncScimIamUser fail mobile is null, userInfo: {}", scimIamUser.toString());
            return;
        }
        for (ScimIamUserPhone scimIamUserPhone : scimIamUserPhones) {
            if (!scimIamUserPhone.isPrimary()) {
                continue;
            }
            if (JudgeUtils.isBlank(scimIamUserPhone.getValue())) {
                continue;
            }
            mobile = scimIamUserPhone.getValue().replace("+86-", "");
            allScimIamMobiles.add(mobile);
        }
        if (JudgeUtils.isBlank(mobile)) {
            log.warn("syncScimIamUser fail mobile is null, userInfo: {}", scimIamUser.toString());
            return;
        }

        String email = null;
        List<ScimIamUserEmail> scimIamUserEmails = scimIamUser.getEmails();
        if (JudgeUtils.isNotEmpty(scimIamUserEmails)) {
            for (ScimIamUserEmail scimIamUserEmail : scimIamUserEmails) {
                if (!scimIamUserEmail.isPrimary()) {
                    continue;
                }
                email = scimIamUserEmail.getValue();
            }
        }
        String deptId = null;
        List<ScimIamUserSubject> scimSubjectAttrs = scimIamUser.getSubjectAttr();
        if (JudgeUtils.isNotEmpty(scimSubjectAttrs)) {
            for (ScimIamUserSubject scimIamUserSubject : scimSubjectAttrs) {
                if (!scimIamUserSubject.isMajorSubject()) {
                    continue;
                }
                deptId = scimIamUserSubject.getOrganization();
            }
        }
        String status = scimIamUser.isActive() ? StatusConstans.ENABLE.getValue() : StatusConstans.DISABLE.getValue();
        String scimUserName = scimIamUser.getUserName();
        String displayName = scimIamUser.getDisplayName();
        //数据库中有,更新
        if (allUserMobiles.contains(mobile)) {
            log.info("user isExist update now mobile:{},fullName:{},fullName:{},scimUserName:{}", mobile, displayName, scimUserName);
            UserBO user = new UserBO();
            // 超过授权时间
            // if (JudgeUtils.equalsIgnoreCase(StatusConstans.DISABLE.getValue(), userMap.get(mobile))) {
            //     user.setExpireTime(LocalDateTime.now());
            //     log.warn("user isDisable not update status,mobile:{},fullName:{},scimUserName:{}", mobile, displayName, scimUserName);
            // } else {
            //     user.setStatus(status);
            // }
            user.setScimUserId(scimIamUser.getId());
            user.setScimUserName(scimUserName);
            user.setFullName(displayName);
            user.setEmail(email);
            user.setMobile(mobile);
            user.setDeptId(batchScimIamProperties.isEnabledSyncUserOrganization() ? deptId : null);
            if (JudgeUtils.isNotNull(scimIamUser.getMeta())) {
                ScimIamUserMeta meta = scimIamUser.getMeta();
                user.setCreateTime(JudgeUtils.isNotNull(meta.getCreated()) ? meta.getCreated() : null);
            }
            //执行更新操作
            try {
                systemUserService.updateUserByMobile(user);
            } catch (Exception exception) {
                log.warn("update sys_user  Exception: {},user: {}", exception.getMessage(), user.toString());
            }
        } else {
            if (batchScimIamProperties.isOnlyUpdate()) {
                log.info("syncScimIamUser enable OnlyUpdate {} not insert", mobile);
            } else {
                UserBO user = new UserBO();
                user.setStatus(status);
                user.setUserName(JudgeUtils.isNotBlank(scimIamUser.getMajorAccount()) ? scimIamUser.getMajorAccount() : scimIamUser.getUserName());
                user.setScimUserName(scimUserName);
                user.setScimUserId(scimIamUser.getId());
                user.setFullName(displayName);
                user.setEmail(email);
                user.setMobile(mobile);
                user.setDeptId(batchScimIamProperties.isEnabledSyncUserOrganization() ? deptId : batchScimIamProperties.getDefaultDeptId());
                user.setDutyId(batchScimIamProperties.getDefaultDutyId());
                // user.setExpireTime(status.equals(StatusConstans.DISABLE.getValue()) ? LocalDateTime.now() : null);
                LocalDateTime localDateTime = LocalDateTime.now();
                if (JudgeUtils.isNotNull(scimIamUser.getMeta())) {
                    ScimIamUserMeta meta = scimIamUser.getMeta();
                    user.setCreateTime(JudgeUtils.isNotNull(meta.getCreated()) ? meta.getCreated() : localDateTime);
                } else {
                    user.setCreateTime(localDateTime);
                }
                user.setHasRole(StatusConstans.NOT_HAS_ROLE.getValue());
                //生成随机密码
                try {
                    user.setPassword(PasswordUtil.createPassWord(RandomUtil.getCharacterAndNumber(16), systemAccessTokenService.getAesKey()));
                    systemUserService.batchAdd(user);
                } catch (Exception exception) {
                    log.warn("insert sys_user  Exception: {},user: {}", exception.getMessage(), user.toString());
                }
            }
        }
    }

    private int getScimIamOrganizationsTotal(int startIndex, int count, String filter, String searchType) {
        int totalResults = 0;
        try {
            ScimIamOrganizations scimIamOrganizations = this.scimIamGetOrganizationsApi(startIndex, count, filter, searchType);
            totalResults = scimIamOrganizations.getTotalResults();
        } catch (Exception e) {
            log.error("getScimIamOrganizationsTotal Exception: {}", e.getMessage());
        }
        return totalResults;
    }

    public void syncScimIamOrganizations(List<ScimIamOrganization> scimIamOrganizations) {
        //有可能是暂时异常了，不能禁用所有组织
        if (JudgeUtils.isEmpty(scimIamOrganizations)) {
            return;
        }
        //全量数据库中存在的组织
        List<String> allDeptIds = systemDepartmentService.getAllDeptIds();
        //全量新4A系统中的组织
        List<String> allScimIamDeptIds = new ArrayList<>();
        int current = 0;
        int count = scimIamOrganizations.size();
        for (ScimIamOrganization scimIamOrganization : scimIamOrganizations) {
            current++;
            log.info("syncScimIamOrganization count {},current {}", count, current);
            this.syncScimIamOrganization(scimIamOrganization, allDeptIds, allScimIamDeptIds);
        }
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void syncScimIamOrganization(ScimIamOrganization scimIamOrganization, List<String> allDeptIds, List<String> allScimIamDeptIds) {
        String operatorId = "task";
        DeptBO deptVO = new DeptBO();
        deptVO.setDeptId(scimIamOrganization.getId());
        deptVO.setDeptName(scimIamOrganization.getDisplayName());
        deptVO.setParentId(JudgeUtils.equalsIgnoreCase(scimIamOrganization.getCode(), "ROOT") ? "0" : scimIamOrganization.getParent());
        deptVO.setOrderNum(scimIamOrganization.getOrder());
        deptVO.setCreateUserID(operatorId);
        LocalDateTime localDateTime = LocalDateTime.now();
        deptVO.setModifyTime(localDateTime);
        if (allDeptIds.contains(scimIamOrganization.getId())) {
            if (JudgeUtils.isNotNull(scimIamOrganization.getMeta())) {
                ScimIamOrganizationMeta meta = scimIamOrganization.getMeta();
                deptVO.setCreateTime(JudgeUtils.isNotNull(meta.getCreated()) ? meta.getCreated() : null);
            }
            try {
                systemDepartmentService.update(operatorId, deptVO);
            } catch (Exception exception) {
                log.warn("update sys_dept Exception: {},dept: {}", exception.getMessage(), deptVO);
            }

        } else {
            if (JudgeUtils.isNotNull(scimIamOrganization.getMeta())) {
                ScimIamOrganizationMeta meta = scimIamOrganization.getMeta();
                deptVO.setCreateTime(JudgeUtils.isNotNull(meta.getCreated()) ? meta.getCreated() : localDateTime);
            }
            deptVO.setStatus(StatusConstans.ENABLE.getValue());
            try {
                systemDepartmentService.add(operatorId, deptVO);
            } catch (Exception exception) {
                log.warn("insert sys_dept Exception: {},dept: {}", exception.getMessage(), deptVO);
            }

        }
    }

}
