package com.cmpay.hacp.extend.sso.ticket.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;


@Data
@ConfigurationProperties(prefix = "hacp.web.admin.freeze")
public class FreezeAccountProperties {

    /**
     * 是否启用定时任务，默认false
     */
    private boolean enabled;

    /**
     * 多长时间未使用或登录开启禁用（单位小时）,默认2160小时/90天
     */
    private Integer freezeTimeout = 90 * 24;

    /**
     * 账户禁用通知系统名称
     */
    private String personal = "运营管理系统";

    /**
     * 邮箱通知主题，或短信下发渠道
     */
    private String subject = "账户冻结通知";

    /**
     * 邮箱通知模板，或短信模板
     */
    private String template = "freeze-account.html";

    /**
     * 是否开启账户禁用前消息通知,默认false，不通知
     */
    private boolean notice;

    /**
     * 通知方式，邮箱通知email,短信通知sms.未配置默认为none不通知
     */
    private NoticeType noticeType = NoticeType.none;

    /**
     * 开启通知的环境，默认prd，其余环境需要配置白名单才可以发送通知。
     */
    private String noticeActive = "prd";

    /**
     * 测试或预投产环境的白名单，否则不会发送通知，避免非生产环境打扰用户。
     */
    private List<String> noticeWhite;

    /**
     * 账户禁用前多长时间发送通知，默认10天，单位小时
     */
    private int noticeInitialize = 10 * 24;

    /**
     * 是否每次都发送通知，默认false，否则按照noticeDay计算天数发送通知。
     */
    private boolean noticeAny;

    /**
     * 在第几天发送通知，默认第10, 8, 6, 4, 2天会发送通知(计算单位为小时，一天24小时，取余往前进1天)
     */
    private String noticeDays = "10,8,6,4,2";

    /**
     * 通知类型枚举
     */
    public enum NoticeType {
        /**
         * 默认
         */
        none,
        /**
         * 邮件通知
         */
        email,
        /**
         * 短信通知
         */
        sms;
    }
}
