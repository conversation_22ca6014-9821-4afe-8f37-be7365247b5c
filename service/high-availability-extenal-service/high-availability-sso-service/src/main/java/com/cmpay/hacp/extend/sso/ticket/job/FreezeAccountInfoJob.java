package com.cmpay.hacp.extend.sso.ticket.job;

import com.cmpay.hacp.extend.sso.ticket.service.FreezeAccountService;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 *
 */
@Slf4j
@EnableScheduling
public class FreezeAccountInfoJob {

    private final FreezeAccountService freezeAccountService;

    public FreezeAccountInfoJob(FreezeAccountService freezeAccountService) {
        this.freezeAccountService = freezeAccountService;
    }

    /**
     * 定时任务-删除冻结状态账号【每日凌晨3点执行一次】
     * 0 0 3 * * ?
     */
    @Scheduled(cron = "${lemon.web.admin.freeze.cron:0 0 3 * * ?}")
    @InitialLemonData
    public void run() {
        log.info("删除冻结状态账号定时任务启动");
        this.freezeAccountService.disable();
    }
}
