package com.cmpay.hacp.extend.sso.ticket.service.impl;

import com.cmpay.hacp.extend.sso.ticket.service.FreezeAccountNoticeService;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * 系统日志通知服务实现
 * <AUTHOR>
 */
@Slf4j
public class FreezeAccountNoneNoticeServiceImpl implements FreezeAccountNoticeService {
    @Override
    public void sendNotice(String sender, String subject, String[] recipients, String templateName, Object templateParams)  {
        log.info("sendNotice  sender {}, subject {}, recipients {}, templateName {},templateParams {}", sender, subject, Arrays.toString(recipients), templateName, templateParams.toString());
    }
}
