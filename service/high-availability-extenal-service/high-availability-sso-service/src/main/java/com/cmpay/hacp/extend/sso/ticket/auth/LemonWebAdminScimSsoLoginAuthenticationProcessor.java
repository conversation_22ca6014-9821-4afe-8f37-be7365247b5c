package com.cmpay.hacp.extend.sso.ticket.auth;

import com.cmpay.hacp.system.auth.HacpWebAdminAuthenticationSuccessHandler;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.extend.sso.ticket.enums.SsoConstant;
import com.cmpay.hacp.extend.sso.ticket.service.SystemSsoLoginService;
import com.cmpay.hacp.system.utils.BeanConvertUtil;
import com.cmpay.hacp.dto.system.LoginHistoryLogDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import com.cmpay.lemon.common.codec.CodecException;
import com.cmpay.lemon.common.codec.ObjectDecoder;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.exception.LemonException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.SimpleUserInfo;
import com.cmpay.lemon.framework.security.UserInfoBase;
import com.cmpay.lemon.framework.security.auth.AbstractGenericMatchableAuthenticationProcessor;
import com.cmpay.lemon.framework.security.auth.AuthenticationRequest;
import com.cmpay.lemon.framework.security.auth.GenericAuthenticationToken;
import com.cmpay.lemon.framework.utils.WebUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.security.core.AuthenticationException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @author: lihuiquan
 */
public class LemonWebAdminScimSsoLoginAuthenticationProcessor extends AbstractGenericMatchableAuthenticationProcessor<GenericAuthenticationToken> {

    public static final String TICKET = "ticket";

    public static final String SERVICE = "service";

    private final ObjectDecoder objectDecoder;

    private final SystemSsoLoginService systemSsoLoginService;

    private final ObjectMapper lemonWebAdminObjectMapper;

    /**
     * "filterProcessesUrl"前缀必须与"lemon.security.authentication.loginPathPrefix"一致
     */
    public LemonWebAdminScimSsoLoginAuthenticationProcessor(SystemSsoLoginService systemSsoLoginService, String filterProcessesUrl, ObjectDecoder objectDecoder, ObjectMapper lemonWebAdminObjectMapper) {
        super(filterProcessesUrl);
        this.systemSsoLoginService = systemSsoLoginService;
        this.objectDecoder = objectDecoder;
        this.lemonWebAdminObjectMapper = lemonWebAdminObjectMapper;
    }

    @Override
    protected UserInfoBase doProcessAuthentication(GenericAuthenticationToken genericAuthenticationToken) throws AuthenticationException {
        AuthenticationRequest authenticationRequest = genericAuthenticationToken.getAuthenticationRequest();
        Map<String, String> authenticationRequestParameters = new HashMap<>();
        try {
            HttpServletRequest httpServletRequest = authenticationRequest.getHttpServletRequest();
            String queryString = httpServletRequest.getQueryString();
            if (JudgeUtils.isBlank(queryString)) {
                authenticationRequestParameters = this.objectDecoder.readValue(AbstractGenericMatchableAuthenticationProcessor.getRequestInputStream(authenticationRequest), Map.class);
            } else {
                this.resolveQueryString(queryString, authenticationRequestParameters);
            }
        } catch (CodecException e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e);
        }
        if (JudgeUtils.isEmpty(authenticationRequestParameters)) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, "No authentication parameter found in request body.");
        }
        //获取登录参数
        String ticket = authenticationRequestParameters.get(TICKET);
        String domain = authenticationRequestParameters.get(SERVICE);
        UserLoginBO userLoginBO = systemSsoLoginService.ssoScimLogin(ticket, domain);
        UserLoginRspDTO userInfoDTO = BeanConvertUtil.getUserLoginRspDTO(userLoginBO);
        //session 暂时存入
        HttpSession httpSession = WebUtils.getHttpServletRequest().getSession();
        try {
            String userInfoJson = this.lemonWebAdminObjectMapper.writeValueAsString(userInfoDTO);
            httpSession.setAttribute(HacpWebAdminAuthenticationSuccessHandler.LOGIN_INFO, userInfoJson);
            httpSession.setAttribute(SsoConstant.SCIM_USER_NAME,
                    Optional.ofNullable(userInfoDTO.getLoginHistory()).map(LoginHistoryLogDTO::getName).orElse(null));
        } catch (Exception e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e.getMessage());
        }

        return new SimpleUserInfo(userInfoDTO.getUserInfo().getUpmsUserId(), userInfoDTO.getUserInfo().getUserName(), userInfoDTO.getUserInfo().getMobile());
    }

    private void resolveQueryString(String query, Map<String, String> params) {
        String[] arrays = org.apache.commons.lang3.StringUtils.split(query, '&');
        for (int i = 0, n = arrays.length; i < n; i++) {
            String[] kv = org.apache.commons.lang3.StringUtils.split(arrays[0], '=');
            if (kv.length == 2) {
                params.put(kv[0], kv[1]);
            }
        }
    }


}


