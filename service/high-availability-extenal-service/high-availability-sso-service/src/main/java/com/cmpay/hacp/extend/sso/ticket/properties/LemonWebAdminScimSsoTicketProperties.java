package com.cmpay.hacp.extend.sso.ticket.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties("hacp.web.admin.scim-iam")
public class LemonWebAdminScimSsoTicketProperties {
    /**
     * 单点登录返回报文格式
     */
    private String format = "JSON";
    /**
     * 是否启用新4A单点登录方式
     */
    private Boolean sso = true;
    /**
     * 服务器相关信息
     */
    private Server server = new Server();

    /**
     * 多域名时单点登录参数认证
     */
    private List<Service> services;

    /**
     * 单域名单点登录参数认证
     */
    private String service;

    @Data
    public static class Service {

        private String domain;

        private String notifyUrl;
    }

    @Data
    public class Server {
        /**
         * 测试 http://auth.4a.test:8002  ************ auth.4a.test
         * 生产 http://auth.4a.cmft       ************* auth.4a.cmft
         */
        private String address = "http://auth.4a.cmft";

        /**
         * 单点登录接口
         */
        private String casApi = "/auth/cas/p3/serviceValidate";

        /**
         * 连接超时时间
         */
        private int connectTimeout = 6000;

        /**
         * 读取超时时间
         */
        private int readTimeout = 6000;

    }

}
