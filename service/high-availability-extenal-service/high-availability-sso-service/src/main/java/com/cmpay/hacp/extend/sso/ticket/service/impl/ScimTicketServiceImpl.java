package com.cmpay.hacp.extend.sso.ticket.service.impl;

import com.cmpay.hacp.extend.sso.ticket.bo.TicketUserInfoBO;
import com.cmpay.hacp.extend.sso.ticket.bo.UserDetailBO;
import com.cmpay.hacp.extend.sso.ticket.enums.MessageCodeEnum;
import com.cmpay.hacp.extend.sso.ticket.properties.LemonWebAdminScimSsoTicketProperties;
import com.cmpay.hacp.extend.sso.ticket.service.ScimTicketService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ScimTicketServiceImpl implements ScimTicketService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScimTicketServiceImpl.class);

    private static final String REG_EXP = "\r|\n";

    private static final String REPLACEMENT = "";

    private final LemonWebAdminScimSsoTicketProperties lemonWebAdminScimSsoTicketProperties;

    private final RestTemplate scimIamRestTemplate;

    public ScimTicketServiceImpl(RestTemplate scimIamRestTemplate,LemonWebAdminScimSsoTicketProperties lemonWebAdminScimSsoTicketProperties) {
        this.scimIamRestTemplate = scimIamRestTemplate;
        this.lemonWebAdminScimSsoTicketProperties = lemonWebAdminScimSsoTicketProperties;
    }

    @Override
    public TicketUserInfoBO serviceValidate(String ticket, String domain) {
        String service = getService(domain);
        if (JudgeUtils.isBlank(service)) {
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        LOGGER.info("serviceValidate service {}", service);
        String ssoServer = getSsoServer();
        if (JudgeUtils.isBlank(ssoServer)) {
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        LOGGER.info("serviceValidate ssoServer {}", ssoServer);
        if (JudgeUtils.isBlank(ticket)) {
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        LOGGER.info("serviceValidate ticket {}", ticket);
        return this.getServiceValidate(ssoServer, ticket, service);
    }

    private String getSsoServer() {
        LemonWebAdminScimSsoTicketProperties.Server server = lemonWebAdminScimSsoTicketProperties.getServer();
        return server.getAddress().concat(server.getCasApi());
    }

    private String getService(String domain) {
        if (JudgeUtils.isBlank(domain)) {
            return lemonWebAdminScimSsoTicketProperties.getService();
        }
        List<LemonWebAdminScimSsoTicketProperties.Service> services = lemonWebAdminScimSsoTicketProperties.getServices();
        if (JudgeUtils.isEmpty(services)) {
            return lemonWebAdminScimSsoTicketProperties.getService();
        }
        List<LemonWebAdminScimSsoTicketProperties.Service> serviceList = services.stream().filter(serviceInfo -> JudgeUtils.equalsIgnoreCase(serviceInfo.getNotifyUrl(), domain)).collect(Collectors.toList());
        if (JudgeUtils.isEmpty(serviceList)) {
            return lemonWebAdminScimSsoTicketProperties.getService();
        }
        LemonWebAdminScimSsoTicketProperties.Service service = serviceList.get(0);
        return service.getNotifyUrl();
    }

    @Override
    public TicketUserInfoBO getServiceValidate(String ssoServer, String ticket, String service) {
        String serviceValidateUrl = ssoServer.concat("?service=").concat(service).concat("&format=").concat(lemonWebAdminScimSsoTicketProperties.getFormat()).concat("&ticket=").concat(ticket);
        LOGGER.info("serviceValidateUrl: {}", serviceValidateUrl);
        String response = scimIamRestTemplate.getForObject(serviceValidateUrl, String.class);
        LOGGER.info("response: {}", response);
        response = response.replaceAll(REG_EXP, REPLACEMENT);
        LOGGER.info("replace response: {}", response);
        return this.getTicketUserInfo(response);
    }

    private TicketUserInfoBO getTicketUserInfo(String response) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        TicketUserInfoBO ticketUserInfo = null;
        try {
            ticketUserInfo = objectMapper.readValue(response, TicketUserInfoBO.class);
        } catch (JsonProcessingException e) {
            LOGGER.info("getTicketUserInfo error {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isNull(ticketUserInfo.getServiceResponse().getAuthenticationSuccess())) {
            LOGGER.info("getAuthenticationSuccess is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isNull(ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes())) {
            LOGGER.info("getAttributes is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isEmpty(ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes().getUserDetail())) {
            LOGGER.info("getUserDetail is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        List<String> userDetail = ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes().getUserDetail();
        String userDetailStr = userDetail.get(0);
        UserDetailBO userDetailInfo = null;
        try {
            userDetailInfo = objectMapper.readValue(userDetailStr, UserDetailBO.class);
        } catch (JsonProcessingException e) {
            LOGGER.info("getUserDetail error {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        ticketUserInfo.setUserDetail(userDetailInfo);
        return ticketUserInfo;
    }


}
