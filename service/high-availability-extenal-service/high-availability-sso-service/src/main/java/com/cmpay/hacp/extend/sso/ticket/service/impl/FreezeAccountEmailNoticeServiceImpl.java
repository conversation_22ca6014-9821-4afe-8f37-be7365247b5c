package com.cmpay.hacp.extend.sso.ticket.service.impl;

/**
 * 系统邮件通知服务
 * <AUTHOR>
 */
// @Slf4j
// public class FreezeAccountEmailNoticeServiceImpl implements FreezeAccountNoticeService {
//     private final MessageSendService systemEmailService;
//
//     public FreezeAccountEmailNoticeServiceImpl(MessageSendService systemEmailService) {
//         this.systemEmailService = systemEmailService;
//     }
//
//     @Override
//     public void sendNotice(String sender, String subject, String[] recipients, String templateName, Object templateParams) throws Exception {
//         log.info("sendEmail  sender {}, subject {}, recipients {}, templateName {}, templateParams {}", sender, subject, Arrays.toString(recipients), templateName, templateParams.toString());
//         BaseMessageBO message = new BaseMessageBO();
//         systemEmailService.sendMessage(message);
//     }
// }
