package com.cmpay.hacp.extend.sso.ticket.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScimIamUser {
    /**
     * 自然人id
     */
    private String id;
    /**
     * 自然人用户名
     */
    private String userName;
    /**
     * 自然人显示名
     */
    private String displayName;
    /**
     * 身份状态，对应字段status，true:启用，false:冻结。
     */
    private boolean active;
    /**
     * 用户真实姓名
     */
    private String realName;
    /**
     * 密码最后修改时间
     */
    private String passwordLastModifiedTime;
    /**
     * 绑定和包用户id
     */
    private String bindHeBaoAccount;
    /**
     * 是否实名
     */
    private String identified;
    /**
     * 4A主账号，先用老4A数据保存，后续静默迁移
     */
    @JsonProperty(value = "4AMajorAccount")
    private String majorAccount;
    /**
     * 自然人用户邮箱
     */
    private List<ScimIamUserEmail> emails;
    /**
     * 用户身份额外属性的集合
     */
    private List<ScimIamUserSubject> subjectAttr;
    /**
     * 自然人用户电话
     */
    private List<ScimIamUserPhone> phoneNumbers;
    /**
     * scim的标准，用户的元数据。
     */
    private ScimIamUserMeta meta;
    /**
     * scim的schema，返回字符串数组固定值"urn:ietf:params:scim:schemas:core:2.0:Person"。
     */
    private List<String> schemas;

}
