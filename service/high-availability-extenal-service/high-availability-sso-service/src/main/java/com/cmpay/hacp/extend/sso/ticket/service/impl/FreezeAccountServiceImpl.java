package com.cmpay.hacp.extend.sso.ticket.service.impl;

import com.cmpay.hacp.extend.sso.ticket.bo.FreezeAccountNoticeBO;
import com.cmpay.hacp.system.bo.system.LoginLatestInfoBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.extend.sso.ticket.properties.FreezeAccountProperties;
import com.cmpay.hacp.extend.sso.ticket.service.FreezeAccountNoticeService;
import com.cmpay.hacp.extend.sso.ticket.service.FreezeAccountService;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemLoginLatestInfoService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 定时删除冻结状态数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FreezeAccountServiceImpl implements FreezeAccountService {

    private final static String FREEZE_ACCOUNT_LOCK = "LEMON_WEB_ADMIN:FREEZE_ACCOUNT_LOCK";

    private final static String DATE_PATTERN = "yyyy年MM月dd日";

    public static final int MAX_HOURS_DAY = 24;

    @Value("${spring.application.name}")
    private String application;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired(required = false)
    private FreezeAccountProperties freezeAccountProperties;

    @Autowired(required = false)
    private FreezeAccountNoticeService freezeAccountNoticeService;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private SystemCacheService systemCacheService;

    @Autowired
    private SystemLoginLatestInfoService systemLoginLatestInfoService;

    @Override
    public void disable() {
        try {
            String currentDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
            //分布式锁，如果没有这个key没有值,就给这个key设置值,并返回true
            if (systemCacheService.setIfAbsent(FREEZE_ACCOUNT_LOCK, currentDateTimeStr, 1, TimeUnit.HOURS)) {
                UserBO userVO = new UserBO();
                userVO.setHasRole(StatusConstans.HAS_ROLE.getValue());
                List<UserBO> allUsers = systemUserService.getUsersByCondition(userVO);
                List<String> userIds = allUsers.stream().map(UserBO::getUserId).collect(Collectors.toList());
                //查询sys_login_latest_info表中的数据,根据配置的freezeTime判断用户是否长期不登录
                List<LoginLatestInfoBO> loginHistoryLogs = systemLoginLatestInfoService.queryLoginLastInfoByUserIds(userIds);
                if (JudgeUtils.isEmpty(loginHistoryLogs)) {
                    return;
                }
                //筛选登陆过但长期未登录的账号
                List<String> freezeUserIds = new ArrayList<>();
                for (LoginLatestInfoBO loginHistoryLog : loginHistoryLogs) {
                    LocalDateTime latestTime = null;
                    if (JudgeUtils.isNotNull(loginHistoryLog.getLatestTime())) {
                        latestTime = loginHistoryLog.getLatestTime();
                    } else if (JudgeUtils.isNotNull(loginHistoryLog.getFirstTime())) {
                        latestTime = loginHistoryLog.getFirstTime();
                    }
                    if (JudgeUtils.isNull(latestTime)) {
                        continue;
                    }
                    LocalDateTime nowTime = LocalDateTime.now();
                    //过期日期 = 登录日期 + 过期时间
                    LocalDateTime expireTime = latestTime.plusHours(freezeAccountProperties.getFreezeTimeout());
                    //过期日期 < 当前日期
                    if (expireTime.isBefore(nowTime)) {
                        //已过期
                        freezeUserIds.add(loginHistoryLog.getUserId());
                    } else {
                        //待过期
                        waitExpireAccount(loginHistoryLog.getUserId(), latestTime, expireTime, nowTime);
                    }
                }
                log.info("删除冻结状态账号,删除变更的userIds:{}", freezeUserIds);
                //修改has_role
                freezeUserIds.forEach(userId -> {
                    UserBO user = new UserBO();
                    user.setUserId(userId);
                    // user.setHasRole(StatusConstans.NOT_HAS_ROLE.getValue());
                    user.setStatus(StatusConstans.DISABLE.getValue());
                    systemUserService.batchUpdate(user);
                    //删除sys_user_role表中的数据
                    // systemUserService.deleteUserRole(userId);
                });
            }
        } catch (Exception e) {
            log.error("删除冻结状态账号定时任务异常", e);
        } finally {
            log.info("delete deleteFreezeAccount lock");
            systemCacheService.delete(FREEZE_ACCOUNT_LOCK);
        }

    }

    public void waitExpireAccount(String userId, LocalDateTime latestTime, LocalDateTime expireTime, LocalDateTime nowTime) {
        if (!freezeAccountProperties.isNotice()) {
            return;
        }
        // 计算两个日期时间之间的间隔(过期日期-当前日期)
        // 将持续时间转换为小时数（duration.toHours()会向下取整）
        long hoursBetween = Duration.between(nowTime, expireTime).abs().toHours();
        //提前10天发送过期通知(过期日期-当前日期<=10)
        if (hoursBetween <= freezeAccountProperties.getNoticeInitialize()) {
            //计算未登录天数
            long noLoginDay = Duration.between(latestTime, nowTime).abs().toDays();
            UserBO userInfo = systemUserService.getUserInfo(userId);
            String address = isSmsNoticeType() ? userInfo.getMobile() : userInfo.getEmail();
            //计算相差天数
            long day = hoursBetween % MAX_HOURS_DAY == 0 ? hoursBetween / MAX_HOURS_DAY : (hoursBetween / MAX_HOURS_DAY) + 1;
            //每天都发通知
            if (freezeAccountProperties.isNoticeAny()) {
                this.sendDisableNotice(address, userInfo.getFullName(), noLoginDay, expireTime, nowTime);
            } else {
                List<String> noticeDays = Arrays.asList(freezeAccountProperties.getNoticeDays().split(","));
                if (noticeDays.contains(String.valueOf(day))) {
                    this.sendDisableNotice(address, userInfo.getFullName(), noLoginDay, expireTime, nowTime);
                }
            }
        }
    }

    private boolean isSmsNoticeType() {
        return freezeAccountProperties.getNoticeType().compareTo(FreezeAccountProperties.NoticeType.sms) == 0;
    }

    public void sendDisableNotice(String address, String fullName, long noLoginDay, LocalDateTime expireTime, LocalDateTime nowTime) {
        // 判断是否是生产环境
        if (freezeAccountProperties.getNoticeActive().indexOf(active) == -1) {
            // 判断是否是白名单用户
            if (!freezeAccountProperties.getNoticeWhite().contains(address)) {
                log.warn("current active is {},NoticeActive is {}, {} is not NoticeWhite", active, freezeAccountProperties.getNoticeActive(), address);
                return;
            }
        }
        String[] addresses = {address};
        FreezeAccountNoticeBO templateParams = new FreezeAccountNoticeBO();
        templateParams.setSystemName(freezeAccountProperties.getPersonal());
        templateParams.setFullName(fullName);
        templateParams.setNoLoginDay(noLoginDay);
        templateParams.setExpireTime(DateTimeUtils.formatLocalDateTime(expireTime, DATE_PATTERN));
        templateParams.setSendTime(DateTimeUtils.formatLocalDateTime(nowTime, DATE_PATTERN));
        templateParams.setApplication(application);
        templateParams.setActive(active);
        try {
            freezeAccountNoticeService.sendNotice(
                    isSmsNoticeType() ? LemonUtils.getRequestId() : freezeAccountProperties.getPersonal(),
                    freezeAccountProperties.getSubject(),
                    addresses,
                    freezeAccountProperties.getTemplate(),
                    templateParams);
        } catch (Exception e) {
            log.warn("sendDisableNotice Exception {},address {},fullName {}", e.getMessage(), address, fullName);
        }
    }
}
