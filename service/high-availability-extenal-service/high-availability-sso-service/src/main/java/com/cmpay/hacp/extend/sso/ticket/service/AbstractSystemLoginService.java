package com.cmpay.hacp.extend.sso.ticket.service;

import com.cmpay.hacp.bo.system.*;
import com.cmpay.hacp.extend.sso.ticket.bo.TicketUserInfoBO;
import com.cmpay.hacp.extend.sso.ticket.bo.UserDetailBO;
import com.cmpay.hacp.extend.sso.ticket.dto.LoginFrom4aReqDTO;
import com.cmpay.hacp.extend.sso.ticket.dto.LoginRspDTO;
import com.cmpay.hacp.extend.sso.ticket.enums.MessageCodeEnum;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.system.service.SystemDepartmentService;
import com.cmpay.hacp.system.service.SystemLoginHistoryLogService;
import com.cmpay.hacp.system.service.SystemLoginLatestInfoService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.utils.network.IpUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Primary
public class AbstractSystemLoginService implements SystemSsoLoginService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractSystemLoginService.class);

    @Autowired
    private SsoTicketService ssoTicketService;

    @Autowired
    private ScimTicketService scimTicketService;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private SystemLoginHistoryLogService systemLoginHistoryLogService;

    @Autowired
    private SystemLoginLatestInfoService systemLoginLatestInfoService;
    @Autowired
    private SystemDepartmentService systemDepartmentService;

    @Value("${spring.application.name}")
    private String applicationName;


    public UserLoginBO getUserLoginBO(LoginRspDTO loginRspDTO) {
        HttpServletRequest httpServletRequest = WebUtils.getHttpServletRequest();
        LoginHistoryLogBO historyLogBO = new LoginHistoryLogBO();
        SessionTokenVO sessionToken = loginRspDTO.getSessionTokenVO();
        String userId = sessionToken.getUpmsUserId();
        UserBO userInfo = systemUserService.getUserInfo(userId);
        if (JudgeUtils.isNull(userInfo)) {
            BusinessException.throwBusinessException(MessageCodeEnum.USERNAME_NOT_EXISTS);
        }
        if (JudgeUtils.isBlank(userInfo.getHasRole()) ||
                !JudgeUtils.equalsIgnoreCase(userInfo.getHasRole(), StatusConstans.HAS_ROLE.getValue())) {
            BusinessException.throwBusinessException(MessageCodeEnum.USER_NOT_HAS_ROLE);
        }
        //禁用用户不能登录
        if (JudgeUtils.equalsIgnoreCase(StatusConstans.DISABLE.getValue(), userInfo.getStatus())) {
            BusinessException.throwBusinessException(MessageCodeEnum.LOGIN_STATUS_CHECK_EXCEPTION);
        }
        //个人基本信息
        UserLoginBO userResultLoginBO = new UserLoginBO();
        userResultLoginBO.setUserInfo(loginRspDTO.getSessionTokenVO());
        userResultLoginBO.setPwdNeedToModify(loginRspDTO.getPwdNeedToModify());
        //查询上一次登录日期
        LoginHistoryLogBO loginHistoryLog = systemLoginLatestInfoService.queryLoginLatestInfo(userId);
        userResultLoginBO.setLoginHistory(loginHistoryLog);
        //上次登录时间
        if (JudgeUtils.isNotBlank(loginRspDTO.getLastLoginTime())) {
            //首先取权限中心的
            userResultLoginBO.setLastLoginTime(loginRspDTO.getLastLoginTime());
        }
        if (JudgeUtils.isBlank(userResultLoginBO.getLastLoginTime())) {
            //其次取当前系统
            if (JudgeUtils.isNotNull(loginHistoryLog)) {
                userResultLoginBO.setLastLoginTime(DateTimeUtils.formatLocalDateTime(loginHistoryLog.getLoginTime()));
            } else {
                //取当前时间
                userResultLoginBO.setLastLoginTime(DateTimeUtils.getCurrentDateTimeStr());
            }
        }

        // // 过期时间顺延3个月
        // LocalDateTime localExpireTime1 = LocalDateTime.now().plusDays(90);
        // LocalDateTime localExpireTime2 = Optional.ofNullable(userInfo.getExpireTime()).orElse(localExpireTime1);
        // // 原来的有效期比当前时间+90天还晚，那么取原来的，否则取当前时间+90
        // boolean after = localExpireTime2.isAfter(localExpireTime1);
        // userInfo.setExpireTime(after ? localExpireTime2 : localExpireTime1);
        // 更新登录时间
        userInfo.setLastLoginTime(DateTimeUtils.parseLocalDateTime(userResultLoginBO.getLastLoginTime()));
        systemUserService.updateDateTime(userInfo);

        //登记登录历史
        historyLogBO.setUserId(userId);
        historyLogBO.setUserName(sessionToken.getUserName());
        // 使用4A账号登记
        historyLogBO.setName(sessionToken.getScimUserName());
        // 不登记手机号
        // historyLogBO.setMobile(sessionToken.getMobile());
        historyLogBO.setLoginIp(IpUtil.getIpAddr(httpServletRequest));
        LocalDate localDate = DateTimeUtils.getCurrentLocalDate();
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        historyLogBO.setLoginTerminal(httpServletRequest.getHeader("user-agent"));
        historyLogBO.setLoginFrom(applicationName);
        historyLogBO.setLoginDate(localDate);
        historyLogBO.setLoginTime(localDateTime);
        historyLogBO.setRequestId(LemonUtils.getRequestId());
        systemLoginHistoryLogService.addLoginHistoryLog(historyLogBO);
        return userResultLoginBO;
    }


    @Override
    public UserLoginBO ssoLogin(String ssoServer, String ticket, String service) {
        TicketUserInfoBO ticketUserInfo = ssoTicketService.serviceValidate(ssoServer, ticket, service);
        if (JudgeUtils.isNull(ticketUserInfo.getUserDetail())) {
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        UserDetailBO userDetail = ticketUserInfo.getUserDetail();
        LOGGER.info("userDetail userExistLogin {}", userDetail.toString());
        LoginFrom4aReqDTO loginFrom = new LoginFrom4aReqDTO();
        loginFrom.setUserName(userDetail.getUsername());
        loginFrom.setFullName(userDetail.getName());
        loginFrom.setMobile(userDetail.getMobile());
        LoginRspDTO loginRspDTO = this.userExistLogin(loginFrom);
        if (JudgeUtils.isNotSuccess(loginRspDTO.getMsgCd())) {
            LOGGER.info("userExistLogin  fail {}", loginRspDTO.getMsgCd());
            BusinessException.throwBusinessException(loginRspDTO.getMsgCd());
        }
        return this.getUserLoginBO(loginRspDTO);
    }

    @Override
    public UserLoginBO ssoScimLogin(String ticket, String domain) {
        TicketUserInfoBO ticketUserInfo = scimTicketService.serviceValidate(ticket, domain);
        if (JudgeUtils.isNull(ticketUserInfo.getUserDetail())) {
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        UserDetailBO userDetail = ticketUserInfo.getUserDetail();
        LOGGER.info("userDetail ssoScimLogin {}", userDetail.toString());
        LoginFrom4aReqDTO loginFrom = new LoginFrom4aReqDTO();
        loginFrom.setUserName(userDetail.getUsername());
        loginFrom.setFullName(userDetail.getName());
        loginFrom.setMobile(userDetail.getMobile());
        LoginRspDTO loginRspDTO = this.scimUserExistLogin(loginFrom);
        if (JudgeUtils.isNotSuccess(loginRspDTO.getMsgCd())) {
            LOGGER.info("ssoScimLogin  fail {}", loginRspDTO.getMsgCd());
            BusinessException.throwBusinessException(loginRspDTO.getMsgCd());
        }
        return this.getUserLoginBO(loginRspDTO);
    }

    @Override
    public LoginRspDTO userExistLogin(LoginFrom4aReqDTO loginFrom4aReqDTO) {
        UserBO userVO = new UserBO();
        userVO.setUserName(JudgeUtils.isNotBlank(loginFrom4aReqDTO.getUserName()) ? loginFrom4aReqDTO.getUserName() : null);
        userVO.setMobile(loginFrom4aReqDTO.getMobile());
        userVO.setFullName(loginFrom4aReqDTO.getFullName());
        UserBO userInfo = systemUserService.getUserInfo(userVO);
        if (JudgeUtils.isNull(userInfo)) {
            BusinessException.throwBusinessException(MessageCodeEnum.USERNAME_NOT_EXISTS);
        }
        return this.getLoginRspDTO(userInfo);
    }

    @Override
    public LoginRspDTO scimUserExistLogin(LoginFrom4aReqDTO loginFrom4aReqDTO) {
        UserBO userVO = new UserBO();
        userVO.setScimUserName(loginFrom4aReqDTO.getUserName());
        userVO.setMobile(loginFrom4aReqDTO.getMobile());
        userVO.setFullName(loginFrom4aReqDTO.getFullName());
        UserBO userInfo = systemUserService.getUserInfo(userVO);
        if (JudgeUtils.isNull(userInfo)) {
            BusinessException.throwBusinessException(MessageCodeEnum.USERNAME_NOT_EXISTS);
        }
        return this.getLoginRspDTO(userInfo);
    }


    private LoginRspDTO getLoginRspDTO(UserBO userInfo) {
        LoginRspDTO loginRspDTO = new LoginRspDTO();
        SessionTokenVO sessionTokenVO = new SessionTokenVO();
        sessionTokenVO.setUserName(userInfo.getUserName());
        sessionTokenVO.setUpmsUserId(userInfo.getUserId());
        sessionTokenVO.setMobile(userInfo.getMobile());
        sessionTokenVO.setFullName(userInfo.getFullName());
        sessionTokenVO.setCstUserId(userInfo.getCstUserId());
        sessionTokenVO.setHasRole(userInfo.getHasRole());
        sessionTokenVO.setPwdModifyTime(userInfo.getPwdModifyTime());
        sessionTokenVO.setScimUserId(userInfo.getScimUserId());
        sessionTokenVO.setScimUserName(userInfo.getScimUserName());
        if (JudgeUtils.isNotEmpty(userInfo.getDeptId())) {
            sessionTokenVO.setDeptName(Optional.ofNullable(systemDepartmentService.getDepartmentInfo(userInfo.getUserId(), userInfo.getDeptId())).map(DeptBO::getDeptName).orElse(null));
        }
        loginRspDTO.setSessionTokenVO(sessionTokenVO);
        loginRspDTO.setMsgCd(MessageCodeEnum.SUCCESS.getMsgCd());
        return loginRspDTO;
    }

}
