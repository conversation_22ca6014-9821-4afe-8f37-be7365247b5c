package com.cmpay.hacp.extend.sso.ticket.autoconfigure.ticket;


import com.cmpay.hacp.extend.sso.ticket.properties.LemonBatchUserProperties;
import com.cmpay.hacp.extend.sso.ticket.properties.LemonWebAdminSsoTicketProperties;
import com.cmpay.hacp.extend.sso.ticket.service.SsoTicketService;
import com.cmpay.hacp.extend.sso.ticket.service.impl.SsoTicketServiceImpl;
import com.cmpay.hacp.utils.http.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({LemonWebAdminSsoTicketProperties.class})
@EnableConfigurationProperties(LemonWebAdminSsoTicketProperties.class)
@AutoConfigureAfter({FeignAutoConfiguration.class})
public class LemonWebAdminSsoTicketAutoConfiguration {

    /**
     * @param lemonWebAdminSsoTicketProperties
     * @return accountInfo4ARestTemplate
     */
    @Bean(name = "accountInfo4ARestTemplate")
    @ConditionalOnBean({CloseableHttpClient.class})
    @ConditionalOnClass({HttpClientUtil.class, CloseableHttpClient.class, RestTemplate.class, LemonBatchUserProperties.class})
    public RestTemplate accountInfo4ARestTemplate(CloseableHttpClient httpClient, LemonWebAdminSsoTicketProperties lemonWebAdminSsoTicketProperties) {
        log.info("init accountInfo4ARestTemplate");
        return HttpClientUtil.getInstance(lemonWebAdminSsoTicketProperties.getConnectTimeout(), lemonWebAdminSsoTicketProperties.getReadTimeout(), httpClient);
    }

    @Bean
    @Primary
    @ConditionalOnBean({RestTemplate.class})
    @ConditionalOnClass({RestTemplate.class, SsoTicketService.class, SsoTicketServiceImpl.class})
    public SsoTicketService ssoTicketService(@Qualifier("accountInfo4ARestTemplate") RestTemplate accountInfo4ARestTemplate,
                                             LemonWebAdminSsoTicketProperties lemonWebAdminSsoTicketProperties) {
        return new SsoTicketServiceImpl(accountInfo4ARestTemplate, lemonWebAdminSsoTicketProperties);
    }
}
