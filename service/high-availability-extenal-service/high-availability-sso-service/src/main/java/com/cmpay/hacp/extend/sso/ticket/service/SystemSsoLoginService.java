package com.cmpay.hacp.extend.sso.ticket.service;


import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.extend.sso.ticket.dto.LoginFrom4aReqDTO;
import com.cmpay.hacp.extend.sso.ticket.dto.LoginRspDTO;

/**
 * <AUTHOR>
 */
public interface SystemSsoLoginService {


    /**
     * 统一工作台/租户门户单点登录入口
     *
     * @param ticket
     * @param service
     * @return
     */
    UserLoginBO ssoLogin(String ssoServer, String ticket, String service);

    /**
     * 新4A单点登录
     *
     * @param ticket
     * @param domain
     * @return
     */
    UserLoginBO ssoScimLogin(String ticket, String domain);




    /**
     * 用户名、姓名、手机号存在验证登录，提供单点登录统一工作台之后，获取会话token,不对外提供此方式登录
     *
     * @param loginFrom4aReqDTO
     * @return
     */
    LoginRspDTO userExistLogin(LoginFrom4aReqDTO loginFrom4aReqDTO);

    /**
     * 新4A用户单点登录
     *
     * @param loginFrom4aReqDTO
     * @return
     */
    LoginRspDTO scimUserExistLogin(LoginFrom4aReqDTO loginFrom4aReqDTO);
}
