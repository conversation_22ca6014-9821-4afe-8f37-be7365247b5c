package com.cmpay.hacp.extend.sso.ticket.service.impl;

/**
 * 短信通知服务
 *
 * <AUTHOR>
 */
// @Slf4j
// public class FreezeAccountSmsNoticeServiceImpl implements FreezeAccountNoticeService {
//     private final MessageSendService systemSmsService;
//
//     public FreezeAccountSmsNoticeServiceImpl(MessageSendService systemSmsService) {
//         this.systemSmsService = systemSmsService;
//     }
//
//     @Override
//     public void sendNotice(String smsSendSerialNumber, String subject, String[] mobiles, String smsCode, Object replaceValues) {
//         log.info("sendSms  smsSendSerialNumber {}, subject {}, mobiles {}, smsCode {}, replaceValues {}", smsSendSerialNumber, subject, Arrays.toString(mobiles), smsCode, replaceValues.toString());
//
//         systemSmsService.sendMessage(smsSendSerialNumber, mobiles[0], smsCode, replaceValues.toString());
//     }
// }
