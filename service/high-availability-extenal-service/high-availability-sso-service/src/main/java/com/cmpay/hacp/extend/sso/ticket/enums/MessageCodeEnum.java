package com.cmpay.hacp.extend.sso.ticket.enums;

import com.cmpay.lemon.common.AlertCapable;

/**
 * 权限系统提示码
 *
 * <AUTHOR>
 * @date 2020/2/17
 */
public enum MessageCodeEnum implements AlertCapable {
    /**
     * 交易成功
     */
    SUCCESS("MON00000", "交易成功"),
    /**
     * 交易失败
     */
    FAIL("MON40000", "交易失败"),
    /**
     * 登录数据解析失败
     */
    LOGIN_DATA_BIND_FAILED("MON00001", "登录数据解析失败"),
    /**
     * 账户或密码错误
     */
    LOGIN_ACCOUNT_OR_PASSWORD_ERROR("MON00002", "账户或密码错误"),
    /**
     * session已失效，请重新登录
     */
    LOGIN_SESSION_EXPIRE("MON00003", "session已失效，请重新登录"),


    /**
     * 数据更新失败
     */
    DB_UPDATE_FAILED("MON00100", "数据更新失败"),
    /**
     * 数据删除失败
     */
    DB_DELETE_FAILED("MON00101", "数据删除失败"),
    /**
     * 数据新增失败
     */
    DB_INSERT_FAILED("MON00102", "数据新增失败"),
    /**
     * 查询记录不存在
     */
    DB_SELECT_FAILED("MON00103", "查询记录不存在"),

    /**
     * 菜单名称不能为空
     */
    MENU_NAME_CANNOT_NULL("MON00200", "菜单名称不能为空"),
    /**
     * 上级菜单不能为空
     */
    PARENT_MENU_CANNOT_NULL("MON00201", "上级菜单不能为空"),
    /**
     * 上级菜单只能为目录类型
     */
    PARENT_MENU_MUST_MENU_OR_CATALOG("MON00203", "上级菜单只能为菜单或者目录"),
    /**
     * 上级菜单只能为菜单类型
     */
    PARENT_MENU_MUSTBE_MENU("MON00204", "上级菜单只能为菜单类型"),
    /**
     * 输入的原始密码有误
     */
    WRONG_ORIGIN_PASSWORD("UPM00022", "输入的旧密码有误"),
    /**
     * 用户输入的新旧密码相同
     */
    SAME_PASSWORD("UPM00035", "用户输入的新旧密码相同"),
    /**
     * 系统菜单不允许删除
     */
    SYSTEM_MENU_CANNOT_DELETE("MON00205", "系统菜单不允许删除"),
    /**
     * 请先删除子菜单或按钮
     */
    DELETE_SUBMENU_OR_BUTTON_FIRST("MON00206", "请先删除子菜单或按钮"),

    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION("MON50000", "系统异常"),
    /**
     * 成功
     */
    UPMS_SUCCESSFUL("00000", "成功"),
    /**
     * ACCESSTOKEN无效
     */
    ACCESS_TOKEN_INVALID("UPM00004", "ACCESSTOKEN无效"),
    /**
     * 用户名或密码错误
     */
    WRONG_USERNAME_OR_PASSWORD("UPM00005", "用户名或密码错误"),
    /**
     * 图片验证码错误
     */
    WRONG_IMAGE_CODE("UPM00008", "图形验证码错误"),
    /**
     * 参数不完整
     */
    INCOMPLETE_PARAM("UPM00009", "参数不完整"),
    /**
     * 手机号已存在
     */
    MOBILE_ALREADY_EXISTS("UPM00011", "手机号已注册"),
    /**
     * 手机号不存在
     */
    MOBILE_NOT_EXISTS("UPM00012", "手机号未注册"),
    /**
     * 用户名已存在
     */
    USERNAME_ALREADY_EXISTS("UPM00013", "用户名已存在"),
    /**
     * 用户名不存在
     */
    USERNAME_NOT_EXISTS("UPM00014", "用户名不存在"),
    /**
     * 用户未登录
     */
    USER_NOT_LOGIN_IN("UPM00026", "用户未登录"),
    /**
     * 图片验证码生成错误
     */
    ERROR_CREATE_IMAGE_CODE("UPM00030", "图形验证码生成错误"),
    /**
     * SESSION_TOKEN不存在
     */
    SESSION_TOKEN_NOT_EXIST("UPM00037", "SESSION_TOKEN不存在"),
    /**
     * 生成AccessToken失败
     */
    FAILED_TO_GET_ACCESSTOKEN("UPM00042", "生成AccessToken失败"),

    /**
     * 无权限访问资源
     */
    MENU_NO_PERMISSION("UPM00049", "无权限访问资源"),

    /**
     * 该用户角色不具备获取菜单权限
     */
    USER_NOT_HAS_ROLE("UPM00057", "该用户角色不具备获取菜单权限"),

    /**
     * 异常码转换异常
     */
    EXCEPTION_CODE_CONVERT_FAIL("UPM00058", "异常码转换异常"),

    /**
     * 生成SessionToken失败
     */
    FAILED_TO_GET_SESSIONTOKEN("UPM00066", "生成SessionToken失败"),
    /**
     * 解析SessionToken失败
     */
    FAILED_TO_PARSE_SESSIONTOKEN("UPM00067", "解析JwtSessionToken失败"),
    /**
     * 校验SessionToken未通过,颁发SessionToken的应用不一致
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_APPID("UPM00068", "校验JwtSessionToken未通过,颁发SessionToken的应用不一致"),

    /**
     * 获取权限系统解析SessionToken的publickey失败，获取的值为空；
     */
    GET_SESSIONTOKEN_PUCLICKEY_ERROR("UPM00070", "获取应用系统的publickey失败"),

    /**
     * JwtSessionToken已过期，请重新登录；
     */
    SESSION_TOKEN_EXPIRED("UPM00071", "JwtSessionToken已过期，请重新登录"),

    /**
     * 校验JwtSessionToken未通过,用户的所属应用编码不一致!
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_USER_APPID_WRONG("UPM00072", "校验JwtSessionToken未通过,用户的所属应用编码不一致!"),

    /**
     * 校验JwtSessionToken未通过,用户的用户名不一致!
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_USER_NAME_WRONG("UPM00073", "校验JwtSessionToken未通过,用户的用户名不一致!"),
    /**
     * 登陆状态过滤器检查发生了异常
     */
    LOGIN_STATUS_CHECK_EXCEPTION("UPM00074", "登陆状态检查异常!"),
    /**
     * 传入的参数异常,无法对参数进行解析！
     */
    PARAM_PARSE_ERROR("UPM00078", "传入的参数异常,无法对参数进行解析！"),

    /**
     * 校验SessionToken未通过,IP地址不一致
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_IPADDR("UPM00082", "校验JwtSessionToken未通过,IP地址不一致"),
    /**
     * 校验SessionToken未通过,请求使用设备不一致
     */
    FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_EQUIPMENT("UPM00083", "校验JwtSessionToken未通过,请求使用设备不一致"),

    /**
     * 校验SessionToken未通过,请求使用设备不一致
     */
    JWT_TOKEN_FUNCTION_INVALID("UPM00097", "token的用途非法"),
    /**
     * 单点登陆的目标应用非法
     */
    SSO_LOGIN_TARGET_APPID_INVALID("UPM00098", "单点登陆的目标应用appId非法"),

    ;

    MessageCodeEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    private final String msgCd;

    private final String msgInfo;

    @Override
    public String getMsgCd() {
        return msgCd;
    }


    @Override
    public String getMsgInfo() {
        return msgInfo;
    }

}
