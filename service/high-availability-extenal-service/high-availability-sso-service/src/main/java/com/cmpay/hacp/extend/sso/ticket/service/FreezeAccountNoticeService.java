package com.cmpay.hacp.extend.sso.ticket.service;

import org.springframework.scheduling.annotation.Async;

/**
 * 账户禁用通知服务
 *
 * <AUTHOR>
 */
public interface FreezeAccountNoticeService {

    /**
     * @param sender:发件人/短信发送日志号
     * @param subject:主题
     * @param recipients:收件人邮件/短信接收号码
     * @param template:模板名称
     * @param templateParams:模板参数
     * @throws Exception
     */
    @Async
    void sendNotice(String sender, String subject, String[] recipients, String template, Object templateParams) throws Exception;
}
