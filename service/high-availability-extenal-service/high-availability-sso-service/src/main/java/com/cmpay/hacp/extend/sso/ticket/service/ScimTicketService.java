package com.cmpay.hacp.extend.sso.ticket.service;


import com.cmpay.hacp.extend.sso.ticket.bo.TicketUserInfoBO;

/**
 * 新4A单点登录
 *
 * <AUTHOR>
 */
public interface ScimTicketService {
    /**
     *
     * @param ticket
     * @param domain
     * @return
     */
    TicketUserInfoBO serviceValidate( String ticket, String domain);

    /**
     *  单点登录参数验证
     * @param ssoServer
     * @param ticket
     * @param service
     * @return
     */
    TicketUserInfoBO getServiceValidate(String ssoServer, String ticket, String service);
}
