package com.cmpay.hacp.extend.sso.ticket.autoconfigure.batch;

import com.cmpay.hacp.extend.sso.ticket.job.AccountInfo4ASyncJob;
import com.cmpay.hacp.extend.sso.ticket.job.FreezeAccountInfoJob;
import com.cmpay.hacp.extend.sso.ticket.job.ScimIamSyncJob;
import com.cmpay.hacp.extend.sso.ticket.properties.BatchScimIamProperties;
import com.cmpay.hacp.extend.sso.ticket.properties.FreezeAccountProperties;
import com.cmpay.hacp.extend.sso.ticket.properties.LemonBatchUserProperties;
import com.cmpay.hacp.extend.sso.ticket.service.BatchUserService;
import com.cmpay.hacp.extend.sso.ticket.service.FreezeAccountService;
import com.cmpay.hacp.extend.sso.ticket.service.ScimIamService;
import com.cmpay.hacp.system.autoconfigure.HacpJacksonAutoConfiguration;
import com.cmpay.hacp.system.autoconfigure.HacpWebAdminAutoConfiguration;
import com.cmpay.hacp.extend.sso.ticket.autoconfigure.ticket.LemonWebAdminScimSsoTicketAutoConfiguration;
import com.cmpay.hacp.extend.sso.ticket.autoconfigure.ticket.LemonWebAdminSsoTicketAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 批量任务
 *
 * @author: lihuiquan
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({AccountInfo4ASyncJob.class, LemonBatchUserProperties.class, BatchScimIamProperties.class, FreezeAccountProperties.class})
@EnableConfigurationProperties({LemonBatchUserProperties.class, BatchScimIamProperties.class, FreezeAccountProperties.class})
@AutoConfigureAfter({HacpWebAdminAutoConfiguration.class,
        HacpJacksonAutoConfiguration.class,
        LemonWebAdminScimSsoTicketAutoConfiguration.class,
        LemonWebAdminSsoTicketAutoConfiguration.class})
public class LemonWebAdminJobAutoConfiguration {

    /**
     * 定时同步4A用户
     *
     * @param batchUserService
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "upms.user-sync-config", name = "enabled", havingValue = "true", matchIfMissing = false)
    @ConditionalOnClass({AccountInfo4ASyncJob.class})
    @ConditionalOnBean({BatchUserService.class})
    public AccountInfo4ASyncJob accountInfo4ASyncJob(BatchUserService batchUserService) {
        log.info("init accountInfo4ASyncJob");
        return new AccountInfo4ASyncJob(batchUserService);
    }


    /**
     * 定时同步新4A用户、部门
     *
     * @param scimIamService
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin.scim-iam", name = "enabled", havingValue = "true", matchIfMissing = false)
    @ConditionalOnClass({ScimIamSyncJob.class})
    @ConditionalOnBean({ScimIamService.class})
    public ScimIamSyncJob scimIamSyncJob(ScimIamService scimIamService) {
        log.info("init scimIamSyncJob");
        return new ScimIamSyncJob(scimIamService);
    }

    /**
     * 定时冻结长期未使用账号
     *
     * @param freezeAccountService
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin.freeze", name = "enabled", havingValue = "true", matchIfMissing = false)
    @ConditionalOnClass({FreezeAccountInfoJob.class})
    @ConditionalOnBean({FreezeAccountService.class})
    public FreezeAccountInfoJob freezeAccountInfoJob(FreezeAccountService freezeAccountService) {
        log.info("init freezeAccountService");
        return new FreezeAccountInfoJob(freezeAccountService);
    }
}
