package com.cmpay.hacp.extend.sso.ticket.service;


import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamOrganization;
import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamOrganizations;
import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamUser;
import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamUsers;

import java.util.List;

/**
 * 新4A账户账户体系
 *
 * <AUTHOR>
 */
public interface ScimIamService {
    /**
     * 同步组织架构与用户
     */
    void sync();

    /**
     * 同步全量用户
     */
    void syncAllScimIamUsers();


    /**
     * 同步startIndex 之后的全量用户
     *
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     * @return
     */
    void syncAllScimIamUsers(int startIndex, int count, String filter, String searchType);

    /**
     * 获取全量用户
     */
    List<ScimIamUser> getAllScimIamUsers();

    /**
     * 获取startIndex 之后的全量用户
     *
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     * @return
     */
    List<ScimIamUser> getAllScimIamUsers(int startIndex, int count, String filter, String searchType);

    /**
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     * @return
     */
    ScimIamUsers scimIamGetUsersApi(int startIndex, int count, String filter, String searchType) throws Exception;

    /**
     * 全量同步组织架构
     */
    void syncAllScimIamOrganizations();

    /**
     * 同步指定条件组织架构
     *
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     */
    void syncAllScimIamOrganizations(int startIndex, int count, String filter, String searchType);

    /**
     * @return
     */
    List<ScimIamOrganization> getAllScimIamOrganizations();

    /**
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     * @return
     */
    List<ScimIamOrganization> getAllScimIamOrganizations(int startIndex, int count, String filter, String searchType);


    /**
     * @param startIndex
     * @param count
     * @param filter
     * @param searchType
     * @return
     */
    ScimIamOrganizations scimIamGetOrganizationsApi(int startIndex, int count, String filter, String searchType) throws Exception;

}
