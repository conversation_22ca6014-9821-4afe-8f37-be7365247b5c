package com.cmpay.hacp.extend.sso.ticket.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


@Data
@ConfigurationProperties(prefix = "hacp.web.admin.scim-iam")
public class BatchScimIamProperties {

    /**
     * 是否启用定时任务，默认false
     */
    private boolean enabled;

    /**
     * 是否只开启更新用户模式，此模式下只会更新系统中存在的用户信息，不会新增用户，默认false
     */
    private boolean onlyUpdate;

    /**
     * 是否同步物理删除4A中不存在的用户，默认false，只禁用用户，不删除用户。
     */
    private boolean syncDelete;

    /**
     * 定时任务时间cron表达式
     */
    private String cron;
    /**
     *
     */
    private int startIndex = 1;
    /**
     *
     */
    private int count = 200;
    /**
     *
     */
    private String filter;
    /**
     *
     */
    private String searchType = "NORMAL";
    /**
     * 是否同步更新用户组织机构
     */
    private boolean enabledSyncUserOrganization;

    /**
     * 是否同步组织机构
     */
    private boolean enabledOrganization;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用秘钥
     */
    private String appSecret;

    /**
     * 默认部门Id
     */
    private String defaultDeptId = "2";
    /**
     * 默认数据权限类型：1：本部门以及下级部门
     */
    private String defaultDutyId = "1";
    /**
     * 服务器相关信息
     */
    private Server server = new Server();

    @Data
    public class Server {
        /**
         * 测试 http://auth.4a.test:8002  ************ auth.4a.test
         * 生产 http://auth.4a.cmft       ************* auth.4a.cmft
         */
        private String address = "http://auth.4a.cmft";

        /**
         * 获取用户接口
         */
        private String userApi = "/openapi_v2/scim/Users";

        /**
         * 获取组织架构接口
         */
        private String organizationApi = "/openapi_v2/scim/Organizations";

        /**
         * 连接超时时间
         */
        private int connectTimeout = 6000;

        /**
         * 读取超时时间
         */
        private int readTimeout = 6000;

    }


}
