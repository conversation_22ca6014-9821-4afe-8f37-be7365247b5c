<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysql" targetRuntime="MyBatis3" defaultModelType="lemonflat">

        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.lemon.LemonPlugin">
            <property name="mappers" value="com.cmpay.lemon.framework.dao.BaseDao"/>
            <property name="ignoreModelFields" value="tmSmp"/>
        </plugin>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver" connectionURL="**********************************"
                        userId="dicp" password="dicp123">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.cmpay.hacp.dispatch.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="false"/>
            <property name="rootClass" value="com.cmpay.framework.data.BaseDO"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.cmpay.hacp.dispatch.dao"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>
       <table tableName="dispatch_node" domainObjectName="DispatchNodeDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
           <generatedKey column="dispatch_node_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="dispatch_test" domainObjectName="DispatchTestDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
           <generatedKey column="dispatch_test_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="dispatch_zone" domainObjectName="DispatchZoneDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="dispatch_config" domainObjectName="DispatchConfigDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="dispatch_push_history" domainObjectName="DispatchPushHistoryDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="dispatch" domainObjectName="DispatchDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="dispatch_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="api_location" domainObjectName="ApiLocationDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
          <generatedKey column="api_location_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="api_tag" domainObjectName="ApiTagDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="api_location_api_tag" domainObjectName="ApiLocationApiTagDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="app_service" domainObjectName="AppServiceDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
           <generatedKey column="app_service_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="dispatch_rule" domainObjectName="DispatchRuleDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="dispatch_rule_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="rule_expression" domainObjectName="RuleExpressionDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="app_instance_machine" domainObjectName="AppInstanceMachineDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>
        <table tableName="app_instance_group" domainObjectName="AppInstanceGroupDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
           <generatedKey column="instance_group_id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="app_instance_zone" domainObjectName="AppInstanceZoneDO"
               enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
        </table>


    </context>
</generatorConfiguration>
