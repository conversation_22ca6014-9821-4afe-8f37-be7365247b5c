dependencies {
    api project(':common:high-availability-common')
    api project(':service:high-availability-tenant-service')
    api project(':service:high-availability-emergency-service')

    api('com.cmpay:lemon-framework-starter-context')
    api('com.cmpay:lemon-framework-starter-idgenerator')
    api('com.cmpay:lemon-framework-starter-datasource')
    api('com.cmpay:lemon-framework-starter-mybatis')
    api('com.cmpay:lemon-framework-starter-cache-jcache')
    api('com.cmpay:file-client-starter')
    api('com.cmpay:lemon-common')
    api('com.cmpay:alerting-starter')
    api('com.cmpay.high-availability:hafr-agent-interface:1.0.0-SNAPSHOT')
    api('org.springframework.cloud:spring-cloud-starter-openfeign')
    api ('io.github.openfeign:feign-jackson')
}
