
package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 系统日志
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemLogDTO extends StaticLogDTO {

    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
    private String tenantId;

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "0179035dc433428c84ff434379374157")
    private String workspaceId;


    /**
     * @Fields tenantName 租户名称
     */
    @ApiModelProperty(value = "租户名称", required = true, example = "租户名称")
    private String tenantName;

    /**
     * @Fields workspaceName 项目名称
     */
    @ApiModelProperty(value = "项目名称", required = true, example = "项目名称")
    private String workspaceName;


}
