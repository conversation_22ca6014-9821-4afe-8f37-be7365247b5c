package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
public class EmergencyAppAddReqDTO implements TenantCapable {

    /**
     * @Fields hostApp 应用名
     */
    @ApiModelProperty(value = "应用名", required = true, example = "应用名")
    @NotNull(message = "HAC22005")
    private String hostApp;

    /**
     * @Fields hostAppName 应用名
     */
    @ApiModelProperty(value = "应用名", required = true, example = "应用名")
    @NotNull(message = "HAC22005")
    private String hostAppName;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
}
