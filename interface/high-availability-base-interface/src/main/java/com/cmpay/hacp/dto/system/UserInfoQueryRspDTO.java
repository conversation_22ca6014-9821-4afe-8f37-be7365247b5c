package com.cmpay.hacp.dto.system;

import com.cmpay.framework.data.response.PageableRspDTO;
import com.cmpay.hacp.bo.system.UserBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR> lihuiquan
 * @date : 2018/11/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserInfoQueryRspDTO extends PageableRspDTO {
    private List<UserBO> list;

}
