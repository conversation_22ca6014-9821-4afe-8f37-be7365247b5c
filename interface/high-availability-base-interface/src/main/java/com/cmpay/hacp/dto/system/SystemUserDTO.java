package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统用户 DTO
 *
 * <AUTHOR>
 * @create 2024/05/09 14:30:43
 * @since 1.0.0
 */
@Data
@ApiModel(description = "系统用户")
public class SystemUserDTO {

    /**
     * @Fields userId 用户编号
     */
    @ApiModelProperty(value = "用户编号", required = true, example = "用户编号")
    private String userId;

    /**
     * @Fields userName 用户名
     */
    @ApiModelProperty(value = "用户名", required = true, example = "用户名")
    private String userName;

    /**
     * @Fields fullName 姓名
     */
    @ApiModelProperty(value = "姓名", required = true, example = "姓名")
    private String fullName;

}
