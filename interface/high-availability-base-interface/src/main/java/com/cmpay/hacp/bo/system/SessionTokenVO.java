package com.cmpay.hacp.bo.system;

import com.cmpay.lemon.framework.desensitization.Desensitization;
import com.cmpay.lemon.framework.desensitization.Type;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/19
 * 会话Token实体类
 */
@Data
public class SessionTokenVO implements Serializable {

    /**
     * 数据存储集合
     */
    private Map<String, Object> map;

    @Desensitization(Type.MIDDLE)
    private String upmsUserId;

    @Desensitization(Type.MIDDLE)
    private String cstUserId;

    @Desensitization(Type.MOBILE_NO)
    private String mobile;

    /**
     * @Fields scimUserId 用户编号
     */
    @Desensitization(Type.MIDDLE)
    private String scimUserId;

    /**
     * @Fields scimUserName 用户名
     */
    @Desensitization(Type.MIDDLE)
    private String scimUserName;

    @Desensitization(Type.MIDDLE)
    private String userName;

    @Desensitization(Type.CHINESE_NAME)
    private String fullName;

    @Desensitization(Type.MIDDLE)
    private String deptName;

    @Desensitization(Type.MIDDLE)
    private String sessionToken;

    private String hasRole;

    private LocalDateTime pwdModifyTime;

    private String appId;


}
