package com.cmpay.hacp.bo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门VO返回对象
 * author: tnw
 * Date: 2020/4/5
 */
@Data
public class DeptBO {

    /**
     * @Fields deptId
     */
    private String deptId;
    /**
     * @Fields deptName 部门名称
     */
    private String deptName;
    /**
     * @Fields parentId 上级部门ID，一级部门为0
     */
    private String parentId;
    /**
     * @Fields parentDeptName 上级部门名称
     */
    private String parentDeptName;
    /**
     * @Fields orderNum 排序
     */
    private Integer orderNum;
    /**
     * @Fields status 是否删除   d：已删除  n：正常
     */
    private String status;
    /**
     * @Fields createUserID 创建者ID
     */
    private String createUserID;
    /**
     * @Fields createUserName 创建者名称
     */
    private String createUserName;
    /**
     * @Fields createTime 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    /**
     * @Fields 子部门
     */
    private List<DeptBO> childrenDeptList;
}
