package com.cmpay.hacp.bo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: tnw
 * Date: 2020/4/6
 */
@Data
public class RoleBO {
    /**
     * @Fields roleId 角色编号
     */
    private Long roleId;
    /**
     * @Fields roleName 角色名称
     */
    private String roleName;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields deptId 部门ID
     */
    private String deptId;
    /**
     * @Fields createUserId 创建者ID
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    /**
     * @Fields ownerAppId 归属应用
     */
    private String ownerAppId;
    /**
     * @Fields deptName 部门名称
     */
    private String deptName;
    /**
     * @Fields createUserName 创建者
     */
    private String createUserName;
    /**
     * @Fields appName 应用名称
     */
    private String appName;
}
