/*
 * @ClassName RiseSystemLogDO
 * @Description
 * @version 1.0
 * @Date 2023-08-08 16:10:59
 */
package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 系统日志
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemLogQueryDTO extends StaticLogReqDTO {
    /**
     * @Fields pageNum 第几页
     */
    @ApiModelProperty(value = "第几页", required = false, example = "1")
    private int pageNum = 1;
    /**
     * @Fields pageSize 一页多少条
     */
    @ApiModelProperty(value = "一页多少条", required = false, example = "10")
    private int pageSize = 10;
    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = false, example = "0179035dc433428c84ff434379374157")
    private String tenantId;

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = false, example = "0179035dc433428c84ff434379374157")
    private String workspaceId;


}
