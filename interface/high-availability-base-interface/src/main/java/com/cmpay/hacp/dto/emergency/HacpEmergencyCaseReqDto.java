/*
 * @ClassName HacpEmergencyCaseDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-16 10:53:43
 */
package com.cmpay.hacp.dto.emergency;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/23 17:34
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyCaseReqDto {
    /**
     * @Fields id id
     */
    private Long id;

    private String workspaceId;
    /**
     * @Fields caseName 任务名称
     */
    private String caseName;
    /**
     * @Fields caseDescribe 任务描述
     */
    private String caseDescribe;
    /**
     * @Fields caseDeployId 部署id
     */
    private String caseDeployId;
    /**
     * @Fields operator 操作人
     */
    private String operator;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 属性
     */
    private  Map<String, String> variables;

    /**
     * 动态属性 多个任务->多个属性  属性名均使用节点id_属性名 防止重复
     */
    private List<HacpCaseVariableReqDto> taskVariables;

    /**
     * @Fields status 状态
     */
    private String status;

    private int pageNum;

    private int pageSize;

    /**
     * 加密使用
     */
    private String uuid;

    private Long tagId;
}