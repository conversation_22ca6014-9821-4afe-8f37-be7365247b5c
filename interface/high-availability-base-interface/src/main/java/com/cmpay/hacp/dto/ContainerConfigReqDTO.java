package com.cmpay.hacp.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/9/6 11:02
 * @version 1.0
 */
@Setter
@Getter
@ToString
public class ContainerConfigReqDTO {
    private String uuid;
    /**
     * @Fields clientId 客户端id
     */
    private String clientId;
    /**
     * @Fields clientSecret 密钥
     */
    private String clientSecret;
    /**
     * @Fields grantType 授权类型
     */
    private String grantType;
    /**
     * @Fields username 账号
     */
    private String username;
    /**
     * @Fields password 密码
     */
    private String password;

    private Long taskId;

}
