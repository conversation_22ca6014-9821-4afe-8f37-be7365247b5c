package com.cmpay.hacp.dto.system;

import com.cmpay.hacp.bo.PageableReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述
 *
 * <AUTHOR> lihuiquan
 * @date : 2018/11/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserInfoQueryReqDTO extends PageableReq {
    /**
     * 用户ID
     */
    private String userName;
    /**
     * 用户姓名
     */
    private String fullName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 状态
     */
    private String status;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 操作用户名
     */
    private String operatorId;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 拥有角色
     */
    private String hasRole;


    /**
     * @Fields scimUserId 4A用户ID
     */
    private String scimUserId;

    /**
     * @Fields scimUserName 4A用户名
     */
    private String scimUserName;

}
