package com.cmpay.hacp.dto.system;

import com.cmpay.hacp.bo.system.SessionTokenVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 用户登录返回数据
 *
 * <AUTHOR> lihuiquan
 */
@ApiModel(value = "UserLoginRspDTO", description = "用户登录返回数据")
@Data
public class UserLoginRspDTO {
    /**
     * 上次登陆时间
     */
    private String lastLoginTime;


    /**
     * 是否需要提醒用户修改密码
     */
    private String pwdNeedToModify;

    /***
     *  会话token  信息登录平台
     */
    private SessionTokenVO userInfo;

    private LoginHistoryLogDTO loginHistory;


}
