package com.cmpay.hacp.bo.menu;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: tnw
 * Date: 2020/4/4
 */
@Data
public class MenuBO {
    /**
     * @Fields menuId 菜单ID
     */
    private Long menuId;
    /**
     * @Fields parentId 父菜单ID，一级菜单为0
     */
    private Long parentId;
    /**
     * @Fields name 菜单名称
     */
    private String name;
    /**
     * @Fields appId 应用英文简称
     */
    private String appId;
    /**
     * @Fields url 菜单URL
     */
    private String url;
    /**
     * @Fields type 类型   D：目录   M：菜单   B：按钮
     */
    private String type;
    /**
     * @Fields icon 菜单图标
     */
    private String icon;
    /**
     * @Fields orderNum 排序
     */
    private Long orderNum;
    /**
     * @Fields createUserId 创建者ID
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    /**
     * @Fields perms 授权(多个用逗号分隔，如：user:list,user:create)
     */
    private String perms;
    /**
     * @Fields meta HTML文档元数据
     */
    private String meta;
    /**
     * @Fields component 对应组件
     */
    private String component;
    /**
     * @Fields redirect
     */
    private String redirect;
    /**
     * @Fields alias
     */
    private String enName;
    /**
     * @Fields 子菜单列表
     */
    private List<MenuBO> menuBOList;

    /**
     * 父菜单名称
     */
    private String parentName;
    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    /**
     * 是否缓存
     */
    private Boolean keepalive;

    /**
     * 隐藏页面标题栏
     */
    private Boolean hidePageTitleBar;
}
