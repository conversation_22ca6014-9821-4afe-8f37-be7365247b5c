package com.cmpay.hacp.bo;

import com.cmpay.framework.data.request.GenericDTO;
import com.cmpay.lemon.framework.data.Pageable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class PageableReq extends GenericDTO implements Pageable  {
    /**
     * 当前页
     */
    @NotNull(message = "HAC00028")
    private int pageNum;
    /**
     * 每页的数量
     */
    @NotNull(message = "HAC00029")
    private int pageSize;

    private long startRow;

    private long endRow;

    private long total;

    private int pages;

}
