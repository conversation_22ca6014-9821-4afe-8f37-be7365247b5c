package com.cmpay.hacp.dto.system;

import com.cmpay.lemon.common.log.LogIgnore;
import com.cmpay.lemon.framework.desensitization.Desensitization;
import com.cmpay.lemon.framework.desensitization.Type;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Map;

/**
 * @author: tnwning
 * @Date: 2020/02/15
 * 权限认证登陆请求DTO
 */
@Data
public class LoginPageReqDTO implements Serializable {

    @ApiModelProperty(value = "用户名")
    @NotEmpty(message = "HAC70009")
    @Desensitization(Type.MIDDLE)
    private String userName;

    @ApiModelProperty(value = "密码")
    @LogIgnore
    @NotEmpty(message = "HAC70009")
    private String password;

    @ApiModelProperty(value = "图片验证码请求ID")
    @NotEmpty(message = "HAC70009")
    @Desensitization(Type.MIDDLE)
    private String captchaReqId;

    @ApiModelProperty(value = "图片验证码")
    @NotEmpty(message = "HAC70009")
    @Desensitization(Type.MIDDLE)
    @Pattern(regexp = "^[^`~!@#$%^&*+=|{}':;',//[//].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。，、？]*$", message = "UPM00076")
    @Length(max = 10, message = "HAC70077")
    private String captchaCode;

    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    @ApiModelProperty(value = "请求参数")
    private Map<String, Object> paramsMap;
}
