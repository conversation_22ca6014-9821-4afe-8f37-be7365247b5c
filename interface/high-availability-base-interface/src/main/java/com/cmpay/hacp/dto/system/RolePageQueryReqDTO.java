
package com.cmpay.hacp.dto.system;

import com.cmpay.hacp.bo.PageableReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述
 *
 * <AUTHOR> lihuiquan
 * @date : 2018/11/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RolePageQueryReqDTO extends PageableReq {

    /**
     * @Fields roleId 角色编号
     */
    private Long roleId;

    /**
     * @Fields roleName 角色名称
     */
    private String roleName;

    /**
     * @Fields status 状态
     */
    private String status;

    /**
     * @Fields deptId 部门ID
     */
    private String deptId;

    /**
     * @Fields deptName 部门名称
     */
    private String deptName;


}
