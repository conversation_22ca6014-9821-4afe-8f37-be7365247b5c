package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 登录请求数据
 *
 * <AUTHOR> lihuiquan
 */
@ApiModel(value = "UserLoginReqDTO", description = "登录请求数据")
@Data
public class UserLoginReqDTO {
    /**
     * 用户ID
     */
    private Long userNo;

    /**
     * 用户名
     */
    private String username;
    /**
     * uuid,用于联合username查找sm4密钥
     */
    private String uuid;

    /**
     * 密码
     */
    private String password;

    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 短信验证码
     */
    private String messageCode;

    /**
     * 图片验证码请求ID
     */
    private String captchaReqId;

    /**
     * 图片验证码
     */
    private String captchaCode;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 单点登录票据
     */
    private String ticket;
    /**
     * 单点登录页面地址
     */
    private String service;

    /**
     * 单点登录服务器地址
     */
    private String ssoServer;

}
