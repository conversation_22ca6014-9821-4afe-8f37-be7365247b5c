package com.cmpay.hacp.dto.emergency;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class EmergencyTagUpdateReqDTO extends EmergencyTagAddReqDTO {
    /**
     * @Fields machineId
     */

    @NotNull(message = "HAC22004")
    @ApiModelProperty(value = "主键", required = true, example = "主键")
    private Integer tagId;
}
