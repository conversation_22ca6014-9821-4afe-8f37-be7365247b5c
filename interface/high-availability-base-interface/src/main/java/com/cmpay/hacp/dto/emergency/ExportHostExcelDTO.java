package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.annotation.excel.ColumnName;
import com.cmpay.hacp.annotation.excel.DropDownSetValue;
import com.cmpay.hacp.enums.HostOSEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/08/21 16:59
 * @since 1.0.0
 */
@Data
public class ExportHostExcelDTO {
    /**
     * @Fields machineAddress 机器地址
     */
    @ColumnName(title = "主机地址(必填)")
    @NotNull(message = "HAC22002")
    private String hostAddress;
    /**
     * @Fields machinePort port
     */
    @NotNull(message = "HAC22001")
    @ColumnName(title = "主机端口(必填)")
    private Integer hostPort;
    /**
     * @Fields machineUsername 账号
     */
    @NotNull(message = "HAC22000(必填)")
    @ColumnName(title = "主机账号*")
    private String hostUsername;
    /**
     * @Fields machinePassword 密码
     */
    @ColumnName(title = "主机密码(可选)")
    private String hostPassword;
    /**
     * @Fields machineKey 密钥
     */
    @ColumnName(title = "主机密钥(可选)")
    private String secretKey;

    /**
     * @Fields machineTag 标签
     */
    @ColumnName(title = "主机标签(可选,多个标签使用分号隔开)")
    private String hostTag;

    /**
     * @Fields hostApp 归属应用
     */
    @ColumnName(title = "实例名(可选)**新增直接输入")
    private String hostApp;

    /**
     * @Fields machineDesc 机器描述
     */
    @ColumnName(title = "主机描述(可选)")
    private String hostDesc;

    /**
     * @Fields machineType 主机系统
     */
    @ColumnName(title = "主机系统(可选)",fieldType = HostOSEnum.class,deserializationMethodName="getEnum", SerializationMethodName = "getHostOsExt")
    @NotNull(message = "HAC22003")
    @DropDownSetValue(value = HostOSEnum.class ,colIndex = 8)
    private HostOSEnum hostOS;


    public String getHostOsExt(){
        return hostOS.getValue();
    }
}
