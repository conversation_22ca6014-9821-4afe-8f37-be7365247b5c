package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: tnw
 * @Date: 2020/02/15
 * 菜单资源返回DTO
 */
@Data
public class MenuTreeRspDTO {


    @ApiModelProperty(value = "子菜单列表")
    private List<MenuTreeRspDTO> children;

    @ApiModelProperty(value = "父菜单ID")
    private Long parentId;

    @ApiModelProperty(value = "父菜单名字")
    private String parentName;

    @ApiModelProperty(value = "菜单类型")
    private String type;

    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

    @ApiModelProperty(value = "菜单名称")
    private String name;

    @ApiModelProperty(value = "菜单url")
    private String url;

    @ApiModelProperty(value = "权限")
    private String perms;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "排序序号")
    private Long orderNum;

    @ApiModelProperty(value = "HTML文档元数据")
    private String meta;

    @ApiModelProperty(value = "对应组件")
    private String component;

    @ApiModelProperty(value = "映射")
    private String redirect;


    private String enName;

    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    /**
     * 是否缓存
     */
    private Boolean keepalive;

    /**
     * 隐藏页面标题栏
     */
    private Boolean hidePageTitleBar;

}
