package com.cmpay.hacp.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class DictDTO {
    /**
     * @Fields dictId 编号
     */
    private String dictId;
    /**
     * @Fields value 数据值
     */
    @ApiModelProperty(required = true)
    private String value;
    /**
     * @Fields label 标签名
     */
    @ApiModelProperty(required = true)
    private String label;
    /**
     * @Fields type 类型
     */
    @ApiModelProperty(required = true)
    private String type;
    /**
     * @Fields description 描述
     */
    @ApiModelProperty(required = true)
    private String description;
    /**
     * @Fields sort 排序（升序）
     */
    @ApiModelProperty(required = true)
    private Long sort;
    /**
     * @Fields parentId 父级编号
     */
    @ApiModelProperty(required = true,example = "0")
    private String parentId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    private List<DictDTO> child;

}
