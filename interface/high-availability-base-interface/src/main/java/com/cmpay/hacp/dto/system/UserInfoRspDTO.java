package com.cmpay.hacp.dto.system;

import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.bo.system.UserBO;
import lombok.Data;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR> lihuiquan
 * @date : 2018/11/9
 */
@Data
public class UserInfoRspDTO  {

    private UserBO userInfo;

    /**
     * @Fields roleList 用户角色列表
     */
    private List<RoleBO> roleList;

    private List<String> permissions;

    private List<Long> roleIdList;

    /**
     * 用户菜单
     */
    private List<MenuTreeRspMetaDTO> menus;
}
