/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.dto.emergency;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyTaskReqDto {
    /**
     * @Fields id 流水号
     */
    private Long id;
    /**
     * @Fields taskName 任务名称
     */
    private String taskName;
    /**
     * @Fields taskType 任务类型：人工、系统应急调度、接口调用、shell
     */
    @JsonAlias({"taskTypeEnum","taskType"})
    private String taskType;
    /**
     * @Fields taskDescribe 任务描述
     */
    private String taskDescribe;
    /**
     * @Fields taskOperator 任务执行人
     */
    private String taskOperator;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields taskParam 任务参数JSON
     */
    private JSONObject taskParam;

    private int pageNum;

    private int pageSize;

    private String uuid;

    private List<Integer> tagIds;

    private Long tagId;
}