/*
 * @ClassName HacpEmergencyCaseDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-16 10:53:43
 */
package com.cmpay.hacp.dto.emergency;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/16 11:11
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyCaseRspDto {
    /**
     * @Fields id id
     */
    private Long id;

    private String workspaceId;
    /**
     * @Fields caseName 任务名称
     */
    private String caseName;
    /**
     * @Fields caseDescribe 任务描述
     */
    private String caseDescribe;
    /**
     * @Fields caseDeployId 部署id
     */
    private String caseDeployId;
    /**
     * @Fields operator 操作人
     */
    private String operator;
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields 流程定义xml
     */
    private String processDefinitionXml;

    private HacpProcessDetailDto processDetail;

    private List<HacpCaseVariableRspDto> variableList;

    private String tagName;
    private List<Integer> tagIds;

    private String taskParamStatus;
}