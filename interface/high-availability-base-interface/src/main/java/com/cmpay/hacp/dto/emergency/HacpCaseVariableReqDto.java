/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.dto.emergency;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpCaseVariableReqDto {
    private JSONObject taskParamJson;
    @JsonIgnore
    private String taskParam;
    @JsonAlias({"taskTypeEnum","taskType"})
    private String taskType;

    private String taskName;

    private String activityId;

    private String taskId;
}