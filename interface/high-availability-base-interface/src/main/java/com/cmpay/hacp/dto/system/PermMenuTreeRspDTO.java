package com.cmpay.hacp.dto.system;

import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: tnw
 * @Date: 2020/02/17
 * 获取菜单返回DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PermMenuTreeRspDTO extends DefaultRspDTO<NoBody> {

    @ApiModelProperty(value = "菜单树形列表")
    private List<MenuTreeRspDTO> menuTreeList;


    @ApiModelProperty(value = "操作列表")
    private List<MenuActionRspDTO> menuActionList;
}
