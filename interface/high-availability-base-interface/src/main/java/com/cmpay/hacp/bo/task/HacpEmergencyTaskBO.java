/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.bo.task;

import com.cmpay.hacp.capable.EntityTagCapable;
import com.cmpay.hacp.capable.TaskEncryptCapable;
import com.cmpay.hacp.capable.TenantCapable;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyTaskBO extends TaskParam implements TaskEncryptCapable, TenantCapable, EntityTagCapable {
    /**
     * @Fields id 流水号
     */
    private Long id;
    /**
     * @Fields tenantId 租户编号
     */
    private String tenantId;
    /**
     * @Fields workspaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields taskName 任务名称
     */
    private String taskName;
    /**
     * @Fields taskType 任务类型：人工、系统应急调度、接口调用、shell
     */
    @JsonAlias({"taskTypeEnum","taskType"})
    private String taskType;
    /**
     * @Fields taskDescribe 任务描述
     */
    private String taskDescribe;
    /**
     * @Fields taskOperator 任务执行人
     */
    private String taskOperator;
    private String operatorName;
    /**
     * @Fields operator 操作人
     */
    private String operator;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields taskParam 任务参数JSON
     */
    private String taskParam;

    private TaskParam taskParamJson;

    private String uuid;

    private List<Integer> tagIds;
    private List<String> tagNames;
    private Integer tagId;

    private String entityType;

    private String tagName;

    private String operatorId;

    private String taskParamStatus;

    private String comment;

    @Override
    public Long getEntityId() {
        return this.getId();
    }
}