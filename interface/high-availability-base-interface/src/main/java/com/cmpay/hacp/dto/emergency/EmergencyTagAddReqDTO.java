package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
public class EmergencyTagAddReqDTO implements TenantCapable {

    /**
     * @Fields machineTag 标签
     */
    @ApiModelProperty(value = "机器标签", required = true, example = "机器标签")
    private String tag;

    /**
     * @Fields machineTag 标签
     */
    @ApiModelProperty(value = "机器标签", required = true, example = "机器标签")
    private String tagName;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
}
