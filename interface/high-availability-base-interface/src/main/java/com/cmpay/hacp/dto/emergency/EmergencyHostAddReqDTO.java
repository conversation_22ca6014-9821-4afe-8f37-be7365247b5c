package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.enums.HostOSEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
public class EmergencyHostAddReqDTO implements TenantCapable {

    /**
     * @Fields machineDesc 机器描述
     */
    @ApiModelProperty(value = "描述",  example = "描述")
    private String hostDesc;
    /**
     * @Fields machineUsername 账号
     */
    @NotNull(message = "HAC22000")
    @ApiModelProperty(value = "账号", required = true, example = "账号")
    private String hostUsername;
    /**
     * @Fields machinePassword 密码
     */
    @ApiModelProperty(value = "密码", example = "密码")
    private String hostPassword;
    /**
     * @Fields machinePort port
     */
    @NotNull(message = "HAC22001")
    @ApiModelProperty(value = "端口", required = true, example = "端口")
    private Integer hostPort;
    /**
     * @Fields machineAddress 机器地址
     */
    @ApiModelProperty(value = "地址", required = true, example = "地址")
    @NotNull(message = "HAC22002")
    private String hostAddress;
    /**
     * @Fields machineKey 密钥
     */
    @ApiModelProperty(value = "密钥", example = "密钥")
    private String secretKey;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields machineType 主机系统
     */
    @ApiModelProperty(value = "主机系统", required = true, example = "主机类型")
    private HostOSEnum hostOS;

    /**
     * 密钥缓存key
     */
    @ApiModelProperty(value = "加密uuid", required = true, example = "加密uuid")
    private String uuid;

    /**
     * @Fields hostTagIds 标签Ids
     */
    @ApiModelProperty(value = "标签Ids", example = "标签Ids")
    private List<Integer> hostTagIds;

    /**
     * @Fields hostAppId 应用id
     */
    @ApiModelProperty(value = "应用id", example = "应用id")
    private Integer hostAppId;
}
