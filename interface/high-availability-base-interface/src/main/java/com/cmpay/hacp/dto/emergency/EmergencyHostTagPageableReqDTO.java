package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.bo.PageableReq;
import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyHostTagPageableReqDTO extends PageableReq implements TenantCapable {

    @ApiModelProperty(value = "标签",example = "标签")
    private String tag;

    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;

}
