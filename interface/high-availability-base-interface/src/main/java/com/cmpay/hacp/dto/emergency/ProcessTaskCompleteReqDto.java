/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.dto.emergency;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class ProcessTaskCompleteReqDto {
    /**
     * @Fields taskId 任务id
     */
    private String taskId;
    /**
     * @Fields 流程业务ID
     */
    private String businessKey;
    /**
     * @Fields pass：同意 reject：不同意
     */
    private String result;
    /**
     * @Fields 意见
     */
    private String comment;

    private Map<String, String> variables;

}