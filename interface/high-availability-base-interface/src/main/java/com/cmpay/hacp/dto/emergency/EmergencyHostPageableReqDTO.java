package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.bo.PageableReq;
import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyHostPageableReqDTO extends PageableReq implements TenantCapable {

    @ApiModelProperty(value = "地址",example = "地址")
    private String hostAddress;

    @ApiModelProperty(value = "应用名",example = "机器类型")
    private Integer hostAppId;

    @ApiModelProperty(value = "标签",example = "标签")
    private Integer hostTagId;

    @ApiModelProperty(value = "应用名",example = "机器类型")
    private List<Integer> hostAppIds;

    @ApiModelProperty(value = "标签",example = "标签")
    private List<Integer> hostTagIds;

    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;

}
