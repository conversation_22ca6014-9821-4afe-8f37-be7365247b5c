package com.cmpay.hacp.dto.emergency;

import com.cmpay.hacp.bo.PageableReq;
import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:27
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyTaskScriptPageableReqDTO extends PageableReq implements TenantCapable {

    /**
     * @Fields scriptName 脚本名称
     */
    @ApiModelProperty(value = "脚本名称", example = "重启")
    private String scriptName;
    /**
     * @Fields scriptDescribe 描述
     */
    @ApiModelProperty(value = "描述",  example = "描述")
    private String scriptDescribe;
    /**
     * @Fields scriptContent 脚本内容
     */
    @ApiModelProperty(value = "脚本内容",  example = "ls")
    private String scriptContent;

    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;

}
