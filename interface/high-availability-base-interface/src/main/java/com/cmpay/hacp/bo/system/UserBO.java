package com.cmpay.hacp.bo.system;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对象请求返回VO对象
 *
 * @author: tnw
 * Date: 2020/4/6
 */
@Data
public class UserBO {
    /**
     * @Fields userId 用户编号
     */
    private String userId;

    /**
     * @Fields scimUserId 4A用户ID
     */
    private String scimUserId;
    /**
     * @Fields userName 用户名
     */
    private String userName;

    /**
     * @Fields scimUserName 4A用户名
     */
    private String scimUserName;
    /**
     * @Fields fullName 姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;

    /**
     * @Fields password 密码
     */
    private String password;
    /**
     * @Fields salt 盐
     */
    private String salt;
    /**
     * @Fields deptId 部门编号
     */
    private String deptId;
    /**
     * @Fields dutyId 岗位编号
     */
    private String dutyId;
    /**
     * @Fields email 邮箱
     */
    @CryptField(type = CryptType.SM4)
    private String email;
    /**
     * @Fields mobile 手机号
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;
    /**
     * @Fields weiXin 微信号
     */
    private String weixin;
    /**
     * @Fields status 状态  b：禁用   n：正常
     */
    private String status;
    /**
     * @Fields createUserId 创建者ID
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    /**
     * @Fields lastLoginTime 上次登陆时间
     */
    private LocalDateTime lastLoginTime;
    /**
     * @Fields hasRole 是否拥有角色 Y：是  N：否
     */
    private String hasRole;
    /**
     * @Fields cstUserId 用户中心ID
     */
    private String cstUserId;
    /**
     * @Fields appId 应用ID
     */
    private String appId;
    /**
     * @Fields pwdModifyTime 密码上次修改时间
     */
    private LocalDateTime pwdModifyTime;

    /**
     * @Fields roleList 用户角色列表
     */
    private List<RoleBO> roleList;
    /**
     * @Fields deptName  部门名称
     */
    private String deptName;
    /**
     * @Fields dutyName  岗位名称
     */
    private String dutyName;
    /**
     * @Fields dataPermType  数据权限类型
     */
    private Integer dataPermType;

    /**
     * @Fields appName  归属应用名称
     */
    private String appName;

}
