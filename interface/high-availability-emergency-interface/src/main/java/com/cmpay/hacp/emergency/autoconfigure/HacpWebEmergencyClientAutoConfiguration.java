package com.cmpay.hacp.emergency.autoconfigure;

import com.cmpay.hacp.emergency.client.EmergencyHostClient;
import com.cmpay.hacp.emergency.service.SysConfigHostService;
import com.cmpay.hacp.emergency.service.impl.SysConfigHostClientServiceImpl;
import com.cmpay.hacp.system.autoconfigure.HacpWebSystemClientAutoConfiguration;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "hacp.management.discovery", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter(HacpWebSystemClientAutoConfiguration.class )
public class HacpWebEmergencyClientAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean(SysConfigHostService.class)
    @ConditionalOnBean({EmergencyHostClient.class,SubSystemCipherService.class})
    public SysConfigHostService sysConfigHostService(EmergencyHostClient emergencyHostClient, SubSystemCipherService systemCipherService) {
        log.info("init SysConfigHostClientServiceImpl");
        return new SysConfigHostClientServiceImpl(emergencyHostClient,systemCipherService);
    }
}
