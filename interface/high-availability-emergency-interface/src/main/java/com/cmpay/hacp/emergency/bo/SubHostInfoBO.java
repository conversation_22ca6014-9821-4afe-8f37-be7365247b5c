package com.cmpay.hacp.emergency.bo;

import com.cmpay.hacp.enums.HostOSEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SubHostInfoBO {

    /**
     * @Fields hostId
     */
    private Long hostId;
    /**
     * @Fields hostDesc 机器描述
     */
    private String hostDesc;
    /**
     * @Fields hostAddress 机器地址
     */
    private String hostAddress;
    /**
     * @Fields hostPort port
     */
    private Integer hostPort;
    /**
     * @Fields hostUsername 账号
     */
    private String hostUsername;
    /**
     * @Fields hostPassword 密码
     */
    private String hostPassword;
    /**
     * @Fields secretKey 密钥
     */
    private String secretKey;
    /**
     * @Fields hostAppId 应用id
     */
    private Integer hostAppId;
    /**
     * @Fields hostOS 主机系统
     */
    private HostOSEnum hostOS;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
}
