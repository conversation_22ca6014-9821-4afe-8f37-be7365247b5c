package com.cmpay.hacp.emergency.service.impl;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.emergency.bo.SubHostInfoBO;
import com.cmpay.hacp.emergency.client.EmergencyHostClient;
import com.cmpay.hacp.emergency.client.dto.HostQueryReqDTO;
import com.cmpay.hacp.emergency.service.SysConfigHostService;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
public class SysConfigHostClientServiceImpl implements SysConfigHostService {

    private final EmergencyHostClient emergencyHostClient;

    private final SubSystemCipherService subSystemCipherService;

    /**
     * 公钥配置的字典父类Type，父类字典下的字典数据必须包含publicKey的key
     */
    @Value(CommonConstant.SUB_CIPHER_TYPE_CONFIG)
    private String cipherType;

    @Override
    public List<SubHostInfoBO> queryHostList(List<Integer> hostAppIds, String workspaceId) {
        HostQueryReqDTO reqDTO = new HostQueryReqDTO();
        reqDTO.setHostAppIds(hostAppIds);
        reqDTO.setWorkspaceId(workspaceId);
        reqDTO.setCipherType(cipherType);
        DefaultRspDTO<String> response = emergencyHostClient.queryHostList(reqDTO);
        if (JudgeUtils.isNotSuccess(response.getMsgCd())){
            BusinessException.throwBusinessException(response.getMsgCd(),response.getMsgInfo());
        }
        if(response.getBody() == null){
            return new ArrayList<>();
        }
        String data = subSystemCipherService.subDecryptData(response.getBody());
        return JsonUtil.strToObject(data, new TypeReference<List<SubHostInfoBO>>(){});
    }
}
