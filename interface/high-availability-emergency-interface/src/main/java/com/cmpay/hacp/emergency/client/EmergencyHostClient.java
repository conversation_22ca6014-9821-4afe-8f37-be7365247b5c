package com.cmpay.hacp.emergency.client;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.emergency.api.HostApi;
import com.cmpay.hacp.emergency.client.dto.HostQueryReqDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "emergencyHostClient")
public interface EmergencyHostClient {

    @PostMapping(value = VersionApi.VERSION_V1+ HostApi.HOST_URL+ HostApi.HOST_SUB_QUERY_URL)
    DefaultRspDTO<String> queryHostList(@RequestBody HostQueryReqDTO reqDTO);

}
