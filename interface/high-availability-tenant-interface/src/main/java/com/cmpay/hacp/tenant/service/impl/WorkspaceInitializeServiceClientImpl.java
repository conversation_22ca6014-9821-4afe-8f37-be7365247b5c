package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.tenant.bo.WorkspaceInitializeConfigBO;
import com.cmpay.hacp.tenant.client.WorkspaceInitializeServiceClient;
import com.cmpay.hacp.tenant.dto.tenant.WorkspaceInitializeConfigRspDTO;
import com.cmpay.hacp.tenant.enums.InitializeType;
import com.cmpay.hacp.tenant.service.adapter.WorkspaceInitializeServiceAdapter;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.security.TokenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor
public class WorkspaceInitializeServiceClientImpl extends WorkspaceInitializeServiceAdapter {

    private final WorkspaceInitializeServiceClient workspaceInitializeServiceClient;

    @Override
    public List<WorkspaceInitializeConfigBO> queryInitializeConfigList(InitializeType type) {
        DefaultRspDTO<List<WorkspaceInitializeConfigRspDTO>> initializeConfig = workspaceInitializeServiceClient.queryInitializeConfig(TokenUtil.getToken(),type);
        if (JudgeUtils.isNotSuccess(initializeConfig.getMsgCd())) {
            BusinessException.throwBusinessException(initializeConfig.getMsgCd(), initializeConfig.getMsgInfo());
        }
        return BeanConvertUtil.convertList(initializeConfig.getBody(), WorkspaceInitializeConfigBO.class);
    }
}
