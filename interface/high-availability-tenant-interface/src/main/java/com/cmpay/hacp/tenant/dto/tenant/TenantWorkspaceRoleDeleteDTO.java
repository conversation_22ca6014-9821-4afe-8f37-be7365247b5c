package com.cmpay.hacp.tenant.dto.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除项目角色dto
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
@ApiModel(description = "删除项目角色")
public class TenantWorkspaceRoleDeleteDTO {

    /**
     * @Fields workspaceRoleId 项目角色ID
     */
    @ApiModelProperty(value = "项目角色ID", required = true, example = "项目角色ID")
    @NotBlank(message = "HAC00021")
    private String workspaceRoleId;
}
