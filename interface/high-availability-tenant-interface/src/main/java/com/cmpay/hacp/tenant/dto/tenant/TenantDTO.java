/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.dto.tenant;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "租户详情")
public class TenantDTO {
    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
    private String tenantId;

    /**
     * @Fields tenantName 租户名称
     */
    @ApiModelProperty(value = "租户名称", required = true, example = "租户名称")
    private String tenantName;
    /**
     * @Fields deptId 部门编号
     */
    @ApiModelProperty(value = "部门编号", required = true, example = "888888")
    private String deptId;
    /**
     * @Fields deptName 部门名称
     */
    @ApiModelProperty(value = "部门名称", required = false, example = "部门名称")
    private String deptName;

    /**
     * @Fields createUser 创建者
     */
    @ApiModelProperty(value = "创建者", required = true, example = "JK8888")
    private String createUser;

    /**
     * @Fields createTime 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = true, example = "2023-08-01 09:51:39")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * @Fields updateUser 更新者
     */
    @ApiModelProperty(value = "更新者", required = true, example = "JK8888")
    private String updateUser;

    /**
     * @Fields updateTime 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true, example = "2023-08-01 09:51:39")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty(value = "备注信息", required = true, example = "备注信息")
    private String remarks;

    /**
     * @Fields status 删除标记
     */
    @ApiModelProperty(value = "删除标记", required = true, example = "0")
    private String status;

    /**
     * @Fields tenantUserType 租户成员类型
     */
    @ApiModelProperty(value = "租户成员类型", required = true, example = "1")
    private String tenantUserType;

    /**
     * @Fields userId 租户管理员ID
     */
    @ApiModelProperty(value = "租户管理员ID", required = true, example = "MON000001")
    private String userId;

    /**
     * @Fields userName 租户管理员用户名
     */
    @ApiModelProperty(value = "租户管理员用户名", required = true, example = "JK8888")
    private String userName;

    /**
     * @Fields fullName 租户管理员姓名
     */
    @ApiModelProperty(value = "租户管理员姓名", required = true, example = "张三")
    private String fullName;

    /**
     * @Fields email 租户管理员邮箱
     */
    @ApiModelProperty(value = "租户管理员邮箱", required = true, example = "<EMAIL>")
    private String email;

    /**
     * @Fields mobile 租户管理员手机
     */
    @ApiModelProperty(value = "租户管理员手机", required = true, example = "18888888888")
    private String mobile;
    /**
     * @Fields userStatus 租户管理员用户状态
     */
    @ApiModelProperty(value = "租户管理员用户状态", required = true, example = "ENABLE")
    private String userStatus;

    /**
     * @Fields workspaces 项目列表
     */
    @ApiModelProperty(value = "项目列表", required = false)
    private List<TenantWorkspaceDTO> workspaces;
}
