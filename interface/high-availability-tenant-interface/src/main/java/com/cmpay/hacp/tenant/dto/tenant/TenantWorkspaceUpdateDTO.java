/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
@ApiModel(description = "修改项目")
public class TenantWorkspaceUpdateDTO {
    /**
     * @Fields id 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true, example = "6543215dc433428c84ff434379374157")
    @NotBlank(message = "HAC00009")
    private String id;
    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "1234565dc433428c84ff434379374157")
    @NotBlank(message = "HAC00006")
    private String workspaceId;
    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "4321565dc433428c84ff434379374157")
    @NotBlank(message = "HAC00004")
    private String tenantId;
    /**
     * @Fields workspaceName 项目名称
     */
    @ApiModelProperty(value = "项目名称", required = true, example = "项目名称")
    @NotBlank(message = "HAC00007")
    private String workspaceName;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty(value = "备注信息", required = false, example = "备注信息")
    private String remarks;

    /**
     * @Fields userId 租户管理员ID
     */
    @ApiModelProperty(value = "项目管理员ID", required = true, example = "MON000001")
    private String userId;

    /**
     * @Fields profile 所属环境
     */
    @ApiModelProperty(value = "所属环境", required = true, example = "prd")
    private String profile;


}
