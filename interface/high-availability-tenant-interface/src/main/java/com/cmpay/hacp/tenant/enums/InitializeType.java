package com.cmpay.hacp.tenant.enums;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.valuable.Valuable;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Getter
@Slf4j
public enum InitializeType implements Valuable<Integer> {

    PLUGIN(1, "plugin"),
    RULE(2, "rule"),;

    @JsonValue
    private final Integer code;
    private final String desc;

    InitializeType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, InitializeType> ENUM_MAP = Arrays.stream(InitializeType.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static InitializeType getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }

    @Override
    public Integer getValue() {
        return this.code;
    }
}
