/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "批量删除租户")
public class TenantDeletesDTO {

    /**
     * @Fields tenantIds 租户ID列表
     */
    @ApiModelProperty(value = "租户ID列表", required = true)
    @NotEmpty(message = "HAC00004")
    private List<String> tenantIds;

}
