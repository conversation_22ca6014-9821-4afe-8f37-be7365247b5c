package com.cmpay.hacp.tenant.client;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.tenant.api.WorkspaceApi;
import com.cmpay.hacp.tenant.dto.tenant.WorkspaceInitializeConfigRspDTO;
import com.cmpay.hacp.tenant.enums.InitializeType;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "workspaceInitializeServiceClient")
public interface WorkspaceInitializeServiceClient {

    String CONFIG_API = "/initialize/config";

    @GetMapping(value = VersionApi.VERSION_V1+WorkspaceApi.WORKSPACE_API + CONFIG_API)
    DefaultRspDTO<List<WorkspaceInitializeConfigRspDTO>> queryInitializeConfig(@RequestHeader("token")String token, @RequestParam("type") InitializeType type);
}
