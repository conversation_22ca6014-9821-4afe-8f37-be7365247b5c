package com.cmpay.hacp.tenant.service.adapter;

import com.cmpay.hacp.tenant.bo.WorkspaceInitializeConfigBO;
import com.cmpay.hacp.tenant.enums.InitializeType;
import com.cmpay.hacp.tenant.service.WorkspaceInitializeService;

import java.util.List;

public class WorkspaceInitializeServiceAdapter implements WorkspaceInitializeService {

    @Override
    public List<WorkspaceInitializeConfigBO> queryInitializeConfigList(InitializeType type) {
        return null;
    }
}
