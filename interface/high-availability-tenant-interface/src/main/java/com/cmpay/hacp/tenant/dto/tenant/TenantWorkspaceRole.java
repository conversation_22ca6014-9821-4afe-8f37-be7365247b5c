package com.cmpay.hacp.tenant.dto.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目角色详情DTO
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
@ApiModel(description = "项目角色摘要详情")
public class TenantWorkspaceRole {

    /**
     * @Fields workspaceRoleId 项目角色ID
     */
    @ApiModelProperty(value = "项目角色ID", required = true, example = "项目角色ID")
    private String workspaceRoleId;

    /**
     * @Fields workspaceRoleName 项目角色名称
     */
    @ApiModelProperty(value = "项目角色名称", required = true, example = "项目角色名称")
    private String workspaceRoleName;

    /**
     * @Fields workspaceRoleType 项目角色类型
     */
    @ApiModelProperty(value = "项目角色类型", required = true, example = "项目角色类型")
    private String workspaceRoleType;

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "项目ID")
    private String workspaceId;

}
