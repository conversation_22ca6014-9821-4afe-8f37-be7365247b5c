package com.cmpay.hacp.tenant.api;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.constant.TenantConstant;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

public interface WorkspaceApi {

    String WORKSPACE_API = "/tenant/workspace";

    String WORKSPACE_INITIALIZATION_API = VersionApi.VERSION_V1 + WORKSPACE_API + "/initialization";

    /**
     * 初始化项目
     * @return
     */
    @PostMapping(WORKSPACE_INITIALIZATION_API)
    DefaultRspDTO<NoBody> initialization(@RequestHeader(TenantConstant.WORKSPACE_ID)String workspaceId);
}
