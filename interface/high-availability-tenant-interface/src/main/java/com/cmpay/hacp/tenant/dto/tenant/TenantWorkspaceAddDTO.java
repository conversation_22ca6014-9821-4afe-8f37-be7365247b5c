/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "新增项目")
public class TenantWorkspaceAddDTO {

    /**
     * @Fields workspaceName 项目名称
     */
    @ApiModelProperty(value = "项目名称", required = true, example = "项目名称")
    @NotBlank(message = "HAC00007")
    private String workspaceName;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty(value = "备注信息", required = false, example = "备注信息")
    private String remarks;

    /**
     * @Fields tenantId 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
    @NotBlank(message = "HAC00002")
    private String tenantId;

    /**
     * @Fields userId 租户管理员ID
     */
    @ApiModelProperty(value = "项目管理员ID", required = true, example = "MON000001")
    @NotBlank(message = "HAC00008")
    private String userId;

}
