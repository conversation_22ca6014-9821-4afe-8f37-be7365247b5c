/*
 * @ClassName TenantDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "删除项目")
public class TenantWorkspaceDeleteDTO {

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "1234565dc433428c84ff434379374157")
    @NotBlank(message = "HAC00006")
    private String workspaceId;

}
