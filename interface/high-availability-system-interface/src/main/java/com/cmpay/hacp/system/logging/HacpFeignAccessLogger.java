package com.cmpay.hacp.system.logging;

import com.cmpay.lemon.common.codec.ObjectEncoder;
import com.cmpay.lemon.framework.log.RequestInfo;
import com.cmpay.lemon.framework.log.ResponseInfo;
import com.cmpay.lemon.framework.springcloud.fegin.logging.FeignAccessLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class HacpFeignAccessLogger extends FeignAccessLogger {

    private static final Logger logger = LoggerFactory.getLogger(HacpFeignAccessLogger.class);

    private static final Set<String> IGNORE_RSP_BODY_URL = new HashSet<>();

    public HacpFeignAccessLogger(ObjectEncoder objectEncoder, Environment environment) {
        super(objectEncoder);
        Binder.get(environment).bind("hacp.web.admin.log.ignore-rsp-body-url", String[].class)
                .ifBound(urls -> {
                    IGNORE_RSP_BODY_URL.addAll(Arrays.asList(urls));
                });
    }

    @Override
    public <T, REQ extends RequestInfo<T>> void request(REQ requestInfo) {
        super.request(requestInfo);
    }

    @Override
    public <T, RSP extends ResponseInfo<T>> void response(RSP responseInfo) {
        if(null != responseInfo.getUri() && IGNORE_RSP_BODY_URL.contains(responseInfo.getUri())){
            responseInfo.setResult(null);
        }
        super.response(responseInfo);
    }
}
