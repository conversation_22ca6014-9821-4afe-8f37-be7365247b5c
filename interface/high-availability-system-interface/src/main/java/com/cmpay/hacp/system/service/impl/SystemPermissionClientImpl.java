package com.cmpay.hacp.system.service.impl;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.client.SystemPermissionClient;
import com.cmpay.hacp.system.service.adapter.SystemPermissionServiceAdapter;
import com.cmpay.hacp.utils.security.TokenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemPermissionClientImpl extends SystemPermissionServiceAdapter {

    private final SystemPermissionClient systemPermissionClient;

    @Override
    // @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_MENU", key = "'MENU:'+ #applicationName+':'+#userId", unless = "#result == null")
    public PermMenuTreeMetaBO queryUserPermissions(String userId, String applicationName) {
        GenericRspDTO<PermMenuTreeMetaBO> genericRspDTO = systemPermissionClient.queryUserPermissions(TokenUtil.getToken());
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            BusinessException.throwBusinessException(genericRspDTO.getMsgCd(),genericRspDTO.getMsgInfo());
        }
        return genericRspDTO.getBody();
    }
}
