package com.cmpay.hacp.system.client;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemPermissionApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.logging.FeignIgnore;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "systemPermissionClient" )
public interface SystemPermissionClient {


    /**
     * 查询用户菜单树形结构
     *
     * @return
     */
    @LogNoneRecord
    @FeignIgnore(responseBody = true)
    @GetMapping(VersionApi.VERSION_V1 + SystemPermissionApi.PERMISSION_LIST_DETAIL)
    GenericRspDTO<PermMenuTreeMetaBO> queryUserPermissions(@RequestHeader(CommonConstant.TOKEN) String token);

    /**
     * 查询用户所有权限标识
     *
     * @return
     */
    @LogNoneRecord
    @FeignIgnore(responseBody = true)
    @GetMapping(VersionApi.VERSION_V1 + SystemPermissionApi.PERMISSION_LIST)
    GenericRspDTO<List<String>> getUserPermissions(@RequestHeader(CommonConstant.TOKEN) String token);
}
