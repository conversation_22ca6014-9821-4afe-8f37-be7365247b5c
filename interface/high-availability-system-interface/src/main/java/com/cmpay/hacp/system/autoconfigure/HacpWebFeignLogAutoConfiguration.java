package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.logging.HacpFeignAccessLogger;
import com.cmpay.lemon.framework.autoconfigure.feign.FeignAutoConfiguration;
import com.cmpay.lemon.framework.log.AccessLogger;
import com.cmpay.lemon.framework.log.LoggingCodec;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
@AutoConfigureBefore(FeignAutoConfiguration.class)
public class HacpWebFeignLogAutoConfiguration {

    @Bean
    public AccessLogger feignAccessLogger(LoggingCodec loggingCodec, Environment environment){
        return new HacpFeignAccessLogger(loggingCodec,environment);
    }
}
