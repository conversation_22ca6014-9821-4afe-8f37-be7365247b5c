package com.cmpay.hacp.extend.container.service.impl;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.extend.container.bo.ContainerConfigBO;
import com.cmpay.hacp.extend.container.client.HacpContainerClient;
import com.cmpay.hacp.extend.container.client.dto.EmergencyContainerQueryReqDTO;
import com.cmpay.hacp.extend.container.service.adapter.HacpContainerServiceAdapter;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;

@RequiredArgsConstructor
public class HacpContainerServiceClientImpl extends HacpContainerServiceAdapter {

    private final HacpContainerClient hacpContainerClient;

    private final SubSystemCipherService subSystemCipherService;

    /**
     * 公钥配置的字典父类Type，父类字典下的字典数据必须包含publicKey的key
     */
    @Value(CommonConstant.SUB_CIPHER_TYPE_CONFIG)
    private String cipherType;

    @Override
    public ContainerConfigBO getDecryptDetailInfo(ContainerConfigBO bo) {
        EmergencyContainerQueryReqDTO query = new EmergencyContainerQueryReqDTO();
        query.setCipherType(cipherType);
        query.setWorkspaceId(bo.getWorkspaceId());
        DefaultRspDTO<String> response =  hacpContainerClient.getSubDetailInfo(query,bo.getWorkspaceId());
        if (JudgeUtils.isNotSuccess(response.getMsgCd())){
            BusinessException.throwBusinessException(response.getMsgCd(),response.getMsgInfo());
        }
        String data = subSystemCipherService.subDecryptData(response.getBody());
        return JsonUtil.strToObject(data,ContainerConfigBO.class);
    }
}
