package com.cmpay.hacp.extend.container.bo;

import com.cmpay.hacp.capable.TenantCapable;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:25
 * @since 1.0.0
 */

@Data
public class ContainerConfigBO implements TenantCapable {

    /**
     * @Fields containerId id
     */
    private Integer containerId;
    /**
     * @Fields clientId 客户端id
     */
    private String clientId;
    /**
     * @Fields clientSecret 密钥
     */
    private String clientSecret;
    /**
     * @Fields grantType 授权类型
     */
    private String grantType;
    /**
     * @Fields username 账号
     */
    private String username;
    /**
     * @Fields password 密码
     */
    private String password;
    /**
     * @Fields rootUrl 端点根url
     */
    private String rootUrl;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields cloudWorkspaceId 云平台项目空间ID
     */
    private String cloudWorkspaceId;
    /**
     * @Fields cloudClusters 操作员
     */
    private String cloudClusters;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    private String uuid;

    private List<String> cloudClusterList;

}
