package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.util.List;

/**
 * 探针
 */
@Data
public class Probe {
    
    /**
     * HTTP GET探针
     */
    private HTTPGetAction httpGet;
    
    /**
     * TCP Socket探针
     */
    private TCPSocketAction tcpSocket;
    
    /**
     * 执行探针
     */
    private ExecAction exec;
    
    /**
     * 初始延迟秒数
     */
    private Integer initialDelaySeconds;
    
    /**
     * 超时秒数
     */
    private Integer timeoutSeconds;
    
    /**
     * 周期秒数
     */
    private Integer periodSeconds;
    
    /**
     * 成功阈值
     */
    private Integer successThreshold;
    
    /**
     * 失败阈值
     */
    private Integer failureThreshold;
}

/**
 * HTTP GET动作
 */
@Data
class HTTPGetAction {
    /**
     * 路径
     */
    private String path;
    
    /**
     * 端口
     */
    private String port;
    
    /**
     * 协议
     */
    private String scheme;
    
    /**
     * HTTP头
     */
    private List<HTTPHeader> httpHeaders;
}

/**
 * HTTP头
 */
@Data
class HTTPHeader {
    /**
     * 头名称
     */
    private String name;
    
    /**
     * 头值
     */
    private String value;
}

/**
 * TCP Socket动作
 */
@Data
class TCPSocketAction {
    /**
     * 端口
     */
    private String port;
}

/**
 * 执行动作
 */
@Data
class ExecAction {
    /**
     * 命令
     */
    private List<String> command;
}
