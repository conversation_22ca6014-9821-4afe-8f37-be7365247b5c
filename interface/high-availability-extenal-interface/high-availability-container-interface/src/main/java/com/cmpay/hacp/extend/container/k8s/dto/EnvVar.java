package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

/**
 * 环境变量
 */
@Data
public class EnvVar {
    /**
     * 变量名
     */
    private String name;
    
    /**
     * 变量值
     */
    private String value;
    
    /**
     * 值来源
     */
    private EnvVarSource valueFrom;
}

/**
 * 环境变量来源
 */
@Data
class EnvVarSource {
    /**
     * Secret键引用
     */
    private SecretKeyRef secretKeyRef;
    
    /**
     * ConfigMap键引用
     */
    private ConfigMapKeyRef configMapKeyRef;
    
    /**
     * 字段引用
     */
    private FieldRef fieldRef;
}

/**
 * Secret键引用
 */
@Data
class SecretKeyRef {
    /**
     * Secret名称
     */
    private String name;
    
    /**
     * 键名
     */
    private String key;
}

/**
 * ConfigMap键引用
 */
@Data
class ConfigMapKeyRef {
    /**
     * ConfigMap名称
     */
    private String name;
    
    /**
     * 键名
     */
    private String key;
}

/**
 * 字段引用
 */
@Data
class FieldRef {
    /**
     * API版本
     */
    private String apiVersion;
    
    /**
     * 字段路径
     */
    private String fieldPath;
}
