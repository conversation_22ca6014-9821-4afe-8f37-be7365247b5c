package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

/**
 * 卷
 */
@Data
public class Volume {

    /**
     * 卷名称
     */
    private String name;

    /**
     * 主机路径
     */
    private HostPath hostPath;

    /**
     * 空目录
     */
    private EmptyDir emptyDir;

    /**
     * ConfigMap
     */
    private ConfigMapVolumeSource configMap;

    /**
     * Secret
     */
    private SecretVolumeSource secret;
}

/**
 * 主机路径
 */
@Data
class HostPath {
    /**
     * 路径
     */
    private String path;
    
    /**
     * 类型
     */
    private String type;
}

/**
 * 空目录
 */
@Data
class EmptyDir {
    /**
     * 大小限制
     */
    private String sizeLimit;
    
    /**
     * 存储介质
     */
    private String medium;
}

/**
 * ConfigMap卷源
 */
@Data
class ConfigMapVolumeSource {
    /**
     * ConfigMap名称
     */
    private String name;
}

/**
 * Secret卷源
 */
@Data
class SecretVolumeSource {
    /**
     * Secret名称
     */
    private String secretName;
}
