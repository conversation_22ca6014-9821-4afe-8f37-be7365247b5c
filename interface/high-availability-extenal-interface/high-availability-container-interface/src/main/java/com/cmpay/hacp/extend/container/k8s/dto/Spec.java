package com.cmpay.hacp.extend.container.k8s.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class Spec {

    private String manager;

    private TemplateSpec template;

    private Placement placement;

    private List<String> finalizers;

    /* ======== pod */
    /**
     * 卷列表
     */
    private List<Volume> volumes;

    /**
     * 容器列表
     */
    private List<Container> containers;

    /**
     * 重启策略
     */
    private String restartPolicy;

    /**
     * 终止宽限期（秒）
     */
    private Integer terminationGracePeriodSeconds;

    /**
     * DNS策略
     */
    private String dnsPolicy;

    /**
     * 服务账户名称
     */
    private String serviceAccountName;

    /**
     * 服务账户
     */
    private String serviceAccount;

    /**
     * 安全上下文
     */
    private Map<String, Object> securityContext;

    /**
     * 镜像拉取密钥
     */
    private List<ImagePullSecret> imagePullSecrets;

    /**
     * 调度器名称
     */
    private String schedulerName;
    /* =========== pod */
    // 还有个overrides属性


    /**
     * 镜像拉取密钥
     */
    @Data
    public static class ImagePullSecret {
        /**
         * 密钥名称
         */
        private String name;
    }
}