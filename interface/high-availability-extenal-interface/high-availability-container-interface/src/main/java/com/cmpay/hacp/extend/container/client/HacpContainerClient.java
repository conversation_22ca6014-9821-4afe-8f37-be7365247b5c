package com.cmpay.hacp.extend.container.client;

import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.constant.TenantConstant;
import com.cmpay.hacp.extend.container.api.ContainerApi;
import com.cmpay.hacp.extend.container.client.dto.EmergencyContainerQueryReqDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform}", url = "${hacp.management.discovery.url:}", contextId = "hacpContainerClient")
public interface HacpContainerClient {

    @PostMapping(value = VersionApi.VERSION_V1+ContainerApi.CONTAINER+ContainerApi.SUB_INFO)
    DefaultRspDTO<String> getSubDetailInfo(@RequestBody EmergencyContainerQueryReqDTO bo,@RequestHeader(TenantConstant.WORKSPACE_ID)String workspaceId);
}
