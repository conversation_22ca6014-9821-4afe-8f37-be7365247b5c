package com.cmpay.hacp.extend.container.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;

@AllArgsConstructor
@Data
public class PatchDeployBO {
    private String workspaceId;
    private String cluster;
    private String namespace;
    private String deployment;
    private HashMap<String, Object> body = new HashMap<>();

    static class PatchDeployBuilderBO {
        private String workspaceId;
        private String cluster;
        private String namespace;
        private String deployment;
        private final HashMap<String, Object> body = new HashMap<>();

        private PatchDeployBuilderBO() {}

        public static PatchDeployBuilderBO builder() {
            return new PatchDeployBuilderBO();
        }

        public PatchDeployBuilderBO WorkspaceId(String workspaceId) {
            this.workspaceId = workspaceId;
            return this;
        }

        public PatchDeployBuilderBO Cluster(String cluster) {
            this.cluster = cluster;
            return this;
        }

        public PatchDeployBuilderBO Namespace(String namespace) {
            this.namespace = namespace;
            return this;
        }

        public PatchDeployBuilderBO Deployment(String deployment) {
            this.deployment = deployment;
            return this;
        }

        public PatchDeployBuilderBO replicas(Integer replicas) {
            body.computeIfPresent("spec", (k, v) -> {
                ((HashMap<String, Object>) v).computeIfPresent("replicas", (k1, v1) -> replicas);
                return v;
            });
            body.computeIfAbsent("spec", k -> {
                HashMap<String, Object> spec = new HashMap<>();
                spec.put("replicas", replicas);
                return spec;
            });
            return this;
        }
        public PatchDeployBO build(){
            if (workspaceId == null || cluster == null || namespace == null || deployment == null) {
                throw new IllegalArgumentException("workspaceId, cluster, namespace, and deployment must be set");
            }
            return new PatchDeployBO(workspaceId, cluster, namespace, deployment, body);
        }
    }
}
