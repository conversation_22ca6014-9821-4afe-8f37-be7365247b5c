dependencies {
    optional('com.cmpay:lemon-framework-core')
    optional("cn.hutool:hutool-all:${hutoolVersion}")
    optional("com.cmpay:lemon-common-codec")
    optional("org.apache.commons:commons-lang3")
    optional("org.apache.tomcat.embed:tomcat-embed-core")
    optional("com.cmpay:lemon-smx")
    optional("org.bouncycastle:bcpkix-jdk15on:1.64")
    optional("com.cmpay:lemon-framework-starter-security")
    optional("com.fasterxml.jackson.core:jackson-databind")
    optional("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    optional("org.apache.httpcomponents:httpclient")

    optional("com.cmpay:lemon-common-io")
    optional 'org.apache.poi:poi'
    optional 'org.apache.poi:poi-ooxml'
}
