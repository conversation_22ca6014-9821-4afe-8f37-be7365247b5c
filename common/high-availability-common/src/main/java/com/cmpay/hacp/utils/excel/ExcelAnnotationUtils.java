package com.cmpay.hacp.utils.excel;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ReflectUtil;
import com.cmpay.hacp.annotation.excel.DropDownSetValue;
import com.cmpay.hacp.enums.StringArrayValuable;
import com.cmpay.hacp.excel.metadata.DropDownSetValueMetadata;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/12/05 9:15
 * @since 1.0.0
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExcelAnnotationUtils {

    /**
     * 获取下拉框值
     *
     * @param clazz
     * @return
     */
    public static Map<Integer, DropDownSetValueMetadata> getDropDownSetValue(Class<?> clazz) {
        Field[] fields = ReflectUtil.getFields(clazz);
        Map<Integer, DropDownSetValueMetadata> colMap = new HashMap<>(8);
        for (Field field : fields) {
            DropDownSetValue annotation = AnnotationUtil.getAnnotation(field, DropDownSetValue.class);

            Opt.ofNullable(annotation).ifPresent(p -> {
                String[] values;
                Class<? extends StringArrayValuable> arrayClass = p.value();
                StringArrayValuable[] enumConstants = arrayClass.getEnumConstants();
                if (enumConstants != null) {
                    values = enumConstants[0].array();
                } else {
                    values = ReflectUtil.newInstance(arrayClass).array();
                }
                Assert.isFalse(colMap.containsKey(p.colIndex()), () -> new RuntimeException("下拉框所属列已存在；" + p.colIndex()));
                colMap.put(p.colIndex(),
                        DropDownSetValueMetadata.builder()
                                .values(values)
                                .rowIndex(p.rowIndex())
                                .colIndex(p.colIndex())
                                .isNewSheetDropDown(p.isNewSheetDropDown())
                                .sheetName(p.sheetName())
                                .validation(p.validation())
                                .build());
            });
        }
        return colMap;
    }

}
