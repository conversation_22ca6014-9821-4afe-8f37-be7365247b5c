package com.cmpay.hacp.enums;

public enum StatusConstans {
    DISABLE("DISABLE"),
    ENABLE("ENABLE"),
    /**
     * 用户具有获取菜单权限校色
     */
    HAS_ROLE("Y"),
    /**
     * 用户不具有获取菜单权限校色
     */
    NOT_HAS_ROLE("N"),
    /**
     * 需要提醒用户修改密码
     */
    REMIND_TO_MODIFY_PWD("remind"),
    /**
     * 不需要修改密码和提醒
     */
    PWD_NO_NEED_TO_MODIFY("unexpired"),
    /**
     * 密码过期，强制要求用户修改密码
     */
    PWD_MUST_TO_MODIFY("expired");


    String value;

    StatusConstans(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
