package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemDepartmentApi extends BaseApi {

    /**
     * 删除部门
     */
    String DELETE = BASE_SYSTEM + "/department/delete";

    /**
     * 添加部门
     */
    String ADD = BASE_SYSTEM + "/department/add";

    /**
     * 修改部门
     */
    String UPDATE = BASE_SYSTEM + "/department/update";

    /**
     * 查询部门列表
     */
    String GET_DEPARTMENT_LIST = BASE_SYSTEM + "/department/listAll";
    /**
     * 查询部门信息
     */
    String GET_DEPARTMENT_INFO_BY_ID = BASE_SYSTEM + "/department/info/{id}";

}
