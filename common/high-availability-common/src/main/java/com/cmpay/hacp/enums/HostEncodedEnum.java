package com.cmpay.hacp.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/08/21 14:09
 * @since 1.0.0
 */

@Getter
@AllArgsConstructor
public enum HostEncodedEnum implements KeyValueValuable,StringArrayValuable {

    UTF_8("UTF-8"),
    GBK("GBK"),
    ;
    private String value;

    @JsonCreator
    public static HostEncodedEnum getEnum(String value) {
        return Arrays.stream(HostEncodedEnum.values()).filter(f->f.value.equals(value)||f.name().equals(value)).findAny().get();
    }

    @Override
    public KeyValue generateKeyValue() {
        KeyValue keyValue = new KeyValue();
        keyValue.setValue(this.getValue());
        keyValue.setKey(this.name());
        return keyValue;
    }

    @Override
    public String[] array() {
        return Arrays.stream(values()).map(HostEncodedEnum::getValue).toArray(String[]::new);
    }

}
