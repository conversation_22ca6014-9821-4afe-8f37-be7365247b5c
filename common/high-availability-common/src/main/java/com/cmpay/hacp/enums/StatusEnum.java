package com.cmpay.hacp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StatusEnum {

    ENABLE("0", 0,Byte.valueOf("0"),"DISABLE",true),
    DISABLE("1",1,Byte.valueOf("1"), "ENABLE",false),
    MIDDLE("2", 2,Byte.valueOf("2"),"MIDDLE",null);

    private String value;

    private Integer intValue;

    private Byte byteValue;

    private String describe;

    private Boolean booleanValue;

}
