package com.cmpay.hacp.utils.http;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;


/**
 * <AUTHOR>
 */
public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    public static RestTemplate getInstance(int connectTimeout, int readTimeout, CloseableHttpClient httpClient) {
        return new RestTemplate(HttpClientUtil.generateHttpRequestFactory(connectTimeout, readTimeout, httpClient));
    }

    public static RestTemplate getInstance(int connectTimeout, int readTimeout, boolean enableSslCheck) {
        return new RestTemplate(HttpClientUtil.generateHttpRequestFactory(connectTimeout, readTimeout, enableSslCheck));
    }

    public static HttpComponentsClientHttpRequestFactory generateHttpRequestFactory(int connectTimeout, int readTimeout, boolean enableSslCheck) {
        HttpClientBuilder httpClientBuilder = HttpClients.custom();
        if (!enableSslCheck) {
            TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
            SSLContext sslContext = null;
            try {
                sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
            } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
                logger.error("generateHttpRequestFactory error {}", e.getMessage());
            }
            SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
            httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
        }
        CloseableHttpClient httpClient = httpClientBuilder.build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        factory.setHttpClient(httpClient);
        return factory;
    }

    public static HttpComponentsClientHttpRequestFactory generateHttpRequestFactory(int connectTimeout, int readTimeout, CloseableHttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        factory.setHttpClient(httpClient);
        return factory;
    }
}
