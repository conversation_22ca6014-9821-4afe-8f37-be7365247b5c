package com.cmpay.hacp.excel.handle.sheet;


import com.cmpay.hacp.excel.metadata.DropDownSetValueMetadata;
import com.cmpay.hacp.utils.excel.ExcelAnnotationUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 自定义工作表下拉处理拦截器
 *
 * <AUTHOR>
 * @create 2022/12/05 9:15
 * @since 1.0.0
 */

public class CustomDropDownSheetWriteHandler implements SheetWriteHandler {
    private Map<Integer, DropDownSetValueMetadata> colMap;

    /**
     * @param clazz 带有下拉注解的对象
     */
    public CustomDropDownSheetWriteHandler(Class<?> clazz) {
        this.colMap = ExcelAnnotationUtils.getDropDownSetValue(clazz);
    }

    public void afterSheetCreate(Workbook workbook, Sheet sheet) {
        // 这里可以对cell进行任何操作
        // Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // k 为存在下拉数据集的单元格下表, v为下拉数据集
        colMap.forEach((k, v) -> {
            // 存在\"的下拉会导致插入失败
            for (int i = 0; i < v.getValues().length; i++) {
                String value = v.getValues()[i];
                v.getValues()[i] = StringUtils.replace(value, "\"", "");
            }

            // 需要新建sheet来存放下来框的原因是下拉数据多的时候会下载报错
            // 如果是新的sheet来存放下拉框
            if (v.getIsNewSheetDropDown() != null && v.getIsNewSheetDropDown()) {

                // The implementation of the drop-down box provided by the excel manager is suitable for small data volumes
                // Province and city drop-down box

                // Province and city drop-down box
                String[] strings = v.getValues();
                // Create a sheet to break through the limit of 255 in the drop-down box
                //Get a workbook
                // Workbook workbook = writeWorkbookHolder.getWorkbook();
                //Define the name of the sheet
                String sheetName = v.getSheetName();
                //1. Create a hidden sheet named dropDownSheet
                Sheet dropDownSheet = workbook.createSheet(sheetName);
                // set hidden
                // workbook.setSheetHidden(1,true);
                //2. Circular assignment (in order to prevent the number of lines in the drop-down box from corresponding to the number of lines in the hidden field, add the hidden field to the end line)
                for (int i = 0, length = strings.length; i < length; i++) {
                    // i: indicates the number of rows you start with 0 indicates the number of columns you start with
                    dropDownSheet.createRow(i).createCell(0).setCellValue(strings[i]);
                }
                Name category1Name = workbook.createName();
                category1Name.setNameName(sheetName);
                //4 $A$1:$A$N means to get N rows of drop-down data starting from column A and row 1
                category1Name.setRefersToFormula(sheetName + "!$A$1:$A$" + (strings.length));
                //5 Reference the sheet you just set to your drop-down list
                CellRangeAddressList addressList = new CellRangeAddressList(v.getRowIndex(), 65535, v.getColIndex(), v.getColIndex());
                DataValidationConstraint constraint8 = helper.createFormulaListConstraint(sheetName);
                validation(sheet, v, helper, constraint8, addressList);
            } else {
                // 设置下拉单元格的首行, 末行, 首列, 末列
                CellRangeAddressList rangeList = new CellRangeAddressList(v.getRowIndex(), 65535, v.getColIndex(), v.getColIndex());
                // 下拉列表约束数据
                DataValidationConstraint constraint = helper.createExplicitListConstraint(v.getValues());
                // 设置约束
                validation(sheet, v, helper, constraint, rangeList);
            }
        });
    }

    private static void validation(Sheet sheet,
            DropDownSetValueMetadata v,
            DataValidationHelper helper,
            DataValidationConstraint constraint8,
            CellRangeAddressList addressList) {
        DataValidation dataValidation3 = helper.createValidation(constraint8, addressList);
        dataValidation3.setErrorStyle(DataValidation.ErrorStyle.STOP);
        dataValidation3.setSuppressDropDownArrow(true);
        dataValidation3.setShowErrorBox(false);
        dataValidation3.createErrorBox("提示", "请输入选择正确的值");
        if(v.getValidation()){
            dataValidation3.setShowErrorBox(true);
        }
        sheet.addValidationData(dataValidation3);
    }
}
