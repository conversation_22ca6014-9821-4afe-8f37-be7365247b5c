package com.cmpay.hacp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum TaskLogStatusEnum {

    COMPLETE("0","完成",BpmHighlightStyleEnum.COMPLETE),
    ONGOING("1","进行中",BpmHighlightStyleEnum.ONGOING),
    FAIL("2","失败",BpmHighlightStyleEnum.CANCEL),
    SKIP("3","失败并跳过",BpmHighlightStyleEnum.FAIL_SKIP),
    CANCEL("4","取消",BpmHighlightStyleEnum.CANCEL),
    REJECT("5","拒绝",BpmHighlightStyleEnum.REJECT),

    ;
    private final String code;

    private final String desc;

    private final BpmHighlightStyleEnum styleCode;

    private static final Map<String,TaskLogStatusEnum> MAP_STATUS= Arrays.stream(TaskLogStatusEnum.values()).collect(Collectors.toMap(TaskLogStatusEnum::getCode,v->v));

    public static String getStatus(Object code,String other) {
        return Arrays.stream(TaskLogStatusEnum.values()).filter(f->f.getCode().equals(String.valueOf(code)))
                .map(TaskLogStatusEnum::getStyleCode).map(m->m.getStatus()).findFirst().orElse(other);
    }

    public static TaskLogStatusEnum getEnumByCode(String code) {
        return MAP_STATUS.get(code);
    }
}
