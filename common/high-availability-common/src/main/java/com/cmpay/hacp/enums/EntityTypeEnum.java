package com.cmpay.hacp.enums;

import com.cmpay.lemon.framework.valuable.Valuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/06 14:16
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum EntityTypeEnum implements Valuable<String> {
    HOST("emergency_host","主机管理使用", Collections.singletonList("HOST")),
    TASK("hacp_emergency_task","原子任务使用", Collections.singletonList("TASK")),
    CASE("hacp_emergency_case","预案管理使用", Collections.singletonList("CASE")),
    TASK_OR_CASE("task_or_case","对象标签",Arrays.asList("TASK","CASE")),
    PROCCESS("emergency_process","流程管理使用", Collections.singletonList("PROCESS")),
    ;

    private final String value;

    private final String desc;

    private final List<String> entityType;

}
