package com.cmpay.hacp.enums;

public enum ByteDispatchEnum {
    ROUTINE(Byte.valueOf("0"), "常规调度"),
    EMERGENCY(Byte.valueOf("1"), "应急调度");

    private Byte value;

    private String describe;

    ByteDispatchEnum(Byte value, String describe) {
        this.value = value;
        this.describe = describe;
    }

    public Byte getValue() {
        return value;
    }

    public String getDescribe() {
        return this.describe;
    }
}
