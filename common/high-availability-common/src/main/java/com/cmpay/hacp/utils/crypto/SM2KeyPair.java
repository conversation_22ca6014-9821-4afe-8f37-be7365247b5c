package com.cmpay.hacp.utils.crypto;

import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;

/**
 * SM2密钥对Bean
 *
 * <AUTHOR>
 * @date 2020-05-10
 */
public class SM2KeyPair {

    private final ECPoint publicKey;
    private final BigInteger privateKey;

    public SM2KeyPair(ECPoint publicKey, BigInteger privateKey) {
        this.publicKey = publicKey;
        this.privateKey = privateKey;
    }

    public ECPoint getPublicKey() {

        return publicKey;
    }

    public BigInteger getPrivateKey() {
        return privateKey;
    }

    public String getPubHexInSoft() {
        return ByteUtil.byteToHex(publicKey.getEncoded(false));
    }

    public String getPriHexInSoft() {
        return ByteUtil.byteToHex(privateKey.toByteArray());
    }

}
