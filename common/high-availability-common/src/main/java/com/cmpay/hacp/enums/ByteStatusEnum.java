package com.cmpay.hacp.enums;

public enum ByteStatusEnum {
    DISABLE(Byte.valueOf("0"), "DISABLE"),
    ENABLE(Byte.valueOf("1"), "ENABLE"),
    MIDDLE(Byte.valueOf("2"), "MIDDLE");

    private Byte value;

    private String describe;

    ByteStatusEnum(Byte value, String describe) {
        this.value = value;
        this.describe = describe;
    }

    public Byte getValue() {
        return value;
    }

    public String getDescribe() {
        return this.describe;
    }
}
