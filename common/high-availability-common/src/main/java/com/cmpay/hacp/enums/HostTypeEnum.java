package com.cmpay.hacp.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/08/21 14:09
 * @since 1.0.0
 */

@Getter
@AllArgsConstructor
public enum HostTypeEnum implements KeyValueValuable ,StringArrayValuable {

    Application_Host("应用主机"),
    CL_HOST("CL主机"),
    SPRINGBOARD_HOST("跳板机"),
    BUS_HOST("业务机"),
    ;
    private String value;

    @JsonCreator
    public static HostTypeEnum getEnum(String value) {
        return Arrays.stream(HostTypeEnum.values()).filter(f->f.value.equals(value)||f.name().equals(value)).findAny().get();
    }

    @Override
    public KeyValue generateKeyValue() {
        KeyValue keyValue = new KeyValue();
        keyValue.setValue(this.getValue());
        keyValue.setKey(this.name());
        return keyValue;
    }

    @Override
    public String[] array() {
        return Arrays.stream(values()).map(HostTypeEnum::getValue).toArray(String[]::new);
    }
}
