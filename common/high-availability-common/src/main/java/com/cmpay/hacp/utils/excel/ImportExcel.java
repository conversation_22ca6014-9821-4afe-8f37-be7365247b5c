package com.cmpay.hacp.utils.excel;

import com.cmpay.hacp.annotation.excel.ColumnName;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.utils.reflection.Reflection;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.*;

/***
 * @ClassName: ImportExcel
 * @Description: 导入Excel文件（支持“XLS”和“XLSX”格式）
 * @Author: lihuiquan
 * @Date: 2019/5/15 9:56
 * @version : V1.0
 */
public class ImportExcel {

    private static final Logger log = LoggerFactory.getLogger(ImportExcel.class);

    /**
     * 工作薄对象
     */
    private final Workbook wb;

    /**
     * 工作表对象
     */
    private final Sheet sheet;

    /**
     * 标题行号
     */
    private final int headerNum;

    /**
     * 构造函数
     *
     * @param fileName  导入文件，读取第一个工作表
     * @param headerNum 标题行号，数据行号=标题行号+1
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(String fileName, int headerNum) throws InvalidFormatException, IOException {
        this(new File(fileName), headerNum);
    }

    /**
     * 构造函数
     *
     * @param file      导入文件对象，读取第一个工作表
     * @param headerNum 标题行号，数据行号=标题行号+1
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(File file, int headerNum) throws InvalidFormatException, IOException {
        this(file, headerNum, 0);
    }

    /**
     * 构造函数
     *
     * @param fileName   导入文件
     * @param headerNum  标题行号，数据行号=标题行号+1
     * @param sheetIndex 工作表编号
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(String fileName, int headerNum, int sheetIndex) throws InvalidFormatException, IOException {
        this(new File(fileName), headerNum, sheetIndex);
    }

    /**
     * 构造函数
     *
     * @param file       导入文件对象
     * @param headerNum  标题行号，数据行号=标题行号+1
     * @param sheetIndex 工作表编号
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(File file, int headerNum, int sheetIndex) throws InvalidFormatException, IOException {
        this(file.getName(), FileUtils.openInputStream(file), headerNum, sheetIndex);
    }


    /**
     * 构造函数
     *
     * @param file       导入文件对象
     * @param headerNum  标题行号，数据行号=标题行号+1
     * @param sheetName 工作表编号
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(File file, int headerNum, String sheetName) throws InvalidFormatException, IOException {
        this(file.getName(), FileUtils.openInputStream(file), headerNum, sheetName);
    }

    /**
     * 构造函数
     *
     * @param multipartFile 导入文件对象
     * @param headerNum     标题行号，数据行号=标题行号+1
     * @param sheetIndex    工作表编号
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(MultipartFile multipartFile, int headerNum, int sheetIndex)
            throws InvalidFormatException, IOException {
        this(multipartFile.getOriginalFilename(), multipartFile.getInputStream(), headerNum, sheetIndex);
    }


    /**
     * 构造函数
     *
     * @param multipartFile 导入文件对象
     * @param headerNum     标题行号，数据行号=标题行号+1
     * @param sheetName    工作表编号
     * @throws InvalidFormatException
     * @throws IOException
     */
    public ImportExcel(MultipartFile multipartFile, int headerNum, String sheetName)
            throws InvalidFormatException, IOException {
        this(multipartFile.getOriginalFilename(), multipartFile.getInputStream(), headerNum, sheetName);
    }


    /**
     * 构造函数
     *
     * @param fileName   导入文件对象
     * @param headerNum  标题行号，数据行号=标题行号+1
     * @param sheetName 工作表编号
     * @throws IOException
     */
    public ImportExcel(String fileName, InputStream is, int headerNum, String sheetName)
            throws IOException {
        Workbook wb1;
        if (StringUtils.isBlank(fileName)) {
            throw new RuntimeException("导入文档为空!");
        } else if (fileName.toLowerCase(Locale.ENGLISH).endsWith("xls")) {
            try(HSSFWorkbook sheets = new HSSFWorkbook(is)) {
                wb1 =sheets;
            }catch (IOException e){
                log.error("HSSFWorkbook io exception:{}",e.getMessage());
                throw new IOException();
            }
        } else if (fileName.toLowerCase(Locale.ENGLISH).endsWith("xlsx")) {
            try(XSSFWorkbook sheets = new XSSFWorkbook(is)) {
                wb1 =sheets;
            }catch (IOException e) {
                log.error("XSSFWorkbook io exception:{}", e.getMessage());
                throw new IOException();
            }
        } else {
            throw new RuntimeException("文档格式不正确!");
        }
        this.wb = wb1;
        if (this.wb.getSheetIndex(sheetName) <0) {
            BusinessException.throwBusinessException(MsgEnum.EXCEL_SHEET_NAME_ERROR);
        }
        this.sheet = this.wb.getSheet(sheetName);
        this.headerNum = headerNum;
        log.debug("Initialize success.");
    }

    /**
     * 构造函数
     *
     * @param fileName   导入文件对象
     * @param headerNum  标题行号，数据行号=标题行号+1
     * @param sheetIndex 工作表编号
     * @throws IOException
     */
    public ImportExcel(String fileName, InputStream is, int headerNum, int sheetIndex)
            throws IOException {
        Workbook wb1;
        if (StringUtils.isBlank(fileName)) {
            throw new RuntimeException("导入文档为空!");
        } else if (fileName.toLowerCase(Locale.ENGLISH).endsWith("xls")) {
            try(HSSFWorkbook sheets = new HSSFWorkbook(is)) {
                wb1 =sheets;
            }catch (IOException e){
                log.error("HSSFWorkbook io exception:{}",e.getMessage());
                throw new IOException();
            }
        } else if (fileName.toLowerCase(Locale.ENGLISH).endsWith("xlsx")) {
            try(XSSFWorkbook sheets = new XSSFWorkbook(is)) {
                wb1 =sheets;
            }catch (IOException e) {
                log.error("XSSFWorkbook io exception:{}", e.getMessage());
                throw new IOException();
            }
        } else {
            throw new RuntimeException("文档格式不正确!");
        }
        this.wb = wb1;
        if (this.wb.getNumberOfSheets() < sheetIndex) {
            throw new RuntimeException("文档中没有工作表!");
        }
        this.sheet = this.wb.getSheetAt(sheetIndex);
        this.headerNum = headerNum;
        log.debug("Initialize success.");
    }

    /**
     * 获取行对象
     *
     * @param rownum
     * @return
     */
    public Row getRow(int rownum) {
        return this.sheet.getRow(rownum);
    }

    /**
     * 获取数据行号
     *
     * @return
     */
    public int getDataRowNum() {
        return headerNum + 1;
    }

    /**
     * 获取最后一个数据行号
     *
     * @return
     */
    public int getLastDataRowNum() {
        return this.sheet.getLastRowNum() + headerNum;
    }

    /**
     * 获取最后一个列号
     *
     * @return
     */
    public int getLastCellNum() {
        return this.getRow(headerNum).getLastCellNum();
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (cell != null) {
                if (cell.getCellType() == CellType.NUMERIC) {
                    DecimalFormat decimalFormat = new DecimalFormat("0");
                    val = decimalFormat.format(cell.getNumericCellValue());
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.FORMULA) {
                    val = cell.getCellFormula();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }
            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    public <E> List<E> getDataList(Class<E> cls, String sheetName,int... groups) throws InstantiationException, IllegalAccessException {
        if(sheetName.equals(this.sheet.getSheetName())){
            return this.getDataList(cls,groups);
        }
        BusinessException.throwBusinessException(MsgEnum.EXCEL_SHEET_NAME_ERROR);
        return null;
    }

    /**
     * 获取导入数据列表
     *
     * @param cls    导入对象类型
     * @param groups 导入分组
     */
    public <E> List<E> getDataList(Class<E> cls, int... groups) throws InstantiationException, IllegalAccessException {
        List<Object[]> annotationList = new ArrayList<>();
        // Get annotation field
        Field[] fs = cls.getDeclaredFields();
        for (Field f : fs) {
            scanAnnotations(groups, f, annotationList);
        }
        // Get annotation method
        Method[] ms = cls.getDeclaredMethods();
        for (Method m : ms) {
            scanAnnotations(groups, m, annotationList);
        }
        // Field sorting
        Collections.sort(annotationList, new Comparator<Object[]>() {
            @Override
            public int compare(Object[] o1, Object[] o2) {
                return new Integer(((ColumnName) o1[0]).sort()).compareTo(new Integer(((ColumnName) o2[0]).sort()));
            }

        });
        // Get excel data
        List<E> dataList = new ArrayList<>();
        for (int i = this.getDataRowNum(); i <= this.getLastDataRowNum(); i++) {
            E e = cls.newInstance();
            int column = 0;
            Row row = this.getRow(i);
            StringBuilder sb = new StringBuilder();
            for (Object[] os : annotationList) {
                Object val = this.getCellValue(row, column++);
                if (val != null) {
                    ColumnName ef = (ColumnName) os[0];
                    // Get param type and type cast
                    Class<?> valType = Class.class;
                    if (os[1] instanceof Field) {
                        valType = ((Field) os[1]).getType();
                    } else if (os[1] instanceof Method) {
                        Method method = ((Method) os[1]);
                        if ("get".equals(method.getName().substring(0, 3))) {
                            valType = method.getReturnType();
                        } else if ("set".equals(method.getName().substring(0, 3))) {
                            valType = ((Method) os[1]).getParameterTypes()[0];
                        }
                    }
                    try {
                        if (valType == String.class) {
                            String s = String.valueOf(val.toString());
                            if (StringUtils.endsWith(s, ".0")) {
                                val = StringUtils.substringBefore(s, ".0");
                            } else {
                                val = String.valueOf(val.toString());
                            }
                        } else if (valType == Integer.class) {
                            val = Double.valueOf(val.toString()).intValue();
                        } else if (valType == Long.class) {
                            val = Double.valueOf(val.toString()).longValue();
                        } else if (valType == Double.class) {
                            val = Double.valueOf(val.toString());
                        } else if (valType == Float.class) {
                            val = Float.valueOf(val.toString());
                        } else if (valType == Date.class) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                        else {
                            String fieldMethodName="getValue";
                            if(!"".equals(ef.deserializationMethodName())){
                                fieldMethodName=ef.deserializationMethodName();
                            }
                            if (ef.fieldType() != Class.class) {
                                val = ef.fieldType().getMethod(fieldMethodName, String.class).invoke(null, val.toString());
                            } else {
                                val = Class
                                        .forName(this.getClass().getName().replaceAll(this.getClass().getSimpleName(),
                                                "fieldtype." + valType.getSimpleName() + "Type"))
                                        .getMethod(fieldMethodName, String.class).invoke(null, val.toString());
                            }
                        }
                    } catch (Exception ex) {
                        log.info("Get cell value [" + i + "," + column + "] error: " + ex);
                        val = null;
                    }

                    // set entity value
                    if (os[1] instanceof Field && val != null) {
                        Reflection.invokeSetter(e, ((Field) os[1]).getName(), val);
                    } else if (os[1] instanceof Method) {
                        String mthodName = ((Method) os[1]).getName();
                        if ("get".equals(mthodName.substring(0, 3))) {
                            mthodName = "set" + StringUtils.substringAfter(mthodName, "get");
                        }
                        Reflection.invokeMethod(e, mthodName, new Class[]{valType}, new Object[]{val});
                    }
                }
                sb.append(val + ", ");
            }
            dataList.add(e);
            log.debug("Read success: [" + i + "] " + sb);
        }
        return dataList;
    }

    private static void scanAnnotations(int[] groups, AccessibleObject f, List<Object[]> annotationList) {
        ColumnName ef = f.getAnnotation(ColumnName.class);
        boolean isYes = ef != null && (ef.type() == 0 || ef.type() == 2);
        if (isYes) {
            if (groups != null && groups.length > 0) {
                boolean inGroup = false;
                for (int g : groups) {
                    if (inGroup) {
                        break;
                    }
                    for (int efg : ef.groups()) {
                        if (g == efg) {
                            inGroup = true;
                            annotationList.add(new Object[]{ef, f});
                            break;
                        }
                    }
                }
            } else {
                annotationList.add(new Object[]{ef, f});
            }
        }
    }

    public String getSheetName() {
        return this.sheet.getSheetName();
    }
}
