package com.cmpay.hacp.utils.crypto;

import lombok.Data;

import java.math.BigInteger;

/**
 * 签名对象
 *
 * <AUTHOR>
 * @date 2020-05-10
 */
@Data
public class Signature {
    BigInteger r;
    BigInteger s;

    public Signature() {
    }

    public Signature(BigInteger r, BigInteger s) {
        this.r = r;
        this.s = s;
    }

    @Override
    public String toString() {
        return r.toString(16) + "." + s.toString(16);
    }
}
