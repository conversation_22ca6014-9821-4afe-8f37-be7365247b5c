package com.cmpay.hacp.constant;

/**
 * <AUTHOR>
 * @create 2024/09/11 9:34
 * @since 1.0.0
 */

public class EmergencyConstant {

    public static final String SKIP_TASK = "skip";

    public static String INITIATOR = "initiator";

    public static String START_USER_NAME = "startUserName";

    public static String CASE_ID = "caseId";

    /**
     * 后面需要拼接businessKey
     */
    public static String CASE_INFO = "caseInfo";

    public static String CASE_TAG_ID = "caseTagId";

    public static String TASK_ID = "taskId";

    public static String TASK_STATE_VARIABLE_ID = "TASK_STATE_VARIABLE_ID";

    public static  String TASK_VARIABLE="TASK_VARIABLE_";

    public static  String EXECUTE_TASK_LOCK = "EXECUTE_TASK_LOCK_";

    public static String EXECUTE_TASK_STATE = "EXECUTE_TASK_STATE_";

    public static String SUCCEED = "成功";
    public static  String PROCESS_INSTANCE_NAME = "processInstanceName";
    public static  String LAST_TASK_NAME = "lastTaskName";
    public static String APPROVE_PASS = "pass";
    public static String APPROVE_REJECT = "reject";

    public static String APPROVE_DEFAULT_COMMENT = "同意！";
    public static String NOT_APPROVE_DEFAULT_COMMENT = "不同意！";

    public static final String PROCESS_INSTANCE_FAIL_DELETE_COMMENT = "execution-exception";
    /**
     * 进行中
     */
    public static final String ACTIVE = "ACTIVE";
    /**
     * 完成
     */
    public static final String STATE_COMPLETED = "COMPLETED";
    /**
     * 执行失败
     */
    public static final String STATE_INTERNALLY_TERMINATED = "INTERNALLY_TERMINATED";
    /**
     * 审批拒绝
     */
    public static final String EXTERNALLY_TERMINATED = "EXTERNALLY_TERMINATED";

    public static final String INITIATE_DURATION = "initiate-duration";

    public static final String LOG_ID = "log-id";

    public static final String RUNTIME_LOG_PREFIX_KEY =  "Execute_log_";

}
