package com.cmpay.hacp.utils.crypto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Arrays;

/**
 * 数据库脱敏aes算法
 *
 * <AUTHOR>
 * @Link org.apache.shardingsphere.encrypt.strategy.impl.AESEncryptor
 */
@Slf4j
public class AESEncryptorUtil {

    private static final String AESTYPE = "AES/ECB/PKCS5Padding";

    private static final int KEY_LENGTH = 16;

    /**
     * @return aes加密类型
     */
    private static String getType() {
        return "AES";
    }

    /**
     * 加密密文
     *
     * @param aesKey    秘钥
     * @param plaintext 明文
     * @return
     */
    public static String encrypt(final String aesKey, final Object plaintext) throws Exception {
        if (null == plaintext) {
            return null;
        }
        byte[] result = getCipher(Cipher.ENCRYPT_MODE, aesKey).doFinal(StringUtils.getBytesUtf8(String.valueOf(plaintext)));
        return Base64.encodeBase64String(result);
    }

    /**
     * 解密密文
     *
     * @param aesKey     秘钥
     * @param ciphertext 加密密文
     * @return
     */
    public static String decrypt(final String aesKey, final String ciphertext) throws Exception {
        if (null == ciphertext) {
            return null;
        }
        byte[] result = getCipher(Cipher.DECRYPT_MODE, aesKey).doFinal(Base64.decodeBase64(ciphertext));
        return new String(result, StandardCharsets.UTF_8);
    }

    /**
     * 转换jdk标准秘钥
     *
     * @param decryptMode
     * @param aesKey
     * @return
     */
    private static Cipher getCipher(final int decryptMode, final String aesKey) throws Exception {
        Cipher result = Cipher.getInstance(getType());
        result.init(decryptMode, new SecretKeySpec(createSecretKey(aesKey), getType()));
        return result;
    }

    /**
     * 转换字节key
     *
     * @param aesKey
     * @return
     */
    private static byte[] createSecretKey(final String aesKey) {
        return Arrays.copyOf(DigestUtils.sha1(aesKey), 16);
    }

    /**
     * 方法说明：加密 创建时间：2016-11-30 上午9:34:01 创建者：zhuoml
     *
     * @param keyStr    密钥
     * @param plainText 加密的内容
     * @return 返回类型：String
     */
    public static String aesEncrypt(String keyStr, String plainText) {
        if (null == keyStr || null == plainText || "".equals(keyStr)
                || "".equals(plainText)) {
            return "";
        }

        StringBuffer bufferKey = new StringBuffer();

        if (keyStr.length() < KEY_LENGTH) {
            for (int i = 0; i < KEY_LENGTH - keyStr.length(); ++i) {
                bufferKey.append("=");
            }
            keyStr = keyStr + bufferKey;
        } else if (keyStr.length() > KEY_LENGTH) {
            keyStr = keyStr.substring(0, 16);
        }

        byte[] encrypt = null;
        try {
            Key key = generateKey(keyStr);
            Cipher cipher = Cipher.getInstance(AESTYPE);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            encrypt = cipher.doFinal(plainText.getBytes());
        } catch (Exception e) {
            log.error("aesEncrypt异常...", e);
        }
        return new String(Base64.encodeBase64(encrypt));
    }


    public static String aesDecrypt(String keyStr, String encryptData) {
        if (null == keyStr || null == encryptData || "".equals(keyStr)
                || "".equals(encryptData)) {
            return "";
        }
        StringBuffer bufferKey = new StringBuffer();

        if (keyStr.length() < KEY_LENGTH) {
            for (int i = 0; i < KEY_LENGTH - keyStr.length(); ++i) {
                bufferKey.append("=");
            }
            keyStr = keyStr + bufferKey;
        } else if (keyStr.length() > KEY_LENGTH) {
            keyStr = keyStr.substring(0, 16);
        }
        byte[] decrypt = null;
        try {
            Key key = generateKey(keyStr);
            Cipher cipher = Cipher.getInstance(AESTYPE);
            cipher.init(Cipher.DECRYPT_MODE, key);
            decrypt = cipher
                    .doFinal(Base64.decodeBase64(encryptData.getBytes()));
        } catch (Exception e) {
            log.error("aesDecrypt异常...", e);
        }
        return new String(decrypt).trim();
    }

    private static Key generateKey(String key) throws Exception {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
            return keySpec;
        } catch (Exception e) {
            log.error("generateKey异常...", e);
            throw e;
        }

    }

}
