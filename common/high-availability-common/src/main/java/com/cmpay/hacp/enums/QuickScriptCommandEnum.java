package com.cmpay.hacp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/08/30 10:47
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum QuickScriptCommandEnum implements KeyValueValuable  {
    /**
     * 从6开始使用，需要对应
     */
    REBOOT("6","source .bash_profile&&source .bashrc&&source /etc/profile&&sh ~/bin/start.sh","应用重启",0);

    private String code;

    /**
     * 价值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 扩展预留字段
     */
    private Integer extend;

    public static String getScript(String code) {
        for (QuickScriptCommandEnum taskType : QuickScriptCommandEnum.values()) {
            if (taskType.code.equals(code)) {
                return taskType.value;
            }
        }
        return null;
    }

    @Override
    public KeyValue generateKeyValue() {
        KeyValue keyValue = new KeyValue();
        keyValue.setValue(this.getDesc());
        keyValue.setKey(this.name());
        return keyValue;
    }

}
