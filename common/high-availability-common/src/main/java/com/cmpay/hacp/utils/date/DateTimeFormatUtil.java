package com.cmpay.hacp.utils.date;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateTimeFormatUtil extends com.cmpay.lemon.common.utils.DateTimeUtils {

    public static Date parseDate(String dateStr) {
        LocalDateTime localDateTime = parseLocalDateTime(dateStr, "yyyy-MM-dd HH:mm:ss");
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

}
