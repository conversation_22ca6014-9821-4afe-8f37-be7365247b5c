package com.cmpay.hacp.utils.validation;


import com.cmpay.hacp.enums.StatusConstans;

import java.time.LocalDateTime;

/**
 * 校验用户是否需要修改密码工具类
 *
 * @author: tnw
 * Date: 2020/3/17
 */
public class CheckPwdNeedToModifyUtil {

    /**
     * @param pwdModifyTime        用户上次修改密码时间
     * @param limitTimeOfModifyPwd 用户需要定期修改密码的时间要求
     */
    public static String checkPwdNeedToModify(LocalDateTime pwdModifyTime, long limitTimeOfModifyPwd, long daysFromPasswordExpiration) {
        LocalDateTime pwdMustToModifyTime = pwdModifyTime.plusDays(limitTimeOfModifyPwd);

        //1.判断密码是否已经过期
        if (LocalDateTime.now().isAfter(pwdMustToModifyTime)) {
            return StatusConstans.PWD_MUST_TO_MODIFY.getValue();
        }

        //2.判断是否需要提醒用户修改密码
        LocalDateTime remindToModifyTime = pwdModifyTime.plusDays(limitTimeOfModifyPwd - daysFromPasswordExpiration);
        if (LocalDateTime.now().isAfter(remindToModifyTime)) {
            return StatusConstans.REMIND_TO_MODIFY_PWD.getValue();
        }

        //3.返回用户密码不需要修改和提醒
        return StatusConstans.PWD_NO_NEED_TO_MODIFY.getValue();

    }

}
