package com.cmpay.hacp.utils;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * ID生成器
 *
 * <AUTHOR>
 * @create 2024/05/09 11:24:30
 * @since 1.0.0
 */
public class IdGenUtil {
    /**
     * 生成租户ID,12位，可能冲突，但是概率非常低
     *
     * @return
     */
    public static String generateTenantId() {
        return generatorRandom();
    }

    /**
     * 生成租户用户关联ID
     *
     * @return
     */
    public static String generateTenantUserId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成租户项目关联ID
     *
     * @return
     */
    public static String generateTenantWorkspaceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成租户项目ID,12位，可能冲突，但是概率非常低
     *
     * @return
     */
    public static String generateWorkspaceId() {
        return generatorRandom();
    }

    /**
     * 生成项目成员关联ID
     *
     * @return
     */
    public static String generateWorkspaceUserId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成项目角色id
     *
     * @return
     */
    public static String generateWorkspaceRoleId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成项目角色菜单id
     *
     * @return
     */
    public static String generateWorkspaceRoleMenuId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成字典表主键ID
     *
     * @return
     */
    public static String generatorDictId() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * java  UUID 替换中划线
     *
     * @return
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * 生成用户表ID
     *
     * @return
     */
    public static String generatorUserId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成日志表主键ID
     *
     * @return
     */
    public static String generatorLogId() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    public static String generatorRandom() {
        String str = randomUUID();
        StringBuilder result = new StringBuilder(12);
        SecureRandom random = new SecureRandom();
        // 从字符串中取出字符并添加到 StringBuilder 中，重复12次
        for (int i = 0; i < 12; i++) {
            int index = random.nextInt(str.length()-1);
            result.append(str.charAt(index));
        }
        return result.toString();
    }
}
