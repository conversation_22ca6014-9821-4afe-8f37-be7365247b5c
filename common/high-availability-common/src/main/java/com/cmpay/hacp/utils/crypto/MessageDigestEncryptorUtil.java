package com.cmpay.hacp.utils.crypto;


import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @author: tnw
 * Date: 2020/4/10
 */
@Slf4j
public class MessageDigestEncryptorUtil {
    /**
     * 提取sessionToken摘要用算法
     */
    public final static String MessageDigestSha256Algorithm = "sha-256";

    /**
     * 使用指定哈希算法计算摘要信息
     *
     * @param content 内容
     * @return 内容摘要
     */
    public static String getSha256Digest(String content) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance(MessageDigestSha256Algorithm);
            messageDigest.update(content.getBytes(StandardCharsets.UTF_8));
            return bytesToHexString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.info("使用指定哈希算法计算摘要信息出现异常..{}", e.getMessage());
        }
        return null;
    }

    /**
     * 将字节数组转换成16进制字符串
     *
     * @param bytes 即将转换的数据
     * @return 16进制字符串
     */
    private static String bytesToHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer(bytes.length);
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(0xFF & bytes[i]);
            if (temp.length() < 2) {
                sb.append(0);
            }
            sb.append(temp);
        }
        return sb.toString();
    }
}
