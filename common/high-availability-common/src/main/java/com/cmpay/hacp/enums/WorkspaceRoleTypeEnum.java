package com.cmpay.hacp.enums;

/**
 * 项目角色枚举
 *
 * <AUTHOR>
 * @create 2024/05/09 11:03:39
 * @since 1.0.0
 */
public enum WorkspaceRoleTypeEnum {

    /**
     * 项目管理员
     */
    ADMIN("1", "项目管理员"),
    /**
     * 项目普通用户
     */
    ANYONE("0", "项目普通用户");

    private String code;

    private String describe;

    WorkspaceRoleTypeEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public String getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }
}
