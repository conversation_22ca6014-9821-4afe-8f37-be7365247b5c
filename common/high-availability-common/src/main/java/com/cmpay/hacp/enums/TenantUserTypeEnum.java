package com.cmpay.hacp.enums;

/**
 * 租户用户身份枚举
 *
 * <AUTHOR>
 * @create 2024/05/09 11:03:53
 * @since 1.0.0
 */
public enum TenantUserTypeEnum {

    /**
     * 租户管理员
     */
    ADMIN("1", "租户管理员"),
    /**
     * 租户普通用户
     */
    ANYONE("0", "租户普通用户");

    private String code;

    private String describe;

    TenantUserTypeEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public String getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }
}
