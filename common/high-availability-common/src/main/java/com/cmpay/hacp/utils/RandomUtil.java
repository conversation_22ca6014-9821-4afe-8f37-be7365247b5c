package com.cmpay.hacp.utils;


import lombok.extern.slf4j.Slf4j;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 随机数工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RandomUtil {

    static SecureRandom random = null;

    static {

        try {
            random = SecureRandom.getInstance("SHA1PRNG");
        } catch (NoSuchAlgorithmException e) {
            log.error("获取随机数发生异常: ",e);
        }

    }

    /**
     * 获取随机数
     * @param max 最大值，不含
     * @return
     */
    public static int getRandomNumber(int max) {
        return random.nextInt(max);
    }

    /**
     * <p>
     * 生产长度为length的随机字母数字混合字符串
     * </p>
     *
     * @param length 指定字符串长度
     * @return
     */
    public static String getCharacterAndNumber(int length) {
        StringBuilder val = new StringBuilder();

        for (int i = 0; i < length; i++) {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val.append((char) (choice + random.nextInt(26)));
            }
            // 数字
            else {
                val.append(random.nextInt(10));
            }
        }
        return val.toString();
    }

}
