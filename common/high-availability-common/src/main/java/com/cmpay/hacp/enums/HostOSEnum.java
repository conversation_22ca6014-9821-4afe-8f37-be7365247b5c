package com.cmpay.hacp.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/08/21 14:09
 * @since 1.0.0
 */

@Getter
@AllArgsConstructor
public enum HostOSEnum implements KeyValueValuable,StringArrayValuable {

    Linux("Linux"),
    Windows("Windows"),
    Mac("Mac"),
    ;
    private String value;

    @JsonCreator
    public static HostOSEnum getEnum(String value) {
        return Arrays.stream(HostOSEnum.values()).filter(f->f.value.equals(value)||f.name().equals(value)).findAny().get();
    }

    @Override
    public KeyValue generateKeyValue() {
        KeyValue keyValue = new KeyValue();
        keyValue.setValue(this.getValue());
        keyValue.setKey(this.name());
        return keyValue;
    }

    @Override
    public String[] array() {
        return Arrays.stream(values()).map(HostOSEnum::getValue).toArray(String[]::new);
    }

}
