package com.cmpay.hacp.enums;

import com.cmpay.lemon.common.AlertCapable;
import com.cmpay.lemon.common.utils.StringUtils;



/**
 * 错误码枚举
 *
 * <AUTHOR>
 * @since 2022-03-07 14:59:53
 */
public enum DispatchMsgEnum implements AlertCapable {

    /** 发布版本不存在 */
    PUBLISH_VERSION_IS_CURRENT_VERSION("DPZN0001", "正在使用的版本不能再发布"),
    PUBLISH_VERSION_NOT_EXIST("DPZN0002", "版本不存在"),
    PUBLISH_VERSION_DESC_NOT_BLANK("DPZN0003", "发布备注不能为空"),
    PUBLISH_VERSION_CANNOT_DELETE("DPZN0004", "推送过的版本不能被删除"),
    PUBLISH_VERSION_REDUNDANT("DPZN0005", "重复发布,已经发布的版本"),
    WORKSPACE_IS_NULL("DPZN0006", "工作空间为空"),
    OPERATOR_ID_IS_NULL("DPZN0007", "用户ID不能为空"),
    OPEN_CLOSE_DISPATCH_EMPTY("DPZN0008", "开启和关闭的调度不能都为空"),
    OPERATOR_NAME_IS_NULL("DPZN0009", "用户名称不能为空"),
    DISPATCH_IS_STATUS_RIGHT("DPZN0010", "开启/关闭的调度不存在, 或者当前状态已经正确"),
    DISPATCH_DESC_NOT_BLANK("DPZN1003", "调度描述不能为空"),
    NO_USEFUL_DISPATCH_NODE("DPZN1004", "没有同步的调度节点"),
    EXIST_API_LOCATION_BIND_GROUP("DPZN1005", "API绑定了的业务节点"),
    EXIST_API_LOCATION_BIND_SERVICE("DPZN1006", "API绑定了的业务服务"),
    EXIST_DISPATCH_BIND_API("DPZN1007", "调度已经绑定了API"),
    EXIST_DISPATCH_BIND_RULE("DPZN1008", "调度已经绑定了规则"),
    DISPATCH_BIND_API_AND_RULE_NONE("DPZN1009", "调度没有绑定API或tag"),
    DISPATCH_BIND_API_AND_RULE_BOTH("DPZN1010", "调度不能同时绑定API和tag"),

    NO_USEFUL_DISPATCH_TEST("DPZN1011", "测试案例找不到"),
    NODE_ADDRESS_ILLEGAL("DPZN1012", "节点地址不合法"),
    RULE_NAME_ILLEGAL("DPZN1013", "规则名称不合法"),
    ZONE_LABEL_ILLEGAL("DPZN1014", "机房标识不合法"),

    INSTANCE_ZONE_EXTERNAL_ROUTER_ADDRESS_IS_NULL("DPZN2004", "跨机房节点地址不能为空"),
    COMPUTER_ROOM_USE("DPZN2005", "机房正在使用，请先删除"),

    RULE_NAME_EXISTS("DPZN2006", "规则名称已存在"),
    API_TAG_USE("DPZN2007","标签正在使用，无法删除"),
    APP_NAME_EXISTS("DPZN2008","服务名称已存在"),
    GROUP_NAME_EXISTS("DPZN2009","节点组名称已存在"),
    DISPATCH_NAME_EXISTS("DPZN2010","调度名称已经存在")
    ,
    API_NAME_EXISTS("DPZN2011","接口名称已经存在"),
    ZONE_LABEL_EXISTS("DPZN2012","机房标识已经存在"),

    ;


    private String msgCd;
    private String msgInfo;

    DispatchMsgEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    @Override
    public String getMsgCd() {
        return msgCd;
    }

    @Override
    public String getMsgInfo() {
        return msgInfo;
    }

    public static AlertCapable getEnum(String msgCd) {
        for (DispatchMsgEnum msgEnum : DispatchMsgEnum.values()) {
            if (StringUtils.equals(msgEnum.getMsgCd(), msgCd)) {
                return msgEnum;
            }
        }
    return  null;
    }
}
