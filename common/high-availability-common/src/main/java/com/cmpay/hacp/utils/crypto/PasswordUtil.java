package com.cmpay.hacp.utils.crypto;


import com.cmpay.hacp.utils.RandomUtil;
import com.cmpay.hacp.utils.StringUtil;

/**
 * 密码加密工具类
 *
 * <AUTHOR>
 */
public class PasswordUtil {

    /**
     * 密码盐值拼接符号
     */
    private final static String CONTACH_SYMBOL = "$";

    /**
     * 密码盐值拆分符号
     */
    private final static String SPLIT_SYMBOL = "\\$";

    /**
     * 密码明文在拆分数组的位置
     */
    private final static int INDEX_IN_ARRAY = 1;

    /**
     * 使用密码明文 与盐值进行拼接
     *
     * @param password
     * @param
     * @return
     */
    public static String createPassWord(String password, String aesKey) throws Exception {
        String salt = RandomUtil.getCharacterAndNumber(10);
        String encyptPwd = salt.concat(CONTACH_SYMBOL).concat(password);
        String sm4EncryptPassword = SM4EncryptorUtil.encryptEcb(aesKey, encyptPwd);
        return sm4EncryptPassword;

    }

    /**
     * 解密数据库密码
     * 拆分数据库存储的密码明文和盐值
     *
     * @param passWordFromDataBase
     * @param
     * @return
     */
    public static String decryptPassword(String passWordFromDataBase, String aesKey) {
        String oldPassWordFromDataBase = SM4EncryptorUtil.decryptCompatible(aesKey, passWordFromDataBase);
        if (StringUtil.isBlank(oldPassWordFromDataBase)) {
            return passWordFromDataBase;
        }
        String[] pwdSplit = oldPassWordFromDataBase.split(SPLIT_SYMBOL);
        oldPassWordFromDataBase = pwdSplit[INDEX_IN_ARRAY];
        return oldPassWordFromDataBase;
    }

}
