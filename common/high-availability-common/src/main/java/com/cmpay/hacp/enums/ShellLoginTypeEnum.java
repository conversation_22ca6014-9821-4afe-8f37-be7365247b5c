package com.cmpay.hacp.enums;

import com.cmpay.lemon.framework.valuable.Valuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/08/27 9:34
 * @since 1.0.0
 */

@Getter
@AllArgsConstructor
public enum ShellLoginTypeEnum implements Valuable<String> {

    TAG("tag", "标签"),
    APP("app", "实例"),
    HOST("host", "主机"),
    USERNAME_PASSWORD("username_passwd","账号密码"),
    ;

    private final String value;

    private final String desc;

}
