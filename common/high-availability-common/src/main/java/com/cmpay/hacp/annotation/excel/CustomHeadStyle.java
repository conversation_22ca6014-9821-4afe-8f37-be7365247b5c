package com.cmpay.hacp.annotation.excel;

import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.lang.annotation.*;

/**
 * 自定义头部样式注解
 * <AUTHOR>
 * @create 2022/12/05 13:11
 * @since 1.0.0
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CustomHeadStyle {

    /**
     * The name for the font (i.e. Arial)
     */
    String fontName() default "";

    /**
     * The color for the font
     *
     * @see Font#COLOR_NORMAL
     * @see Font#COLOR_RED
     * @see HSSFPalette#getColor(short)
     * @see IndexedColors
     */
    short color() default -1;

    int rowIndex();

    int colIndex() default -1;

    /**
     * 必填，指定的列的名称，不可重复
     */
    String value() ;
}
