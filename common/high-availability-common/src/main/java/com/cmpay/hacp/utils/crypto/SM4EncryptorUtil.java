package com.cmpay.hacp.utils.crypto;

import com.cmpay.lemonframework.smx.Sm4Utils;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.pqc.legacy.math.linearalgebra.ByteUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;

/**
 * 国密对称加密（SM4）解密工具类
 *
 * @author: tnw
 * Date: 2020-05-07
 */
@Slf4j
public class SM4EncryptorUtil {

    private static final String ENCODING = "UTF-8";
    /**
     * 使用CBC模式，需要一个向量iv，可增加加密算法的强度
     */
    private static final String IV = "0392239233924300";

    /**
     * sm4加密
     *
     * @param hexKey   16进制密钥（忽略大小写）
     * @param paramStr 待加密字符串
     * @return 返回16进制的加密字符串
     * @throws Exception
     * @explain 加密模式：ECB
     * 密文长度不固定，会随着被加密字符串长度的变化而变化
     */
    public static String encryptEcb(String hexKey, String paramStr) throws Exception {
        String cipherText = "";
        // 16进制字符串-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // String-->byte[]
        byte[] srcData = paramStr.getBytes(ENCODING);
        // 加密后的数组
        byte[] cipherArray = Sm4Utils.encryptEcbPadding(keyData, srcData);
        // byte[]-->hexString
        cipherText = ByteUtils.toHexString(cipherArray);
        return cipherText;
    }

    /**
     * sm4解密
     *
     * @param hexKey     16进制密钥
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用ECB
     */
    public static String decryptEcb(String hexKey, String cipherText) throws Exception {
        // 用于接收解密后的字符串
        String decryptStr = "";
        // hexString-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // hexString-->byte[]
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        // 解密
        byte[] srcData = Sm4Utils.decryptEcbPadding(keyData, cipherData);
        // byte[]-->String
        decryptStr = new String(srcData, ENCODING);
        return decryptStr;
    }

    /**
     * sm4加密
     *
     * @param hexKey   16进制密钥（忽略大小写）
     * @param paramStr 待加密字符串
     * @return 返回16进制的加密字符串
     * @throws Exception
     * @explain 加密模式：CBC
     * 密文长度不固定，会随着被加密字符串长度的变化而变化
     */
    public static String encryptCbc(String hexKey, String paramStr) throws Exception {
        String cipherText = "";
        // 16进制字符串-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // String-->byte[]
        byte[] srcData = paramStr.getBytes(ENCODING);
        // 加密后的数组
        byte[] cipherArray = Sm4Utils.encryptCbcPadding(keyData, IV.getBytes(), srcData);
        // byte[]-->hexString
        cipherText = ByteUtils.toHexString(cipherArray);
        return cipherText;
    }

    /**
     * sm4解密
     *
     * @param hexKey     16进制密钥
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用CBC
     */
    public static String decryptCbc(String hexKey, String cipherText) throws Exception {
        // 用于接收解密后的字符串
        String decryptStr = "";
        // hexString-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // hexString-->byte[]
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        // 解密
        byte[] srcData = Sm4Utils.decryptCbcPadding(keyData, IV.getBytes(), cipherData);
        // byte[]-->String
        decryptStr = new String(srcData, ENCODING);
        return decryptStr;
    }


    public static byte[] decryptEcbPadding(byte[] privateKeys, byte[] encryptDatas) throws IllegalBlockSizeException, NoSuchPaddingException, BadPaddingException, NoSuchAlgorithmException, InvalidKeyException, NoSuchProviderException {
        return Sm4Utils.decryptEcbPadding(privateKeys, encryptDatas);
    }

    /**
     * 解密 （此方法专用于兼容历史数据的解密问题）
     *
     * @param hexKey     16进制密钥
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用CBC
     */
    public static String decryptCompatible(String hexKey, String cipherText) {
        String decryptStr;

        //1,SM4 cbc 模式
        try {
            decryptStr = decryptCbc(hexKey, cipherText);
            log.info("SM4Util解密尝试Sm4-Cbc 成功");
            return decryptStr;
        } catch (Exception e) {
            log.info("SM4Util解密尝试Sm4-Cbc失败 {}", e.getMessage());
        }

        //2,Aes
        try {
            decryptStr = Cryptos.aesDecrypt(cipherText, hexKey);
            log.info("SM4Util解密尝试Aes 成功");
            return decryptStr;
        } catch (Exception e) {
            log.info("SM4Util解密尝试Aes失败 {}", e.getMessage());
        }

        //3,SM4 Ecb 模式
        try {
            decryptStr = decryptEcb(hexKey, cipherText);
            log.info("SM4Util解密尝试Sm4-Ecb 成功");
            return decryptStr;
        } catch (Exception e) {
            log.info("SM4Util解密尝试Sm4-Ecb失败 {}", e.getMessage());
        }
        return null;
    }
}
