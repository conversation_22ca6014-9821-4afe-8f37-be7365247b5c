package com.cmpay.hacp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/08/29 17:32
 * @since 1.0.0
 */

@Getter
@AllArgsConstructor
public enum BpmHighlightStyleEnum {
    //待办
    TODO("0","highlight-todo"),
    //scopeComplete
    ONGOING("1","highlight-ongoing"),
    //已取消-失败
    CANCEL("2","highlight-cancel"),
    QUASH("3","highlight-quash"),
    //已完成
    COMPLETE("4","highlight-complete"),
    REJECT("5","highlight-reject"),
    NO_MARKER("-1","no-marker"),
    // 失败-跳过
    FAIL_SKIP("2-1","highlight-cancel");
    ;
    private String status;

    /**
     * 前端的样式名，用于在前端中查找使用
     */
    private String style;


    public static String getStatus(Object status) {
        return Arrays.stream(BpmHighlightStyleEnum.values()).filter(f->f.getStatus().equals(String.valueOf(status))).findFirst().orElse(NO_MARKER).getStatus();
    }
}
