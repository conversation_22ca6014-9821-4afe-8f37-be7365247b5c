package com.cmpay.hacp.utils.network;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * <AUTHOR>
 */
public class AddressUtil {

    public static String getAddress(String url) {
        String address = null;
        Pattern p = compile("(\\d+\\.\\d+\\.\\d+\\.\\d+)\\:(\\d+)");
        Matcher m = p.matcher(url);
        while (m.find()) {
            address = m.group(1).concat(":").concat(m.group(2));
        }
        return address;
    }
}
