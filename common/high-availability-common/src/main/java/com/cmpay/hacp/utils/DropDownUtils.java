package com.cmpay.hacp.utils;

import com.cmpay.hacp.enums.KeyValue;
import com.cmpay.hacp.enums.KeyValueValuable;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/08/21 15:39
 * @since 1.0.0
 */

public class DropDownUtils {

    public static List<KeyValue> getList(KeyValueValuable... keyValues){
        return Arrays.stream(keyValues).map(KeyValueValuable::generateKeyValue).collect(Collectors.toList());
    }
}
