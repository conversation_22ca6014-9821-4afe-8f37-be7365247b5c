package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemUserApi extends BaseApi {

    /**
     * 查询用户信息
     */
    String GET_USER_INFO = BASE_SYSTEM + "/user/info";
    /**
     * * 密码更新
     */
    String UPDATE_PASSWORD = BASE_SYSTEM + "/user/password";

    /**
     * 删除用户
     */
    String DELETE = BASE_SYSTEM + "/user/delete";
    /**
     * 禁用用户
     */
    String DISABLE = BASE_SYSTEM + "/user/disable";
    /**
     * 启用用户
     */
    String ENABLE = BASE_SYSTEM + "/user/enable";

    /**
     * 添加用户
     */
    String ADD = BASE_SYSTEM + "/user/add";

    /**
     * 修改用户
     */
    String UPDATE = BASE_SYSTEM + "/user/update";

    /**
     * 查询用户列表
     */
    String GET_USER_LIST = BASE_SYSTEM + "/user/listAll";
    /**
     * 分页查询用户列表
     */
    String GET_USER_INFO_PAGE = BASE_SYSTEM + "/user/page";
    /**
     * 查询用户信息
     */
    String GET_USER_INFO_BY_ID = BASE_SYSTEM + "/user/info/{id}";
}
