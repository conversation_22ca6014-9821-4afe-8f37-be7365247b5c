package com.cmpay.hacp.annotation.mybatis;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 加密字段注解
 *
 * <AUTHOR>
 **/
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CryptField {
    /**
     * 加解密类型
     *
     * @return
     */
    String type() default CryptType.AES;
}
