package com.cmpay.hacp.utils.network;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址
 */
@Slf4j
public class IpUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(IpUtil.class);
    private static final String X_FORWARDED_FOR = "X-Forwarded-For";
    private static final String X_REAL_IP = "X-Real-IP";
    private static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    private static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    private static final String HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    private static final String HTTP_X_FORWARDED_FOR = "HTTP_X_FORWARDED_FOR";

    private static final String UNKNOWN = "unknown";
    private static final char COMMA_CHAR = ',';

    /**
     * 获取IP地址
     * <p>
     * 使用Nginx等反向代理软件， 则不能通过request.getRemoteAddr()获取IP地址
     * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = null;
        try {

            ip = request.getHeader(X_FORWARDED_FOR);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                int index = ip.indexOf(COMMA_CHAR);
                if (index != -1) {
                    return ip.substring(0, index);
                } else {
                    return ip;
                }
            }

            ip = request.getHeader(X_REAL_IP);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                return ip;
            }

            ip = request.getHeader(PROXY_CLIENT_IP);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                return ip;
            }

            ip = request.getHeader(WL_PROXY_CLIENT_IP);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                return ip;
            }

            ip = request.getHeader(HTTP_CLIENT_IP);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                return ip;
            }

            ip = request.getHeader(HTTP_X_FORWARDED_FOR);
            if (!StringUtils.isEmpty(ip) && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                return ip;
            }

            if (StringUtils.isEmpty(ip) || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                return ip;
            }

        } catch (Exception e) {
            log.error("IPUtils ERROR ", e);
        }

        return ip;
    }

    public static String getHostAddress() {
        String hostAddress = "";
        InetAddress localHost = null;
        try {
            localHost = InetAddress.getLocalHost();
            hostAddress = localHost.getHostAddress();
        } catch (UnknownHostException e) {
            LOGGER.warn("getLocalHost err {}", e.getMessage());
        }
        return hostAddress;
    }
}
