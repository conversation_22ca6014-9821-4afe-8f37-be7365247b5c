package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemMenuApi extends BaseApi {

    /**
     * 删除菜单信息
     */
    String DELETE = BASE_SYSTEM + "/menu/delete";


    /**
     * 新增菜单
     */
    String ADD = BASE_SYSTEM + "/menu/add";

    /**
     * 修改菜单
     */
    String UPDATE = BASE_SYSTEM + "/menu/update";

    /**
     * 查询菜单信息
     */
    String INFO = BASE_SYSTEM + "/menu/info/{menuId}";

    /**
     * 查询菜单信息
     */
    String AUDIT_INFO = BASE_SYSTEM + "/menu/info/tmp/{id}";

    /**
     * 获取菜单列表
     */
    String PAGE = BASE_SYSTEM + "/menu/page";
    /**
     * 个人导航菜单
     */
    String NAV = BASE_SYSTEM + "/menu/nav";

    /**
     * 应用导航菜单
     */
    String TREE = BASE_SYSTEM + "/menu/tree";

    /**
     * 菜单类型
     */
    String TYPE = BASE_SYSTEM + "/menu/type";

}
