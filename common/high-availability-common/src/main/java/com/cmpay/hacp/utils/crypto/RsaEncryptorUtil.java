package com.cmpay.hacp.utils.crypto;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Base64;

import javax.crypto.Cipher;
import java.nio.charset.Charset;
import java.security.Signature;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


/**
 * <AUTHOR>
 */
@Slf4j
public class RsaEncryptorUtil {

    private static final String RSA = "RSA";

    private static final String MD5_WITH_RSA = "MD5withRSA";

    public static byte[] decryptBase64(String key) {
        return Base64.decode(key);
    }

    public static String encryptBase64(byte[] bytes) {
        return Base64.toBase64String(bytes);
    }

    /**
     * @param data
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String sign(byte[] data, String privateKey) {
        try {
            byte[] keyBytes = decryptBase64(privateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
            Signature signature = Signature.getInstance(MD5_WITH_RSA);
            signature.initSign(priKey);
            signature.update(data);
            return encryptBase64(signature.sign());
        } catch (Exception e) {
            log.error("RSAUtilsSignError");
            return "";
        }
    }

    /**
     * @param data
     * @param publicKey
     * @param sign
     * @return
     * @throws Exception
     */
    public static boolean verify(byte[] data, String publicKey, String sign) {
        try {
            byte[] keyBytes = decryptBase64(publicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            PublicKey pubKey = keyFactory.generatePublic(keySpec);
            Signature signature = Signature.getInstance(MD5_WITH_RSA);
            signature.initVerify(pubKey);
            signature.update(data);
            return signature.verify(decryptBase64(sign));
        } catch (Exception e) {
            log.error("RSAUtilsVerifySignError");
            return false;
        }
    }


    /**
     * 公钥加密
     *
     * @param data
     * @param publicKey
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(String data, String publicKey) {
        try {
            byte[] keyBytes = decryptBase64(publicKey);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            Key key = keyFactory.generatePublic(x509KeySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return cipher.doFinal(data.getBytes());
        } catch (Exception e) {
            log.error("RSAUtilsPublicKeyEncryptError");
            return new byte[0];
        }
    }

    /**
     * 私钥解密
     *
     * @param encryptData
     * @param privateKey
     * @return
     */
    public static String decryptByPrivateKey(String encryptData, String privateKey) {
        byte[] bytes = decryptByPrivateKey(decryptBase64(encryptData), privateKey);
        return new String(bytes, Charset.defaultCharset());
    }

    private static byte[] decryptByPrivateKey(byte[] data, String privateKey) {
        try {
            byte[] keyBytes = decryptBase64(privateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            Key key = keyFactory.generatePrivate(pkcs8KeySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, key);
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("RSAUtilsPrivateKeyDecryptError");
            return new byte[0];
        }
    }

    /**
     * 私钥加密
     *
     * @param data
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data, String privateKey) {
        try {
            byte[] keyBytes = decryptBase64(privateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            Key key = keyFactory.generatePrivate(pkcs8KeySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("RSAUtilsPrivateKeyEncryptError");
            return new byte[0];
        }
    }

    /**
     * 公钥解密
     *
     * @param data
     * @param publicKey
     * @return
     */
    public static byte[] decryptByPublicKey(byte[] data, String publicKey) {
        try {
            byte[] keyBytes = decryptBase64(publicKey);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            Key key = keyFactory.generatePublic(x509KeySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, key);
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("RSAUtilsPublicKeyDecryptError");
            return new byte[0];
        }

    }
}
