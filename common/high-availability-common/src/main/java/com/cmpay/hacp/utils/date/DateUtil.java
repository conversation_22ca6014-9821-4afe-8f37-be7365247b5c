package com.cmpay.hacp.utils.date;

import com.cmpay.lemon.common.utils.DateTimeUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class DateUtil {

    public static Date localDateTimeStrToDate(String localDateTimeStr){
        if(localDateTimeStr==null){
            return null;
        }
        return Date.from(DateTimeUtils.parseLocalDateTime(localDateTimeStr).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String localDateTimeToTimeStr(LocalDateTime localDateTime){
        if(localDateTime==null){
            return null;
        }
        return String.valueOf(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }

    public static Date localDateTimeToDateTime(LocalDateTime localDateTime){
        return timeStrToDate(localDateTimeToTimeStr(localDateTime));
    }

    public static  Date timeStrToDate(String str){
        return new Date(Long.parseLong(str));
    }

}
