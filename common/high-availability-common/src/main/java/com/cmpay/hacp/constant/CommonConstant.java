package com.cmpay.hacp.constant;

/**
 * <AUTHOR>
 * @create 2024/06/04 14:33
 * @since 1.0.0
 */

public interface CommonConstant {


    String DYNAMIC_ACCESS_LOG = "DYNAMIC_ACCESS_LOG";

    String STATIC_ACCESS_LOG = "STATIC_ACCESS_LOG";

    String SM4_RANDOM_SALT = "SM4:RANDOM:SALT:";

    String MESSAGE_KEY = "message_key:";

    String ENCRYPTED_DISPLAY = "***";

    String COLON = ":";

    String SEMICOLON = ";";

    String COMMA=",";

    String LEVER = "-";

    String TOKEN = "token";

    String LINE_BREAK = "\n";

    String PANEL_OVERRIDE_JSON = "{ \"matcher\": {\"id\": \"byName\",\"options\": \"#{#name}\"},\"properties\": [{\"id\": \"displayName\", \"value\": \"#{#cn}\"} ]}";

    String SUB_CIPHER_TYPE_CONFIG = "${hacp.web.subApp.cipherType: }";

    String SPACE = " ";

    String OTHER_CN = "其他";
    String OR_CN = "或";
    String AND_CN = "且";
}
