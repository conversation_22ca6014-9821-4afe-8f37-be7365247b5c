package com.cmpay.hacp.api;

/**
 * <AUTHOR>
 */
public interface SystemRoleApi extends BaseApi {

    /**
     * 分页查询
     */
    String PAGE = BASE_SYSTEM + "/role/page";

    /**
     * 获取用户角色
     */
    String GETALLROLES = BASE_SYSTEM + "/role/select";

    /**
     * 获取临时表中角色信息
     */
    String GET_ROLE_AUDIT_INFO = BASE_SYSTEM + "/role/info/tmp/{id}";

    /**
     * 获取角色信息
     */
    String GETROLEINFO = BASE_SYSTEM + "/role/info/{roleId}";

    /**
     * 新增、修改角色-提交审核
     */
    String SAVE = BASE_SYSTEM + "/role/save";

    /**
     * 新增角色
     */
    String ADD = BASE_SYSTEM + "/role/add";

    /**
     * 修改角色
     */
    String UPDATE = BASE_SYSTEM + "/role/update";

    /**
     * 审核角色-新增、修改
     */
    String AUDIT = BASE_SYSTEM + "/role/audit";

    /**
     * 角色删除
     */
    String DELETE = BASE_SYSTEM + "/role/delete";
}
