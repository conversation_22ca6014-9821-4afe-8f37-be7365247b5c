package com.cmpay.hacp.utils.crypto;


import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;

import java.io.*;
import java.math.BigInteger;
import java.security.SecureRandom;


/**
 * SM2公钥加密算法实现 包括签名,验签,公钥加密,私钥解密
 *
 * <AUTHOR>
 * @date 2020-05-10
 */
@Slf4j
public class SM2EncryptorUtil {

    /**
     * SM2签名加密的Key
     */
    public static final String SIGN_KEY = "b4b654a35ebf7a065576e44a8759e80ed4c5e5c2f";
    public static final ECKeyPairGenerator eccKeyPairGenerator;
    private static final BigInteger n = new BigInteger(
            "FFFFFFFE" + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "7203DF6B" + "21C6052B" + "53BBF409" + "39D54123", 16);
    private static final BigInteger p = new BigInteger(
            "FFFFFFFE" + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "00000000" + "FFFFFFFF" + "FFFFFFFF", 16);
    private static final BigInteger a = new BigInteger(
            "FFFFFFFE" + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "FFFFFFFF" + "00000000" + "FFFFFFFF" + "FFFFFFFC", 16);
    private static final BigInteger b = new BigInteger(
            "28E9FA9E" + "9D9F5E34" + "4D5A9E4B" + "CF6509A7" + "F39789F5" + "15AB8F92" + "DDBCBD41" + "4D940E93", 16);
    private static final BigInteger gx = new BigInteger(
            "32C4AE2C" + "1F198119" + "5F990446" + "6A39C994" + "8FE30BBF" + "F2660BE1" + "715A4589" + "334C74C7", 16);
    private static final BigInteger gy = new BigInteger(
            "BC3736A2" + "F4F6779C" + "59BDCEE3" + "6B692153" + "D0A9877C" + "C62A4740" + "02DF32E5" + "2139F0A0", 16);
    private static final ECDomainParameters ecc_bc_spec;
    private static final int w = (int) Math.ceil(n.bitLength() * 1.0 / 2) - 1;
    private static final BigInteger _2w = new BigInteger("2").pow(w);
    private static final int DIGEST_LENGTH = 32;
    private static final SecureRandom random = new SecureRandom();
    private static final ECCurve.Fp curve;
    private static final ECPoint G;
    private static final boolean debug = false;
    private static final String SM_TYPE_04 = "04";

    static {
        curve = new ECCurve.Fp(p, a, b);
        G = curve.createPoint(gx, gy);
        ecc_bc_spec = new ECDomainParameters(curve, G, n);
        eccKeyPairGenerator = new ECKeyPairGenerator();
        eccKeyPairGenerator.init(new ECKeyGenerationParameters(ecc_bc_spec, new SecureRandom()));
    }


    /**
     * 随机数生成器
     *
     * @param max
     * @return
     */
    private static BigInteger random(BigInteger max) {
        BigInteger r = new BigInteger(256, random);
        while (r.compareTo(max) >= 0) {
            r = new BigInteger(128, random);
        }
        return r;
    }

    /**
     * 判断字节数组是否全0
     *
     * @param buffer
     * @return
     */
    private static boolean allZero(byte[] buffer) {
        for (int i = 0; i < buffer.length; i++) {
            if (buffer[i] != 0) {
                return false;
            }
        }
        return true;
    }


    /**
     * 判断是否在范围内
     *
     * @param param
     * @param min
     * @param max
     * @return
     */
    public static boolean between(BigInteger param, BigInteger min, BigInteger max) {
        return param.compareTo(min) >= 0 && param.compareTo(max) < 0;
    }

    /**
     * 字节数组拼接
     *
     * @param params
     * @return
     */
    private static byte[] join(byte[]... params) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] res = null;
        try {
            for (int i = 0; i < params.length; i++) {
                baos.write(params[i]);
            }
            res = baos.toByteArray();
        } catch (IOException e) {
            log.error("出现异常....异常信息：", e);
        }
        return res;
    }

    /**
     * sm3摘要
     *
     * @param params
     * @return
     */
    private static byte[] sm3hash(byte[]... params) {
        byte[] res = null;
        try {
            res = SM3HashAlgorithm.hash(join(params));
        } catch (IOException e) {
            log.error("出现异常....异常信息：", e);
        }
        return res;
    }

    /**
     * 取得用户标识字节数组
     *
     * @param IDA
     * @param aPublicKey
     * @return
     */
    private static byte[] ZA(String IDA, ECPoint aPublicKey) {
        byte[] idaBytes = IDA.getBytes();
        int entlenA = idaBytes.length * 8;
        byte[] ENTLA = new byte[]{(byte) (entlenA & 0xFF00), (byte) (entlenA & 0x00FF)};
        byte[] ZA = sm3hash(ENTLA, idaBytes, a.toByteArray(), b.toByteArray(), gx.toByteArray(), gy.toByteArray(),
                aPublicKey.getXCoord().toBigInteger().toByteArray(),
                aPublicKey.getYCoord().toBigInteger().toByteArray());
        return ZA;
    }

    /**
     * 签名
     *
     * @param sourceData   签名信息
     * @param signatureKey 签名方唯一标识
     * @param keyPair      签名方密钥对
     * @return 签名
     */
    public static Signature sign(String sourceData, String signatureKey, SM2KeyPair keyPair) {
        byte[] ZA = ZA(signatureKey, keyPair.getPublicKey());
        byte[] M_ = join(ZA, sourceData.getBytes());
        BigInteger e = new BigInteger(1, sm3hash(M_));
        BigInteger k;
        BigInteger r;
        do {
            k = random(n);
            ECPoint p1 = G.multiply(k).normalize();
            BigInteger x1 = p1.getXCoord().toBigInteger();
            r = e.add(x1);
            r = r.mod(n);
        } while (r.equals(BigInteger.ZERO) || r.add(k).equals(n));

        BigInteger s = ((keyPair.getPrivateKey().add(BigInteger.ONE).modInverse(n))
                .multiply((k.subtract(r.multiply(keyPair.getPrivateKey()))).mod(n))).mod(n);

        return new Signature(r, s);
    }

    /**
     * 签名
     *
     * @param sourceData   签名信息
     * @param signatureKey 签名方唯一标识
     * @param publicKey    公钥
     * @param privateKey   私钥
     * @return 签名
     */
    public static Signature sign(String sourceData, String signatureKey, String publicKey, String privateKey) {
        SM2KeyPair keyPair = createSM2KeyPair(publicKey, privateKey);
        Signature signature = sign(sourceData, signatureKey, keyPair);
        return signature;
    }

    /**
     * 签名
     *
     * @param sourceData 签名信息
     * @param publicKey  公钥
     * @param privateKey 私钥
     * @return 签名
     */
    public static Signature sign(String sourceData, String publicKey, String privateKey) {
        SM2KeyPair keyPair = createSM2KeyPair(publicKey, privateKey);
        Signature signature = sign(sourceData, SIGN_KEY, keyPair);
        return signature;
    }

    /**
     * 验签
     *
     * @param sourData     签名信息
     * @param signature    签名
     * @param signatureKey 签名方唯一标识
     * @param publicKey    签名方公钥
     * @return true or false
     */
    public static boolean verify(String sourData, Signature signature, String signatureKey, ECPoint publicKey) {
        if (!between(signature.r, BigInteger.ONE, n)) {
            return false;
        }
        if (!between(signature.s, BigInteger.ONE, n)) {
            return false;
        }

        byte[] M_ = join(ZA(signatureKey, publicKey), sourData.getBytes());
        BigInteger e = new BigInteger(1, sm3hash(M_));
        BigInteger t = signature.r.add(signature.s).mod(n);

        if (t.equals(BigInteger.ZERO)) {
            return false;
        }

        ECPoint p1 = G.multiply(signature.s).normalize();
        ECPoint p2 = publicKey.multiply(t).normalize();
        BigInteger x1 = p1.add(p2).normalize().getXCoord().toBigInteger();
        BigInteger R = e.add(x1).mod(n);
        return R.equals(signature.r);
    }

    /**
     * 验签
     *
     * @param sourData     签名信息
     * @param signature    签名
     * @param signatureKey 签名方唯一标识
     * @param publicKey    签名方公钥
     * @return true or false
     */
    public static boolean verify(String sourData, Signature signature, String signatureKey, String publicKey) {
        ECPoint pubEcPoint = stringConvertToEcpoint(publicKey);
        return verify(sourData, signature, signatureKey, pubEcPoint);
    }

    /**
     * 验签
     *
     * @param sourData  签名信息
     * @param signature 签名
     * @param publicKey 签名方公钥
     * @return true or false
     */
    public static boolean verify(String sourData, Signature signature, String publicKey) {
        ECPoint pubEcPoint = stringConvertToEcpoint(publicKey);
        return verify(sourData, signature, SIGN_KEY, pubEcPoint);
    }

    /**
     * 密钥派生函数
     *
     * @param Z
     * @param klen 生成klen字节数长度的密钥
     * @return
     */
    private static byte[] KDF(byte[] Z, int klen) {
        int ct = 1;
        int end = (int) Math.ceil(klen * 1.0 / 32);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            for (int i = 1; i < end; i++) {
                baos.write(sm3hash(Z, SM3HashAlgorithm.toByteArray(ct)));
                ct++;
            }
            byte[] last = sm3hash(Z, SM3HashAlgorithm.toByteArray(ct));
            if (klen % 32 == 0) {
                baos.write(last);
            } else {
                baos.write(last, 0, klen % 32);
            }
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("UpmsSM2Util密钥派生函数发生异常", e);
        }
        return null;
    }

    /**
     * String格式公钥转ECPOINT格式
     */
    public static ECPoint stringConvertToEcpoint(String key) {
        byte[] pub = ByteUtil.hexStringToBytes(key);
        ECPoint C1 = curve.decodePoint(pub).normalize();
        return C1;
    }

    /**
     * String格式私钥转BigInteger格式
     */
    public static BigInteger stringPriConvertToBigInteger(String privateKeyBase64String) {
        byte[] privateKey = ByteUtil.hexStringToBytes(privateKeyBase64String);
        BigInteger pri = new BigInteger(1, privateKey);
        return pri;
    }

    /**
     * 使用私钥解密
     *
     * @param privateKey
     * @param encryptedData
     * @return
     * @throws IOException
     */
    public static byte[] decrypt(byte[] privateKey, byte[] encryptedData) {
        if (privateKey == null || privateKey.length == 0) {
            return null;
        }

        if (encryptedData == null || encryptedData.length == 0) {
            return null;
        }
        String data = ByteUtil.byteToHex(encryptedData);
        byte[] c1Bytes = ByteUtil.hexToByte(data.substring(0, 130));
        int c2Len = encryptedData.length - 97;
        byte[] c3 = ByteUtil.hexToByte(data.substring(130, 130 + 64));
        byte[] c2 = ByteUtil.hexToByte(data.substring(194, 194 + 2 * c2Len));

        BigInteger userD = new BigInteger(1, privateKey);

        ECPoint c1 = curve.decodePoint(c1Bytes);
        Cipher cipher = new Cipher();
        cipher.initDec(userD, c1);
        cipher.decrypt(c2);
        cipher.dofinal(c3);

        //返回解密结果
        return c2;
    }

    /**
     * 使用私钥解密
     *
     * @param privateKeyBase64String
     * @param encryptedDataBase64String
     * @return
     * @throws IOException
     */
    public static String decrypt(String privateKeyBase64String, String encryptedDataBase64String) {
        encryptedDataBase64String = SM_TYPE_04.concat(encryptedDataBase64String);
        byte[] privateKey = ByteUtil.hexStringToBytes(privateKeyBase64String);
        byte[] encryptedData = ByteUtil.hexStringToBytes(encryptedDataBase64String);
        return new String(decrypt(privateKey, encryptedData));
    }

    /**
     * 创建SM2密钥对对象
     *
     * @param publicKey
     * @param privateKey
     * @return
     */
    public static SM2KeyPair createSM2KeyPair(String publicKey, String privateKey) {
        ECPoint pubECPoint = stringConvertToEcpoint(publicKey);
        BigInteger priBigInteger = stringPriConvertToBigInteger(privateKey);
        return new SM2KeyPair(pubECPoint, priBigInteger);
    }

    /**
     * 使用公钥加密
     *
     * @param base64PublicKey
     * @param data
     * @return
     * @throws IOException
     */
    public static String encrypt(String base64PublicKey, String data) {
        byte[] publicKey = ByteUtil.hexStringToBytes(base64PublicKey);
        byte[] dataBytes = data.getBytes();
        if (publicKey == null || publicKey.length == 0) {
            return null;
        }

        if (dataBytes == null || dataBytes.length == 0) {
            return null;
        }

        byte[] source = new byte[dataBytes.length];
        System.arraycopy(dataBytes, 0, source, 0, dataBytes.length);

        Cipher cipher = new Cipher();
        SM2 sm2 = SM2.instance();
        ECPoint userKey = sm2.eccCurve.decodePoint(publicKey);

        ECPoint c1 = cipher.initEnc(sm2, userKey);
        cipher.encrypt(source);
        byte[] c3 = new byte[32];
        cipher.dofinal(c3);
        String encrypt = ByteUtil.byteToHex(c1.getEncoded(false)) + ByteUtil.byteToHex(c3) + ByteUtil.byteToHex(source);
        return encrypt.substring(2);
    }

    /**
     * 判断生成的公钥是否合法
     *
     * @param publicKey
     * @return
     */
    private boolean checkPublicKey(ECPoint publicKey) {

        if (!publicKey.isInfinity()) {

            BigInteger x = publicKey.getXCoord().toBigInteger();
            BigInteger y = publicKey.getYCoord().toBigInteger();

            if (between(x, new BigInteger("0"), p) && between(y, new BigInteger("0"), p)) {

                BigInteger xResult = x.pow(3).add(a.multiply(x)).add(b).mod(p);

                BigInteger yResult = y.pow(2).mod(p);

                return yResult.equals(xResult) && publicKey.multiply(n).isInfinity();
            }
        }
        return false;
    }

    /**
     * 生成密钥对
     *
     * @return
     */
    public SM2KeyPair generateKeyPair() {
        BigInteger d = random(n.subtract(new BigInteger("1")));
        SM2KeyPair keyPair = new SM2KeyPair(G.multiply(d).normalize(), d);
        if (checkPublicKey(keyPair.getPublicKey())) {
            return keyPair;
        } else {
            return null;
        }
    }

    /**
     * 导出公钥到本地
     *
     * @param publicKey
     * @param path
     */
    public void exportPublicKey(ECPoint publicKey, String path) {
        try {
            File file = new File(path);
            if (!file.exists()) {
                if (!file.createNewFile()) {
                    log.error("exportPublicKey出现异常....异常信息：新建文件失败");
                    return;
                }
            }
            byte[] buffer = publicKey.getEncoded(false);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(buffer);
            } catch (IOException e) {
                log.error("exportPublicKey出现异常....异常信息：", e);
            }
        } catch (IOException e) {
            log.error("exportPublicKey出现异常....异常信息：", e);
        }
    }

    /**
     * 从本地导入公钥
     *
     * @param path
     * @return
     */
    public ECPoint importPublicKey(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }
        try (FileInputStream fis = new FileInputStream(file)) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[16];
            int size;
            while ((size = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, size);
            }
            return curve.decodePoint(baos.toByteArray());
        } catch (IOException e) {
            log.error("importPublicKey出现异常....异常信息：", e);
        }
        return null;
    }

    /**
     * 导出私钥到本地
     *
     * @param privateKey
     * @param path
     */
    public void exportPrivateKey(BigInteger privateKey, String path) {
        File file = new File(path);
        try {
            if (!file.exists()) {
                if (!file.createNewFile()) {
                    log.error("exportPrivateKey出现异常....异常信息：新建文件失败");
                    return;
                }
            }
            try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(file))) {
                oos.writeObject(privateKey);
            } catch (IOException e) {
                log.error("exportPrivateKey出现异常....异常信息：", e);
            }
        } catch (IOException e) {
            log.error("exportPrivateKey出现异常....异常信息：", e);
        }
    }

    /**
     * 从本地导入私钥
     *
     * @param path
     * @return
     */
    public BigInteger importPrivateKey(String path) {
        File file = new File(path);
        try {
            if (!file.exists()) {
                return null;
            }
            try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file))) {
                BigInteger res = (BigInteger) (ois.readObject());
                return res;
            } catch (IOException e) {
                log.error("exportPrivateKey出现异常....异常信息：", e);
            }
        } catch (Exception e) {
            log.error("出现异常....异常信息：", e);
        }
        return null;
    }

    /**
     * 传输实体类
     *
     * <AUTHOR>
     */
    private static class TransportEntity implements Serializable {
        final byte[] R; //R点
        final byte[] S; //验证S
        final byte[] Z; //用户标识
        final byte[] K; //公钥

        public TransportEntity(byte[] r, byte[] s, byte[] z, ECPoint pKey) {
            R = r;
            S = s;
            Z = z;
            K = pKey.getEncoded(false);
        }
    }
}
