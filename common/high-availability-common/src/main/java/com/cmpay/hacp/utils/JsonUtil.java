package com.cmpay.hacp.utils;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @create 2024/08/15 11:29
 * @since 1.0.0
 */

public class  JsonUtil {

    private final static Logger logger = LoggerFactory.getLogger(JsonUtil.class);

    private static final ObjectMapper objectMapper ;

    static {
        objectMapper = new ObjectMapper();
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        objectMapper.registerModule(javaTimeModule);
        // 缺少/未知额外字段时也可序列化
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String objToStr(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        }catch (JsonProcessingException e){
            logger.error(e.getMessage(), e);
            BusinessException.throwBusinessException(MsgEnum.JSON_CONVERTED_ERROR);
        }
        return null;
    }

    public static <T> T strToObject(String json,Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        }catch (JsonProcessingException e){
            logger.error(e.getMessage(), e);
            BusinessException.throwBusinessException(MsgEnum.JSON_CONVERTED_ERROR);
        }
        return null;
    }

    public static <T> T strToObject(String json, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.readValue(json, valueTypeRef);
        }catch (JsonProcessingException e){
            logger.error(e.getMessage(), e);
            BusinessException.throwBusinessException(MsgEnum.JSON_CONVERTED_ERROR);
        }
        return null;
    }

}
