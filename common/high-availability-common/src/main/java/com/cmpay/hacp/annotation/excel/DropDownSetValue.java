package com.cmpay.hacp.annotation.excel;


import com.cmpay.hacp.enums.StringArrayValuable;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @create 2022/12/05 9:15
 * @since 1.0.0
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DropDownSetValue {
    /**
     * {@link StringArrayValuable}的array方法获取下拉列表值
     * @return {@link Class}<{@link ?} {@link extends} {@link StringArrayValuable}>
     */
    Class <? extends StringArrayValuable> value();

    /**
     * 行下标
     */
    int rowIndex() default 1;

    /**
     * 列标号必须和字段下标一致
     */
    int colIndex() default -1;

    /**
     * 是否是新的sheet来存放下拉框
     */
    boolean isNewSheetDropDown() default false;

    /**
     * 描述
     */
    String sheetName() default "";

    boolean validation() default true;

}
