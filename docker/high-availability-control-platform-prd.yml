kind: Deployment
apiVersion: apps/v1
metadata:
  name: high-availability-control-platform-v1
  namespace: high-availability
  labels:
    app: high-availability-control-platform-v1
    app.kubernetes.io/name: high-availability-control-platform
    app.kubernetes.io/version: v1
    language: java
    version: v1
  annotations:
    servicemesh.kubesphere.io/enabled: 'false'
spec:
  replicas: 2
  selector:
    matchLabels:
      app: high-availability-control-platform-v1
  template:
    metadata:
      labels:
        app: high-availability-control-platform-v1
        app.kubernetes.io/name: high-availability-control-platform
        app.kubernetes.io/version: v1
        language: java
        version: v1
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: high-availability-control-platform-logs
          emptyDir:
            sizeLimit: 1Gi
        - name: gc-log
          emptyDir:
            medium: Memory
            sizeLimit: 128Mi
      containers:
        - name: high-availability-control-platform
          image: >-
            repos-yl.devops.pay/paycenter-repo/high-availability-control-platform:${imageTag}
          ports:
            - name: tcp-5702
              containerPort: 5702
              protocol: TCP
            - name: tcp-8527
              containerPort: 8527
              protocol: TCP
            - name: tcp-9527
              containerPort: 9527
              protocol: TCP
          env:
            - name: EMAIL_HOST
              valueFrom:
                secretKeyRef:
                  name: hacp-common
                  key: EMAIL_HOST
            - name: EMAIL_PORT
              valueFrom:
                secretKeyRef:
                  name: hacp-common
                  key: EMAIL_PORT
            - name: EMAIL_USER
              valueFrom:
                secretKeyRef:
                  name: hacp-common
                  key: EMAIL_USER
            - name: EMAIL_SECRET
              valueFrom:
                secretKeyRef:
                  name: hacp-common
                  key: EMAIL_SECRET
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: high-availability-control-platform
                  key: DB_PASSWORD
            - name: AES_KEY
              valueFrom:
                secretKeyRef:
                  name: high-availability-control-platform
                  key: AES_KEY
            - name: DEFAULT_AES_KEY
              valueFrom:
                secretKeyRef:
                  name: high-availability-control-platform
                  key: DEFAULT_AES_KEY
            - name: DEFAULT_SM4_KEY
              valueFrom:
                secretKeyRef:
                  name: high-availability-control-platform
                  key: DEFAULT_SM4_KEY
            - name: ACTUATOR_HEADER_AUTH
              valueFrom:
                configMapKeyRef:
                  name: high-availability-control-platform
                  key: ACTUATOR_HEADER_AUTH
            - name: LEMON_ENV
              valueFrom:
                configMapKeyRef:
                  name: high-availability-control-platform
                  key: LEMON_ENV
            - name: SPRING_APPLICATION_JSON
              valueFrom:
                configMapKeyRef:
                  name: high-availability-control-platform
                  key: SPRING_APPLICATION_JSON
            - name: SW_LOGGING_DIR
              value: /app/apprun/logs
            - name: SW_LOGGING_FILE_NAME
              value: skywalking-api.log
          resources:
            limits:
              cpu: '2'
              memory: 4Gi
            requests:
              cpu: 512m
              memory: 512m
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: high-availability-control-platform-logs
              mountPath: /app/apprun/logs
            - name: gc-log
              mountPath: /dev/shm
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 9527
              scheme: HTTP
              httpHeaders:
                - name: Authorization
                  value: Basic bGVtb25BY3R1YXRvcjpsZW1vbkFjdHVhdG9yMTIzIw==
            initialDelaySeconds: 120
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 10
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: 9527
              scheme: HTTP
              httpHeaders:
                - name: Authorization
                  value: Basic bGVtb25BY3R1YXRvcjpsZW1vbkFjdHVhdG9yMTIzIw==
            initialDelaySeconds: 120
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 10
          lifecycle:
            preStop:
              exec:
                command:
                  - offline.sh
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
        - name: filebeat
          image: 'repos-yl.devops.pay/fireflyimages/fflog-filebeat-lemon-zf:v1.0'
          ports:
            - name: http-5566
              containerPort: 5566
              protocol: TCP
          env:
            - name: log_service_id
              value: d813d11f-b9a3-41d6-a7ae-b0bd3431b880
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: high-availability-control-platform-logs
              readOnly: true
              mountPath: /app/apprun/logs
            - name: gc-log
              readOnly: true
              mountPath: /dev/shm
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: hacp-hazelcast
      serviceAccount: hacp-hazelcast
      securityContext: {}
      imagePullSecrets:
        - name: harbor-image-registry
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600