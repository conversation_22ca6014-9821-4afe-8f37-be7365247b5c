server:
  port: 8528
management:
  server:
    port: 9528

spring:
  quartz:
    job-store-type: jdbc
    properties:
      org.quartz.dataSource.myDS.URL: *******************************************,10.176.33.232:6446,10.176.33.231:6446/hacp_sit_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      org.quartz.dataSource.myDS.user: hacpadm
      org.quartz.dataSource.myDS.password: 'F6mK#w3u'

lemon:
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *******************************************,10.176.33.232:6446,10.176.33.231:6446/hacp_sit_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      username: hacpadm
      password: 'F6mK#w3u'
  security:
    authorize-requests:
      permit-all:
        - /v3/api-docs/**
        - /swagger-ui/**
        - /swagger-ui.html
        - /doc.html
        - /webjars/**

# knife4j的增强配置，除dev环境，其他环境不需要配置
knife4j:
  enable: true
  setting:
    language: zh_cn

hazelcast:
  cluster-name: hzcluster-hacp-dev

hacp:
  management:
    discovery:
      name: high-availability-control-platform
      url: http://127.0.0.1:8527
    tenant-session-filter:
      permitUrls:
        - /v3/api-docs/**
        - /doc.html
        - /inspection/public/**

inspection:
  firefly:
    base-url: "http://10.176.156.3:1980"