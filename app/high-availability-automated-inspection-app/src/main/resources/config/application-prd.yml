spring:
  session:
    #登录有效期设置
    timeout: 900
  quartz:
    job-store-type: jdbc
    properties:
      org.quartz.dataSource.myDS.URL: *****************************************,10.161.8.61:6446,10.161.8.62:6446/hacp_prd_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      org.quartz.dataSource.myDS.user: hacpadm

lemon:
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *****************************************,10.161.8.61:6446,10.161.8.62:6446/hacp_prd_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      username: hacpadm

hacp:
  management:
    discovery:
      name: high-availability-control-platform
      url: http://high-availability-control-platform.high-availability.high-availability:8527
  emergence:
    kubesphere:
      properties:
        url: 'http://10.127.128.10:31407'
inspection:
  firefly:
    base-url: "http://api.firefly.pay"
    auth:
      user-id: "business_indicators-15002"
      user-key: "YnxnGt39ZGAFfkUsG50W"