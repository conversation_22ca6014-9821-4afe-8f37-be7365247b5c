SYS00001 = 对不起，系统忙，请稍后再试！
SYS00002 = 访问数据库异常
SYS00003 = 签名异常
SYS00404 = 404 error
SYS00401 = 401 error
SYS01401 = session forced to expire
SYS02401 = invalid refresh token
SYS03401 = 认证失败
SYS00403 = 禁止操作
SYS00005 = task schedule exception
SYS00006 = server 404 error
SYS00007 = 服务不可用
SYS00100 = 不能获取分布式锁
SYS00101 = 累计操作异常
SYS10001 = bean validation exception
SYS20000 = client exception
SYS20001 = UnknownHostException
SYS20002 = timeout excepiton
SYS30001 = illegal parameter
SYS40001 = producer of rabbit exception
SYS40021 = consumer of rabbit exception
SYS99999 = no message_code found
SYS11111 = warning

SYS50000 = 系统异常

SYS50101 = Excel的sheet名称不匹配！
SYS50102 = Excel模板不匹配！
SYS50103 = 导入失败！
SYS50104 = 解析失败！

HAC30100 = 邮件发送异常！ 
HAC30101 = 邮件发送失败，邮箱连接超时，请检查邮箱配置！ 
HAC30102 = 邮件发送失败，请检查发送者/目标邮箱地址是否有效！
HAC40001 = 邮件接收人为空

HAC70001 = 已存在相同租户名称
HAC70002 = 账户或密码错误
HAC70003 = session已失效，请重新登录
HAC70005 = 用户名或密码错误
HAC70006 = 数据解密失败!
HAC70007 = 缓存key不能为空！
HAC70008 = 图形验证码错误
HAC70009 = 验证码已过期
HAC70010 = 参数不完整
HAC70011 = 手机号已注册
HAC70013 = 用户名已存在
HAC70014 = 用户名不存在
HAC70022 = 输入的旧密码有误
HAC70026 = 用户未登录
HAC70030 = 图形验证码生成错误
HAC70035 = 用户输入的新旧密码相同
HAC70036 = SM4密钥过期
HAC70037 = 生成SM4密钥异常
HAC70042 = 生成AccessToken失败
HAC70049 = 无权限访问资源
HAC70057 = 该用户角色不具备获取菜单权限
HAC70074 = 登陆状态检查异常!
HAC70078 = 传入的参数异常,无法对参数进行解析！
HAC70079 = 必要参数不能为空！
HAC70083 = 校验JwtSessionToken未通过,请求使用设备不一致
HAC70084 = 客户端ID不能为空
HAC70085 = 客户端密钥不能为空
HAC70086 = 授权类型不能为空
HAC70087 = 请求地址不能为空

HAC70100 = 集群名称不能为空
HAC70101 = 命名空间不能为空
HAC70102 = 部署名不能为空

HAC90000 = 同城双活接口调用失败！
HAC90001 = 数据加密失败！
HAC90002 = 数据解密失败！
HAC90003 = 同城双活配置不存在！
HAC90004 = 要发布的ID不能为空！
HAC90005 = 发布状态核验结果失败！
HAC90006 = 枚举值不存在！
HAC90007 = 分页参数不能为空！

MON00000=交易成功
MON40000=交易失败
MON00001=登录数据解析失败
MON00002=账户或密码错误
MON00003=session已失效，请重新登录
MON00100=数据更新失败
MON00101=数据删除失败
MON00102=数据新增失败
MON00103=查询记录不存在
MON00200=菜单名称不能为空
MON00201=上级菜单不能为空
MON00203=上级菜单只能为菜单或者目录
MON00204=上级菜单只能为菜单类型
UPM00022=输入的旧密码有误
UPM00035=用户输入的新旧密码相同
MON00205=系统菜单不允许删除
MON00206=请先删除子菜单或按钮
MON50000=系统异常
00000=成功
UPM00004=ACCESSTOKEN无效
UPM00005=用户名或密码错误
UPM00008=图形验证码错误
UPM00009=参数不完整
UPM00011=手机号已注册
UPM00012=手机号未注册
UPM00013=用户名已存在
UPM00014=用户名不存在
UPM00026=用户未登录
UPM00030=图形验证码生成错误
UPM00037=SESSION_TOKEN不存在
UPM00042=生成AccessToken失败
UPM00049=无权限访问资源
UPM00057=该用户角色不具备获取菜单权限
UPM00058=异常码转换异常
UPM00066=生成SessionToken失败
UPM00067=解析JwtSessionToken失败
UPM00068=校验JwtSessionToken未通过,颁发SessionToken的应用不一致
UPM00070=获取应用系统的publickey失败
UPM00071=JwtSessionToken已过期，请重新登录
UPM00072=校验JwtSessionToken未通过,用户的所属应用编码不一致!
UPM00073=校验JwtSessionToken未通过,用户的用户名不一致!
UPM00074=登陆状态检查异常!
UPM00078=传入的参数异常,无法对参数进行解析！
UPM00082=校验JwtSessionToken未通过,IP地址不一致
UPM00083=校验JwtSessionToken未通过,请求使用设备不一致
UPM00097=token的用途非法
UPM00098=单点登陆的目标应用appId非法

HAI00001 = 数据加密公钥为空
HAI00002 = 数据解密私钥为空

HAI10001 = 插件ID不能为空
HAI10002 = 更新插件失败
HAI10003 = 插件信息不存在
HAI10004 = 插件名称已存在
HAI10005 = 插件参数不能为空
HAI10006 = 插件参数值不能为空
HAI10007 = 持续时间不能为空
HAI10008 = 插件名称不能为空
HAI10009 = 插件名称长度不能超过128个字符
HAI10010 = 插件类型不能为空
HAI10011 = 插件描述长度不能超过500个字符
HAI10012 = 脚本内容不能为空
HAI10013 = 插件详情不存在
HAI10014 = 标签ID不存在
HAI10015 = 机房不能为空
HAI10016 = 指标类型不能为空
HAI10017 = 指标不能为空
HAI10018 = 配置项不能为空
HAI10019 = 插件配置不能为空
HAI10020 = 系统内置插件不允许修改
HAI10021 = 系统内置插件不允许删除
HAI10022 = 插件检查项不能为空

HAI20001 = 规则ID不能为空
HAI20002 = 更新规则失败
HAI20003 = 规则执行配置不能为空
HAI20004 = 规则名称不能为空
HAI20005 = 规则名称长度不能超过128个字符
HAI20006 = 规则状态不能为空
HAI20007 = 规则信息不存在
HAI20008 = 规则类型不能为空
HAI20009 = 规则名称不能重复

HAI30001 = 任务ID不能为空
HAI30002 = 创建任务失败
HAI30003 = 更新任务失败
HAI30004 = 任务不存在
HAI30005 = 执行任务失败
HAI30006 = 任务名称不能为空
HAI30007 = 任务名称长度不能超过128个字符
HAI30008 = 任务描述长度不能超过500个字符
HAI30009 = 任务已存在！
HAI30010 = 任务不存在！
HAI30011 = 任务名称重复
HAI30012 = 任务状态不能为空

HAI40001 = 报告不存在
HAI40002 = 报告详细内容不存在

HAI50001 = CRON表达式不能为空
HAI50002 = 无效的CRON表达式
HAI50100 = 执行日期不能为空
HAI50101 = 执行时间不能为空

HAI50302 = 告警等级不能为空
HAI50305 = 部署环境不能为空
HAI50306 = 监控字段不能为空
HAI50307 = 规则条件不能为空
HAI50308 = 规则条件信息不能为空
HAI50309 = 条件逻辑不能为空

HAI50406 = 调度配置不能为空
HAI50407 = 间隔值不能为空
HAI50408 = 间隔值必须大于0
HAI50409 = 间隔单位不能为空
HAI50410 = 告警通知条件不能为空
HAI50411 = 计划配置未启用

HAI50500 = 参数名称不能为空
HAI50501 = 参数类型不能为空
HAI50502 = 正则表达式不能为空
HAI50503 = 示例不能为空
HAI50504 = 参数描述不能为空
HAI50505 = 字段名称不能为空
HAI50506 = 字段类型不能为空
HAI50507 = 判断条件不能为空
HAI50508 = 判断数值不能为空
HAI50509 = 仅检查峰值不能为空
HAI50510 = 治理建议不能为空
HAI50511 = 目标环境列表不能为空
HAI50512 = 目标环境类型不能为空
HAI50513 = 取值路径不能为空