package com.cmpay.hacp.inspection;

import com.cmpay.lemon.common.LemonFramework;
import com.cmpay.lemon.framework.LemonBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;

@LemonBootApplication("com.cmpay.hacp.**.client")
@EnableScheduling
@EnableCaching
public class HighAvailabilityAutomatedInspectionApplication {
    public static void main(String[] args) {
        LemonFramework.run(HighAvailabilityAutomatedInspectionApplication.class, args);
    }
}
