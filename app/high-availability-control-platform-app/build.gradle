apply plugin: 'org.springframework.boot'

dependencies {
    api project(':interface:high-availability-base-interface')
    api project(':common:high-availability-common')

    // 容器模块
    api project(':rest:high-availability-extenal-rest:high-availability-container-rest')
    // 双活流量调度模块
    api project(':rest:high-availability-extenal-rest:high-availability-cmft-rest')
    // 4A单点登录模块
    api project(':rest:high-availability-extenal-rest:high-availability-sso-rest')

    api project(':rest:high-availability-dashboard-rest')
    // 流量调度模块
    api project(':rest:high-availability-dispatch-rest')
    // 预案模块
    api project(':rest:high-availability-emergency-rest')
    // 系统管理模块
    api project(':rest:high-availability-system-rest')
    // 租户管理模块模块
    api project(':rest:high-availability-tenant-rest')
    // 消息模块
    api project(':rest:high-availability-message-rest')

    implementation('com.cmpay:lemon-framework-starter-actuator-prometheus')
    implementation('com.cmpay:lemon-framework-starter-actuator-security')
    implementation('com.cmpay:lemon-swagger-starter')
//    implementation('com.cmpay:lemon-framework-starter-cloud-netflix-eureka-client')
    implementation("com.cmpay:lemon-framework-starter-session-hazelcast")
    implementation('com.cmpay:lemon-framework-starter-security')
//    implementation('com.cmpay:lemon-framework-starter-security-refresh') // 认证续约暂时只支持redis
//    implementation('com.cmpay:lemon-framework-starter-cloud-config')
    implementation('com.cmpay:cmpay-tracing-starter')
    implementation('mysql:mysql-connector-java')


    implementation 'com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3'
}

springBoot {
    mainClass = 'com.cmpay.hacp.HighAvailabilityControlPlatformApplication'
}

configurations.all{
    if (!project.hasProperty("cmpayDispatch")){
        exclude module: 'high-availability-dashboard-rest'
        exclude module: 'high-availability-dispatch-rest'
    }
}
